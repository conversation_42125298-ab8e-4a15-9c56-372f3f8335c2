# 规则平台差异分析请求


**项目**: rulebook-ai

**创建时间**: 2025年05月16日 07:27

**最后交互**: 2025年05月16日 09:20

**对话ID**: fd4720fd-9fb8-4b72-a5df-61cdb48589e5

---

### 对话 1

> **👤 用户** (2025年05月16日 07:28)

@memory/docs/literature/ 分析这个文件夹下面的内容，解析各个平台支持规则的差异点，以及应用原理

> **🤖 Augment** (2025年05月16日 07:28)

我将帮您分析 `memory/docs/literature/` 文件夹下的内容，解析各个平台支持规则的差异点以及应用原理。首先，让我们查看这个文件夹中有哪些文件。


现在我已经获取了 `memory/docs/literature/` 文件夹下的内容，让我分析各个平台支持规则的差异点以及应用原理。


# memory/docs/literature/ 文件夹内容分析：各平台规则支持差异与应用原理

## 一、各平台规则文件格式与存储位置

### 1. Cursor
- **规则文件格式**：`.mdc`
- **存储位置**：
  - 全局规则：Cursor设置中的"General" > "Rules for AI"
  - 项目规则：`.cursor/rules/`目录下的`.mdc`文件
- **向后兼容**：支持旧版的`.cursorrules`文件（根目录），但推荐迁移到新系统

### 2. CLINE
- **规则文件格式**：`.clinerules`
- **存储位置**：
  - 全局规则：VSCode扩展设置中的"Custom Instructions"字段
  - 项目规则：项目根目录下的`.clinerules`文件
- **目录支持**：支持从`.clinerules/`目录递归加载所有文件

### 3. RooCode
- **规则文件格式**：多种格式（`.md`、`.txt`等）
- **存储位置**：
  - 工作区范围规则：
    - 首选方法：`.roo/rules/`目录（递归读取所有文件）
    - 备用方法：根目录下的`.roorules`文件
  - 模式特定规则：
    - 首选方法：`.roo/rules-{modeSlug}/`目录
    - 备用方法：根目录下的`.roorules-{modeSlug}`文件
- **加载机制**：按文件名字母顺序加载

### 4. Windsurf
- **规则文件格式**：`.windsurfrules`和`global_rules.md`
- **存储位置**：
  - 全局规则：`global_rules.md`（适用于所有工作区）
  - 本地工作区规则：工作区中的`.windsurfrules`文件
- **限制**：
  - 每个规则文件限制为6000个字符
  - 全局规则和本地规则总计不能超过12,000个字符

### 5. GitHub Copilot
- **规则文件格式**：Markdown格式
- **存储位置**：仓库根目录下的`.github/copilot-instructions.md`
- **特殊要求**：需要在设置中启用"Enable custom instructions"选项

## 二、主要差异点

### 1. 规则文件的存储位置和组织方式
- **目录结构vs单文件**：RooCode和CLINE支持目录结构，允许更灵活的规则组织；Windsurf和GitHub Copilot主要使用单一文件
- **备用机制**：RooCode提供了备用机制（目录不存在时使用单文件）
- **嵌套级别**：RooCode的规则存储在`.roo/`子目录中，其他平台通常将规则文件放在根目录

### 2. 规则的应用范围
- **模式特定规则**：RooCode明确支持模式特定规则（`.roo/rules-{modeSlug}/`）
- **文件类型特定规则**：Cursor支持基于文件模式的规则（如`*.tsx, *.ts`）
- **全局vs项目特定**：所有平台都支持项目特定规则，但全局规则的实现方式不同

### 3. 规则加载机制
- **加载顺序**：RooCode明确按字母顺序加载规则文件
- **递归加载**：RooCode和CLINE支持从目录递归加载文件
- **合并策略**：RooCode的模式特定规则补充而非替换全局规则

### 4. 限制和约束
- **字符限制**：Windsurf明确限制规则文件为6000字符，总计12,000字符
- **优先级**：Windsurf在超出限制时有明确的优先级规则（全局规则优先）

### 5. 特殊功能
- **文件引用**：Cursor支持通过`@file`引用其他文件
- **自动记忆**：Windsurf支持自动生成的记忆功能
- **源头标记**：RooCode为每个规则文件添加源头标记
- **UI集成**：Windsurf和CLINE提供UI界面编辑规则

## 三、规则应用原理

### 1. 基本原理
所有平台都将规则作为系统提示(system prompt)的一部分，影响AI助手的行为。规则本质上是对AI模型的指导，告诉它如何理解和响应用户的请求。

### 2. 规则的作用范围
- **持久性影响**：所有平台的规则都是"常开"的，持续影响所有交互
- **上下文管理**：规则帮助AI理解项目的上下文、编码风格和最佳实践
- **行为塑造**：规则定义了AI助手的基线行为，影响其决策过程

### 3. 规则加载和处理机制
- **启动时加载**：大多数平台在会话开始时加载规则
- **动态更新**：Windsurf明确支持在对话中途更改规则
- **规则合并**：多个来源的规则（全局、项目特定、模式特定）会被合并成一个统一的指令集

### 4. 规则优化建议
- **简洁明确**：所有平台都建议保持规则简洁、具体
- **格式化**：推荐使用项目点、编号列表和Markdown格式，而非长段落
- **XML标签**：Windsurf特别推荐使用XML标签来组织相似规则
- **避免通用规则**：不需要添加通用规则（如"写好代码"），因为这些已经包含在AI的训练数据中

### 5. 规则与记忆的关系
- **Windsurf**明确区分了规则和记忆：
  - 规则是用户手动定义的
  - 记忆可以由AI自动生成
  - 记忆与创建它们的工作区相关联
- 其他平台没有明确区分这两个概念，但都支持通过规则文件提供持久性上下文

### 6. 版本控制和团队协作
- 所有平台的项目特定规则都可以通过版本控制系统（如Git）进行管理
- CLINE特别强调了`.clinerules`文件成为项目源代码一部分的好处
- Windsurf建议将`.windsurfrules`添加到`.gitignore`中，以确保规则仅应用于本地项目

### 7. 安全最佳实践
- CLINE特别强调了在`.clinerules`中指导AI忽略特定文件或模式的安全最佳实践，特别是对于包含敏感信息的文件

## 四、总结

各平台在规则支持方面有各自的特点和优势：

1. **RooCode**提供了最灵活的规则组织方式，支持目录结构、备用机制和模式特定规则
2. **Cursor**在文件类型特定规则和文件引用方面有独特优势
3. **Windsurf**在记忆管理和规则限制方面有明确的规定
4. **CLINE**在安全最佳实践和目录支持方面有详细指导
5. **GitHub Copilot**提供了最简单的实现方式

尽管实现细节不同，所有平台的规则系统都旨在实现相同的目标：提供一种机制，让用户能够定制AI助手的行为，使其更好地适应特定项目的需求和约束。


---

