# 分析规则管理文件功能与流程


**项目**: rulebook-ai

**创建时间**: 2025年05月16日 06:34

**最后交互**: 2025年05月16日 09:20

**对话ID**: bd6f2ff8-26bc-4480-a2ce-cd2897b5902f

---

### 对话 1

> **👤 用户** (2025年05月16日 06:35)

@src/manage_rules.py  帮我分析这个py 文件，梳理这个代码支持的指令是什么？每个指令的 流程是什么？预期执行后的效果是什么？目的是让我对整个命令有一个清晰的认识 

> **🤖 Augment** (2025年05月16日 06:35)

我将帮您分析`src/manage_rules.py`文件，梳理其支持的指令、流程和预期效果。首先，让我获取该文件的内容。


现在我将分析这个文件，梳理其支持的指令、流程和预期效果。

## `src/manage_rules.py` 文件分析

这个Python脚本是一个命令行工具，用于管理AI助手的规则集、项目记忆和支持工具。它支持以下几个主要指令：

### 1. `install` 指令

**流程**：
1. 接收目标仓库路径和规则集名称（默认为"light-spec"）
2. 从源目录复制规则集到目标仓库的`project_rules/`目录
3. 从源目录复制记忆启动器到目标仓库的`memory/`目录
4. 从源目录复制工具启动器到目标仓库的`tools/`目录
5. 复制`env.example`和`requirements.txt`文件到目标仓库根目录
6. 运行初始同步操作（调用`sync`命令）
7. 输出安装完成信息和后续操作建议

**预期效果**：
- 在目标仓库中建立完整的AI助手框架结构
- 创建必要的目录和文件：`project_rules/`、`memory/`、`tools/`等
- 生成各平台（Cursor、CLINE、RooCode、Windsurf、GitHub Copilot）的规则文件
- 提供后续操作指南（如更新.gitignore和提交文件）

### 2. `sync` 指令

**流程**：
1. 接收目标仓库路径
2. 从目标仓库的`project_rules/`目录读取规则
3. 删除现有的生成规则文件
4. 为不同平台生成新的规则文件：
   - Cursor规则（`.cursor/rules/`目录，添加.mdc扩展名）
   - CLINE规则（`.clinerules/`目录，移除扩展名）
   - RooCode规则（`.roo/`目录，重构目录结构）
   - Windsurf规则（`.windsurfrules`文件，连接所有规则）
   - GitHub Copilot指令（`.github/copilot-instructions.md`文件）

**预期效果**：
- 更新所有平台的规则文件，使其与`project_rules/`目录中的规则保持同步
- 保持规则在不同AI平台间的一致性

### 3. `clean-rules` 指令

**流程**：
1. 接收目标仓库路径
2. 删除与规则相关的组件：
   - `project_rules/`目录
   - `.cursor/`目录
   - `.clinerules/`目录或文件
   - `.roo/`目录
   - `.windsurfrules`文件
   - `.github/copilot-instructions.md`文件
3. 如果`.github/`目录为空，则尝试删除它

**预期效果**：
- 清除所有与规则相关的文件和目录
- 保留`memory/`和`tools/`目录不变

### 4. `clean-all` 指令

**流程**：
1. 接收目标仓库路径
2. 要求用户确认操作（输入"yes"）
3. 删除所有框架组件：
   - 所有规则相关文件和目录（同`clean-rules`）
   - `memory/`目录
   - `tools/`目录
   - `env.example`文件
   - `requirements.txt`文件

**预期效果**：
- 完全清除所有与框架相关的文件和目录
- 将仓库恢复到安装框架前的状态

### 5. `list-rules` 指令

**流程**：
1. 查找源规则集目录
2. 列出所有可用的规则集名称

**预期效果**：
- 显示用户可以安装的所有规则集选项

## 总结

`manage_rules.py`是一个管理AI助手框架的工具，主要功能包括：

1. **安装框架**：将规则集、记忆启动器和工具启动器复制到目标仓库
2. **同步规则**：将项目规则转换为各AI平台（Cursor、CLINE、RooCode、Windsurf、GitHub Copilot）可用的格式
3. **清理规则**：删除与规则相关的文件和目录
4. **完全清理**：删除所有框架组件
5. **列出规则集**：显示可用的规则集选项

这个工具的主要目的是帮助用户在项目中设置和管理AI助手的规则，确保不同AI平台使用相同的规则集，并提供一种标准化的方式来管理AI助手的配置。它通过文件复制、转换和组织，使得用户可以在不同的AI平台上保持一致的AI助手行为。


---

### 对话 2

> **👤 用户** (2025年05月16日 06:38)

同步规则 是如何实现的 ？

> **🤖 Augment** (2025年05月16日 06:38)

我将详细分析`manage_rules.py`中的同步规则（sync）功能是如何实现的。让我们深入了解这个过程。

## 同步规则（sync）的实现分析

同步规则功能主要由`handle_sync`函数实现（第292-339行）。这个功能的核心目的是将项目规则目录（`project_rules/`）中的规则转换为各个AI平台可识别的格式，并放置在相应的位置。

### 1. 同步流程概述

```python
def handle_sync(args):
    target_repo_path = os.path.abspath(args.target_repo_path)
    project_rules_dir_in_target = os.path.join(target_repo_path, TARGET_PROJECT_RULES_DIR)
    
    # 检查规则源目录是否存在
    if not os.path.isdir(project_rules_dir_in_target):
        print(f"Error: Rule source directory '{project_rules_dir_in_target}' not found.")
        return 1
        
    # 定义各平台规则的目标路径
    # 删除现有的生成规则
    # 生成新的规则文件
```

### 2. 目标路径定义

同步过程首先定义了各个AI平台规则文件的路径：

```python
# 定义各平台规则的目标路径
cursor_dir = os.path.join(target_repo_path, ".cursor", "rules")
cursor_parent_dir = os.path.dirname(cursor_dir)
cline_dir = os.path.join(target_repo_path, ".clinerules")
roo_rules_dir = os.path.join(target_repo_path, ".roo")
windsurf_file_path = os.path.join(target_repo_path, ".windsurfrules")
gh_copilot_instructions_path = os.path.join(target_repo_path, TARGET_GITHUB_COPILOT_DIR, TARGET_COPILOT_INSTRUCTIONS_FILE)
gh_copilot_parent_dir = os.path.dirname(gh_copilot_instructions_path)
```

### 3. 创建必要的目录

```python
os.makedirs(cursor_parent_dir, exist_ok=True)
os.makedirs(gh_copilot_parent_dir, exist_ok=True) # 确保.github目录存在
```

### 4. 删除现有的生成规则

在生成新规则前，先删除现有的规则文件/目录，确保没有旧规则残留：

```python
if os.path.exists(cursor_dir): shutil.rmtree(cursor_dir)
if os.path.exists(cline_dir):
    if os.path.isfile(cline_dir): os.remove(cline_dir)
    elif os.path.isdir(cline_dir): shutil.rmtree(cline_dir)
if os.path.exists(roo_rules_dir): shutil.rmtree(roo_rules_dir)
if os.path.isfile(windsurf_file_path): os.remove(windsurf_file_path)
if os.path.isfile(gh_copilot_instructions_path): os.remove(gh_copilot_instructions_path)
```

### 5. 为各平台生成新规则

这是同步过程的核心部分，针对不同平台使用不同的处理方法：

#### 5.1 Cursor规则生成

```python
copy_and_number_files(project_rules_dir_in_target, cursor_dir, extension_mode='add_mdc')
```

`copy_and_number_files`函数（第84-110行）的工作方式：
- 获取源目录中的所有文件并排序
- 为每个文件分配一个序号（从现有文件数+1开始）
- 根据`extension_mode`参数处理文件扩展名：
  - `'add_mdc'`：添加`.mdc`扩展名（Cursor使用）
  - `'remove'`：移除扩展名（CLINE使用）
  - `'keep'`：保持原扩展名
- 复制文件到目标目录，使用新的编号前缀命名

#### 5.2 CLINE规则生成

```python
copy_and_number_files(project_rules_dir_in_target, cline_dir, extension_mode='remove')
```

与Cursor规则类似，但移除了文件扩展名。

#### 5.3 RooCode规则生成

```python
copy_and_restructure_roocode(project_rules_dir_in_target, roo_rules_dir)
```

`copy_and_restructure_roocode`函数（第112-153行）的工作方式：
- 复制整个源目录到目标目录
- 处理目录名：移除数字前缀（如"01-"）
- 处理文件名：移除扩展名
- 这种处理适应了RooCode的特定要求

#### 5.4 Windsurf规则生成

```python
concatenate_ordered_files(project_rules_dir_in_target, windsurf_file_path)
```

`concatenate_ordered_files`函数（第155-182行）的工作方式：
- 获取源目录中的所有文件并排序
- 将所有文件内容连接成一个文件
- 在每个文件内容之间添加分隔符
- 这种处理适应了Windsurf使用单一规则文件的要求

#### 5.5 GitHub Copilot指令生成

```python
concatenate_ordered_files(project_rules_dir_in_target, gh_copilot_instructions_path)
```

与Windsurf规则生成类似，将所有规则连接成一个Markdown文件，供GitHub Copilot使用。

### 6. 辅助函数详解

#### 6.1 `get_ordered_source_files`函数

```python
def get_ordered_source_files(source_dir_path):
    # 获取目录中的所有文件，排除隐藏文件，并按字母顺序排序
```

这个函数递归遍历源目录，收集所有非隐藏文件，并按字母顺序排序。这确保了规则文件的处理顺序是一致的。

#### 6.2 `copy_file`函数

```python
def copy_file(source, destination):
    # 创建目标目录（如果不存在），然后复制文件
```

这个函数负责安全地复制单个文件，确保目标目录存在。

## 同步规则的关键特点

1. **格式转换**：根据不同AI平台的要求，对规则文件进行不同的格式转换
   - Cursor：添加`.mdc`扩展名
   - CLINE：移除扩展名
   - RooCode：重构目录结构，移除前缀和扩展名
   - Windsurf和GitHub Copilot：合并为单一文件

2. **文件排序**：确保规则按照一致的顺序处理，通常是按字母顺序

3. **文件编号**：为Cursor和CLINE规则添加数字前缀（如"01-"），确保规则按特定顺序加载

4. **非破坏性**：在生成新规则前，先删除旧规则，确保没有过时的规则残留

5. **目录结构**：自动创建必要的目录结构，确保规则文件放置在正确的位置

## 同步规则的预期效果

当执行`python manage_rules.py sync <target_repo_path>`命令时：

1. 系统会读取`<target_repo_path>/project_rules/`目录中的规则文件
2. 为每个AI平台生成适当格式的规则文件：
   - Cursor：`.cursor/rules/`目录中的`.mdc`文件
   - CLINE：`.clinerules/`目录中的无扩展名文件
   - RooCode：`.roo/`目录中的重构文件和目录
   - Windsurf：`.windsurfrules`单一文件
   - GitHub Copilot：`.github/copilot-instructions.md`单一文件
3. 所有平台的规则内容保持一致，只是格式和组织方式不同

这种同步机制确保了在不同AI平台上使用相同的规则集，提高了AI助手行为的一致性和可预测性。


---

### 对话 3

> **👤 用户** (2025年05月16日 06:40)

给出所有的 命令操作的测试case

> **🤖 Augment** (2025年05月16日 06:40)

我将为您提供`src/manage_rules.py`中所有命令操作的测试用例，特别关注`copy_and_number_files`函数的使用场景。

## `manage_rules.py` 命令操作测试用例

### 1. `install` 命令测试用例

```bash
# 基本安装 - 使用默认规则集(light-spec)
python src/manage_rules.py install /path/to/your/project

# 指定规则集安装
python src/manage_rules.py install /path/to/your/project --rule-set advanced-spec

# 安装到当前目录
python src/manage_rules.py install .

# 安装到相对路径
python src/manage_rules.py install ../my-other-project
```

**预期结果**：
- 在目标路径创建`project_rules/`、`memory/`和`tools/`目录
- 复制规则集、记忆启动器和工具启动器
- 复制`env.example`和`requirements.txt`文件
- 自动执行`sync`命令生成各平台规则文件
- 显示安装完成信息和后续操作建议

### 2. `sync` 命令测试用例

```bash
# 同步规则到当前项目
python src/manage_rules.py sync .

# 同步规则到指定项目
python src/manage_rules.py sync /path/to/your/project

# 在修改project_rules/目录内容后同步
# 1. 先编辑规则文件
# 2. 然后运行同步命令
python src/manage_rules.py sync .
```

**预期结果**：
- 读取`project_rules/`目录中的规则
- 生成Cursor规则（`.cursor/rules/`目录，添加.mdc扩展名）
- 生成CLINE规则（`.clinerules/`目录，移除扩展名）
- 生成RooCode规则（`.roo/`目录，重构目录结构）
- 生成Windsurf规则（`.windsurfrules`文件）
- 生成GitHub Copilot指令（`.github/copilot-instructions.md`文件）

### 3. `clean-rules` 命令测试用例

```bash
# 清理当前项目的规则
python src/manage_rules.py clean-rules .

# 清理指定项目的规则
python src/manage_rules.py clean-rules /path/to/your/project
```

**预期结果**：
- 删除`project_rules/`目录
- 删除`.cursor/`目录
- 删除`.clinerules/`目录或文件
- 删除`.roo/`目录
- 删除`.windsurfrules`文件
- 删除`.github/copilot-instructions.md`文件
- 如果`.github/`目录为空，则删除它
- 保留`memory/`和`tools/`目录不变

### 4. `clean-all` 命令测试用例

```bash
# 清理当前项目的所有框架组件
python src/manage_rules.py clean-all .

# 清理指定项目的所有框架组件
python src/manage_rules.py clean-all /path/to/your/project
```

**预期结果**：
- 要求用户确认操作（输入"yes"）
- 删除所有规则相关文件和目录（同`clean-rules`）
- 删除`memory/`目录
- 删除`tools/`目录
- 删除`env.example`文件
- 删除`requirements.txt`文件

### 5. `list-rules` 命令测试用例

```bash
# 列出所有可用的规则集
python src/manage_rules.py list-rules
```

**预期结果**：
- 显示源规则集目录中所有可用的规则集名称

## `copy_and_number_files` 函数测试用例

这个函数在`sync`命令中被用于生成Cursor和CLINE规则。以下是针对这个函数的具体测试用例：

### 测试用例1：保持扩展名（默认模式）

```python
# 测试代码
copy_and_number_files('/path/to/source_dir', '/path/to/dest_dir')
```

**预期结果**：
- 源目录中的文件被复制到目标目录
- 文件名添加数字前缀（如"01-"、"02-"等）
- 保持原始文件扩展名不变

### 测试用例2：添加.mdc扩展名（Cursor模式）

```python
# 测试代码
copy_and_number_files('/path/to/source_dir', '/path/to/dest_dir', extension_mode='add_mdc')
```

**预期结果**：
- 源目录中的文件被复制到目标目录
- 文件名添加数字前缀
- 文件扩展名被替换为`.mdc`
- 例如：`intro.md` → `01-intro.mdc`

### 测试用例3：移除扩展名（CLINE模式）

```python
# 测试代码
copy_and_number_files('/path/to/source_dir', '/path/to/dest_dir', extension_mode='remove')
```

**预期结果**：
- 源目录中的文件被复制到目标目录
- 文件名添加数字前缀
- 文件扩展名被移除
- 例如：`intro.md` → `01-intro`

### 测试用例4：处理已有编号的文件

```python
# 测试场景：目标目录已有编号文件
# 1. 创建目标目录并添加一些编号文件
os.makedirs('/path/to/dest_dir', exist_ok=True)
with open('/path/to/dest_dir/01-existing.mdc', 'w') as f:
    f.write('existing content')
with open('/path/to/dest_dir/02-another.mdc', 'w') as f:
    f.write('more content')

# 2. 运行函数
copy_and_number_files('/path/to/source_dir', '/path/to/dest_dir', extension_mode='add_mdc')
```

**预期结果**：
- 源目录中的文件被复制到目标目录
- 新文件的编号从3开始（03-、04-等）
- 现有文件保持不变

### 测试用例5：处理空源目录

```python
# 测试代码
os.makedirs('/path/to/empty_source', exist_ok=True)
copy_and_number_files('/path/to/empty_source', '/path/to/dest_dir')
```

**预期结果**：
- 显示信息："Info: No source files found in '/path/to/empty_source' to process for numbering."
- 不创建任何文件

### 测试用例6：处理不存在的源目录

```python
# 测试代码
copy_and_number_files('/path/to/nonexistent_dir', '/path/to/dest_dir')
```

**预期结果**：
- 显示错误："Error: Source directory '/path/to/nonexistent_dir' not found or is not a directory."
- 不创建任何文件

## 完整的端到端测试流程

以下是一个完整的端到端测试流程，展示了如何使用`manage_rules.py`来管理AI助手框架：

```bash
# 1. 列出可用的规则集
python src/manage_rules.py list-rules

# 2. 安装框架到测试项目
python src/manage_rules.py install ./test_project --rule-set light-spec

# 3. 修改规则（例如，编辑test_project/project_rules/中的文件）

# 4. 同步规则
python src/manage_rules.py sync ./test_project

# 5. 验证生成的规则文件
ls -la ./test_project/.cursor/rules/
ls -la ./test_project/.clinerules/
ls -la ./test_project/.roo/
cat ./test_project/.windsurfrules
cat ./test_project/.github/copilot-instructions.md

# 6. 清理规则
python src/manage_rules.py clean-rules ./test_project

# 7. 验证规则已被清理
ls -la ./test_project/

# 8. 重新安装框架
python src/manage_rules.py install ./test_project

# 9. 完全清理
python src/manage_rules.py clean-all ./test_project
# 输入"yes"确认

# 10. 验证所有框架组件已被清理
ls -la ./test_project/
```

这些测试用例涵盖了`manage_rules.py`中所有命令的基本功能和边缘情况，特别是`copy_and_number_files`函数在不同模式下的行为。通过这些测试，您可以全面了解这个脚本的功能和预期行为。


---

### 对话 4

> **👤 用户** (2025年05月16日 06:42)

思考这个命令还可以扩展的点是什么？

> **🤖 Augment** (2025年05月16日 06:43)

# `manage_rules.py` 可扩展点分析

经过对`manage_rules.py`脚本的深入分析，我发现该工具有多个可以扩展的方向，以增强其功能和用户体验。以下是几个值得考虑的扩展点：

## 1. 规则模板自定义和创建

**当前限制**：目前脚本只支持使用预定义的规则集。

**扩展建议**：
- 添加`create-rule-set`命令，允许用户基于现有规则集创建新的规则集
- 提供规则模板编辑器，帮助用户从头开始创建规则
- 支持规则模板的导入和导出，便于用户之间共享规则集
- 添加规则模板库，收集和分享社区创建的高质量规则集

## 2. 规则验证和测试

**当前限制**：缺乏验证规则有效性的机制。

**扩展建议**：
- 添加`validate`命令，检查规则文件的格式、语法和内容
- 提供详细的错误报告和修复建议
- 实现规则测试功能，模拟不同AI平台对规则的解释
- 添加规则冲突检测，识别可能导致AI行为不一致的规则

## 3. 规则版本控制和回滚

**当前限制**：没有内置的规则版本管理功能。

**扩展建议**：
- 添加`save-version`命令，保存当前规则集的快照
- 实现`rollback`命令，允许回滚到之前保存的版本
- 提供版本比较功能，显示不同版本之间的差异
- 支持版本标记和注释，便于记录版本变更的原因和内容

## 4. 支持更多AI平台

**当前限制**：固定支持几个特定平台。

**扩展建议**：
- 设计插件架构，使添加新平台支持变得简单
- 创建标准化的平台适配器接口
- 支持自定义转换规则，适应不同平台的特殊要求
- 提供平台特定配置选项，优化每个平台的规则生成

## 5. 规则性能分析和优化

**当前限制**：缺乏分析规则性能和效果的工具。

**扩展建议**：
- 添加`analyze`命令，评估规则的复杂性和潜在影响
- 提供规则优化建议，提高AI响应的质量和效率
- 实现规则使用情况跟踪，了解哪些规则最常被触发
- 支持A/B测试，比较不同规则集的效果

## 6. 用户界面和可视化

**当前限制**：仅提供命令行界面。

**扩展建议**：
- 开发图形用户界面(GUI)或Web界面
- 提供规则可视化编辑器，使规则创建和编辑更直观
- 实现规则效果预览功能
- 添加仪表板，显示规则使用情况和性能指标

## 7. 增强的文档和教程

**当前限制**：文档可能不够全面。

**扩展建议**：
- 生成详细的规则文档，解释每个规则的目的和效果
- 提供最佳实践指南和示例
- 创建交互式教程，帮助新用户快速上手
- 支持规则注释，便于团队协作和知识共享

## 8. 自动化和集成

**当前限制**：需要手动运行同步命令。

**扩展建议**：
- 添加自动同步功能，监控规则变更并自动更新
- 提供CI/CD集成，将规则管理纳入开发工作流
- 支持与版本控制系统(如Git)的深度集成
- 实现规则变更通知机制，确保团队成员了解最新规则

这些扩展点将使`manage_rules.py`脚本更加强大、灵活和用户友好，满足更广泛的用户需求，并适应AI技术的快速发展。


---

### 对话 5

> **👤 用户** (2025年05月16日 06:49)

@rule_sets/  分析这个文件夹下面的 各个模式的差异是什么？共性是什么？

> **🤖 Augment** (2025年05月16日 06:49)

我将分析`rule_sets/`文件夹下的各个模式，找出它们的差异和共性。首先，让我查看这个文件夹的内容。


让我尝试使用codebase-retrieval工具来获取关于rule_sets目录的信息。


让我继续获取更多关于rule_sets目录的信息，特别是各个规则集的内容。


现在我将查看每个规则集中的文件结构，以便更好地理解它们的组织方式。


现在我将查看各个规则集中的关键文件，以便比较它们的差异和共性。首先，让我查看各个规则集的`06-rules_v1.md`文件，这是包含一般原则和最佳实践的文件。


现在让我查看一下各个规则集中的计划模式文件，以便比较它们的差异。


基于以上查看的文件，我现在可以分析`rule_sets/`文件夹下各个模式的差异和共性。

# `rule_sets/`文件夹下各模式的分析

## 文件结构共性

所有规则集都遵循相似的文件结构组织：

1. **基本目录结构**：
   - `01-rules/`：包含基础规则和元规则
   - `02-rules-architect/`：包含规划模式的规则
   - `03-rules-code/`：包含实现模式的规则
   - `04-rules-debug/`：包含调试模式的规则

2. **核心文件**：
   - `00-meta-rules.md`：定义操作焦点和跨助手模式逻辑
   - `01-memory.md`：定义记忆文件结构
   - `02-error-documentation.md`：错误文档规则
   - `03-lessons-learned.md`：经验教训规则
   - `04-archiecture-understanding.md`：架构理解规则
   - `05-directory-structure.md`：目录结构规则
   - `06-rules_v1.md`：一般原则和最佳实践

3. **模式特定文件**：
   - `01-plan_v1.md`：规划模式工作流
   - `01-code_v1.md`：实现模式工作流
   - `01-debug_v1.md`：调试模式工作流

## 各模式的差异

### 1. 详细程度和规范性

1. **heavy-spec（重规范）**：
   - 非常详细的规则，行数最多（`06-rules_v1.md`有150行，`01-plan_v1.md`有53行）
   - 高度规范化，提供明确的步骤和检查点
   - 详细的工作流程，每个步骤都有具体的指导
   - 强制性的记忆库检查和验证
   - 适用于：大型复杂项目、需要严格规范的团队、较弱的AI模型

2. **medium-spec（中规范）**：
   - 中等详细程度（`06-rules_v1.md`有111行，`01-plan_v1.md`有34行）
   - 平衡了详细步骤和原则指导
   - 合并了一些步骤，减少冗余
   - 保留了关键的记忆库检查点
   - 适用于：中等复杂度的项目、熟悉AI但仍需明确结构的团队

3. **light-spec（轻规范）**：
   - 最简洁的规则（`06-rules_v1.md`有101行，`01-plan_v1.md`只有18行）
   - 主要基于原则，而非详细步骤
   - 假设AI有较高的解释能力
   - 强调核心原则和工作流程结果
   - 适用于：经验丰富的AI协作团队、使用高能力AI模型、需要灵活性和速度的项目

4. **no_memory_interation_rules**：
   - 特殊规则集，似乎是为不需要记忆交互的场景设计
   - 文件结构与其他规则集相同，但可能减少了记忆库交互的要求

### 2. 记忆库交互

1. **heavy-spec**：
   - 在多个点强制进行详细的记忆库检查
   - 明确要求引用记忆库文件中的关键发现
   - 详细的记忆影响评估

2. **medium-spec**：
   - 初始扫描加上持续一致性原则
   - 保留了关键的记忆库检查，但减少了强制性

3. **light-spec**：
   - 初始扫描加上强烈的持续一致性原则
   - 更加简化的记忆库交互，依赖AI的理解能力

### 3. 工作流程详细程度

以规划工作流为例：

1. **heavy-spec**：
   - 5个主要步骤，每个步骤都有多个详细子步骤
   - 明确的强制性记忆库咨询
   - 详细的解决方案评估和实施计划

2. **medium-spec**：
   - 4个主要步骤，子步骤较少
   - 保留了关键的记忆库参考，但表述更简洁
   - 合并了一些评估和规划步骤

3. **light-spec**：
   - 4个简洁的主要步骤，几乎没有子步骤
   - 高度概括的工作流程，依赖AI的理解能力
   - 保留了核心原则，但移除了详细指导

## 共性

1. **核心原则**：
   - 所有规则集都强调清晰性、结构化响应和主动建议
   - 都要求理解任务和收集相关上下文
   - 都强调软件工程基础原则（可读性、一致性、健壮性、安全性等）
   - 都包含相同的工具部分（截图验证、LLM、Web浏览器、搜索引擎）

2. **记忆文件结构**：
   - 所有规则集都定义了相同的记忆文件层次结构
   - 核心文件包括：`product_requirement_docs.md`、`technical.md`、`architecture.md`、`tasks_plan.md`、`active_context.md`等
   - 可选的上下文文件：`/docs/literature/`和`/tasks/rfc/`

3. **模式确定逻辑**：
   - 所有规则集都保留了详细的模式确定逻辑（Plan/Act/Debug）
   - 这确保了在不同AI助手平台上的广泛适用性

4. **目录结构**：
   - 所有规则集都定义了相同的项目目录结构
   - 包括`/memory/docs/`、`/memory/tasks/`、`.cursor/rules/`、`.clinerules/`、`.roo/`等

## 总结

`rule_sets/`文件夹下的各个模式主要在详细程度、规范性和灵活性上有所不同，但共享相同的核心原则、文件结构和基本工作流程。

- **heavy-spec**：最详细、最规范，适合复杂项目和需要严格指导的团队
- **medium-spec**：平衡详细度和原则指导，适合中等复杂度的项目
- **light-spec**：最简洁、最灵活，适合经验丰富的团队和高能力AI模型
- **no_memory_interation_rules**：特殊规则集，可能适用于不需要记忆交互的场景

选择哪个规则集取决于项目复杂度、团队经验、AI助手能力和对严格性与灵活性的需求平衡。默认规则集是`light-spec`，这表明该框架倾向于简洁和灵活性，但为不同需求提供了更详细的选项。


---

### 对话 6

> **👤 用户** (2025年05月16日 06:53)

找出实际的差异点，并且解释理由

> **🤖 Augment** (2025年05月16日 06:54)

# `rule_sets/`文件夹下各规则集的实际差异点分析

通过深入比较`heavy-spec`、`medium-spec`和`light-spec`三个规则集的核心文件，我发现了以下关键差异点及其背后的设计理由：

## 1. 指导哲学与语言风格差异

**具体差异**：
- **heavy-spec**：使用强制性语言，如"ALWAYS"、"MUST"、"Mandatory"、"Explicitly verify"
- **medium-spec**：使用指导性语言，如"Deeply consider"、"Analyze"、"Provide"
- **light-spec**：使用结果导向语言，如"Achieve full clarity"、"Create a step-by-step plan"

**设计理由**：
这反映了不同的指导哲学 - heavy-spec是规范性的（告诉你必须做什么），medium-spec是指导性的（告诉你应该做什么），light-spec是目标导向的（告诉你需要达到什么结果）。这种差异适应了不同团队的工作风格和AI模型的能力水平。

## 2. 记忆库交互方式

**具体差异**：
- **heavy-spec**：明确规定"Mandatory Memory Consult"，指定具体文件，要求"Reference key findings"
- **medium-spec**：保留记忆库查阅要求，但使用"Deeply consider relevant constraints"等较柔和表述
- **light-spec**：仅提及"leverage relevant project context"，不指定具体文件或查阅方法

**设计理由**：
这种差异反映了对AI自主性的不同假设。heavy-spec假设AI需要明确指导才能正确使用记忆库，适合初级团队或能力有限的AI；light-spec假设AI能够自主判断何时以及如何使用记忆库，适合高级团队或高能力AI模型。

## 3. 信息收集结构

**具体差异**：
- **heavy-spec**：详细的三层结构（Task Definition → Core Memory Bank Files → Existing Codebase），每层都有具体指导
- **medium-spec**：保留三层结构，但简化描述
- **light-spec**：将整个过程简化为"Context is Key"原则，只有两个简短子点

**设计理由**：
heavy-spec注重过程（"如何做"），适合需要明确步骤的团队；light-spec注重结果（"做什么"），适合已熟悉流程的团队。这种差异使规则集能适应不同经验水平的团队和AI模型。

## 4. 规划工作流程的复杂度

**具体差异**：
- **heavy-spec**：5个详细步骤，每步都有多个子步骤和明确指导，如"Deep Dive into Requirements"下有多个强制性检查点
- **medium-spec**：4个步骤，减少强制性语言，简化子步骤
- **light-spec**：4个高度简化的步骤，几乎没有子步骤，如"Understand & Contextualize"只有一个简短句子

**设计理由**：
这反映了不同的指导深度 - heavy-spec提供详细"食谱"，适合需要具体指导的场景；medium-spec提供"烹饪技巧"，平衡指导和灵活性；light-spec只提供"菜单"，适合有经验的团队或高能力AI。这种差异使规则集能适应不同复杂度的项目需求。

## 5. 软件工程原则的详细程度

**具体差异**：
- **heavy-spec**：详细分解每个原则，如"Security"下列出四个具体措施（Assume Untrusted Input, Sanitize/Escape等）
- **medium-spec**：保留原则类别，但简化描述
- **light-spec**：每个原则只有一个简短句子，如"Security"原则被压缩为一行

**设计理由**：
heavy-spec适合需要详细软件工程指导的团队；light-spec假设团队或AI已熟悉这些原则，只需简短提醒。这种差异使规则集能适应不同软件工程经验水平的团队。

## 6. 验证和评估要求

**具体差异**：
- **heavy-spec**：专门的"Memory Impact Assessment"步骤，要求详细评估计划对记忆库的影响
- **medium-spec**："Assess Impact & Request Validation"步骤，但要求较简化
- **light-spec**：简化为"Assess & Validate"，只简单提到"Note potential Memory Bank impacts"

**设计理由**：
这反映了对验证严格性的不同要求。heavy-spec强调全面验证，适合关键系统；light-spec简化验证，适合快速迭代场景。这种差异使规则集能适应不同风险容忍度的项目。

## 7. 工具部分的一致性

**具体差异**：
- 所有规则集中的工具部分（Screenshot Verification、LLM、Web browser、Search engine）几乎完全相同

**设计理由**：
工具使用是技术性的，需要精确指令，不适合简化或详细化。这种一致性确保了无论使用哪个规则集，工具的使用方式都是标准化的，减少了学习成本。

## 8. no_memory_interation_rules的特殊性

**具体差异**：
- 这个规则集可能减少或移除了记忆库交互的要求，但保持了相同的文件结构

**设计理由**：
可能设计用于不需要或不支持记忆库功能的场景，或者是为了简化初始设置。这提供了一个不依赖记忆库的选项，增加了框架的灵活性。

## 总结

这些差异点反映了规则集设计的核心理念：提供不同详细程度和规范性的选项，以适应不同团队经验、项目复杂度和AI能力水平的需求。从heavy-spec到light-spec的演进显示了一个从"详细指导"到"原则指导"的转变，使框架能够在各种场景中灵活应用。


---

