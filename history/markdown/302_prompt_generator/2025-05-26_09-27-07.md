# 2025-05-26_09-27-07

**项目**: 302_prompt_generator

**创建时间**: 2025年05月26日 09:27

**最后交互**: 2025年05月26日 09:44

**对话ID**: 658a7dd9-8220-449d-bd07-7ab4c338039f

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月26日 09:27)

@src/lib/prompt.ts 阅读这个文件，主要是用来生成提示词的？请为我梳理有多少种提示词模板？每种的作用是什么？

> **🤖 Augment** (2025年05月26日 09:27)

我来帮您阅读并分析这个文件，了解其中的提示词模板。


根据对 `src/lib/prompt.ts` 文件的分析，我可以为您梳理出这个文件中包含的提示词模板。这个文件主要用于生成各种类型的提示词，包含了多种不同的提示词框架和模板。

## 提示词模板总览

该文件包含 **13种** 不同的提示词模板，每种都有其特定的用途：

### 1. **CO-STAR** 框架
- **作用**：基于CO-STAR框架（上下文、目标、身份、语气、受众、结果）来分析和拆解任务，生成结构化的提示词
- **特点**：将任务分解为6个部分，确保AI能够完全理解并准确执行任务

### 2. **CRISPE** 框架  
- **作用**：基于CRISPE提示框架（Capacity、Role、Insight、Statement、Personality、Experiment）优化常规提示词
- **特点**：包含角色定义、技能描述、目标设定、约束条件等完整结构

### 3. **DRAW** 绘画提示词优化
- **作用**：专门用于优化绘画相关的提示词，将画面描述拆解为镜头、光线、主体、背景、风格和氛围六个要素
- **特点**：生成适合AI绘画工具使用的高质量提示词

### 4. **MicrOptimization** 微优化
- **作用**：对现有提示词进行深度优化，增加复杂度和具体性
- **特点**：在原有基础上增加30-50个字的新内容，提升提示词效果

### 5. **Q*** 算法提示词
- **作用**：基于Q*算法设计个性化学习路径，适用于教育场景
- **特点**：包含系统指令、变量定义、A*搜索、Q值估计等复杂结构

### 6. **RISE** 递归内省算法
- **作用**：通过多轮交互持续改进回答质量，具备自我学习和改进能力
- **特点**：包含初始回应、自我分析、改进策略、迭代优化等步骤

### 7. **VARI** 变分规划
- **作用**：将任务转化为变分规划的提示词，适用于内容生成任务
- **特点**：定义状态空间、动作空间、奖励函数等数学化结构

### 8. **CoT** 思维链
- **作用**：基于思维链推理方法，适用于需要逐步推理的复杂任务
- **特点**：包含医疗诊断、金融风险评估、创意写作等多个示例

### 9. **Meta Prompting** 元提示
- **作用**：优化提示词的结构和语法，提高token使用效率
- **特点**：专注于保持核心目标的同时简化和优化提示词结构

### 10. **Custom** 自定义
- **作用**：允许用户输入自定义的提示词模板
- **特点**：支持{input}占位符替换，灵活性最高

### 11. **O1-STYLE** O1风格推理
- **作用**：模拟O1模型的推理风格，包含思考、步骤、反思、奖励评分等元素
- **特点**：强调逐步思考、自我反思和质量评估

### 12. **OpenAI** 官方风格
- **作用**：按照OpenAI官方推荐的提示词结构来优化任务描述
- **特点**：包含任务理解、推理顺序、示例、格式要求等标准化结构

### 13. **Claude** Anthropic风格
- **作用**：基于Anthropic Claude的提示词编写风格，适用于复杂任务指导
- **特点**：详细的任务说明、输入变量定义、结构化指导等

## 核心功能

这个文件的主要功能是：
1. **提示词模板管理**：集中管理多种不同类型的提示词框架
2. **动态内容替换**：支持将用户任务动态插入到模板中
3. **自定义支持**：允许用户使用自定义模板
4. **多场景覆盖**：涵盖教育、创作、分析、优化等多个应用场景

每种模板都针对特定的使用场景和需求，用户可以根据具体任务选择最适合的提示词框架来生成高质量的AI指令。


---

### 对话 3

> **👤 用户** (2025年05月26日 09:42)

分析每个提示词模板的使用场景

> **🤖 Augment** (2025年05月26日 09:44)

基于对代码的深入分析，我为您详细梳理每个提示词模板的使用场景：

## 📋 通用任务分解类

### 1. **CO-STAR** 框架
**最佳使用场景：**
- 复杂的多维度任务需要全面规划
- 需要考虑受众、目标、身份等多个因素的项目
- 商业策划、营销方案、产品设计等

**适用时机：**
- 任务目标不够明确时
- 需要系统性思考的复杂项目
- 多利益相关方的任务

**实际应用示例：**
- 制定社交媒体营销策略
- 设计用户体验流程
- 撰写商业计划书

### 2. **CRISPE** 框架
**最佳使用场景：**
- 需要AI扮演特定专业角色
- 技能导向的任务执行
- 需要明确工作流程的项目

**适用时机：**
- 需要专业知识输出
- 角色扮演类任务
- 需要标准化流程的工作

**实际应用示例：**
- AI客服助手设计
- 专业咨询服务
- 教学辅导系统

### 3. **OpenAI** 官方风格
**最佳使用场景：**
- 需要标准化、规范化的提示词
- 企业级应用开发
- 需要高度可靠性的任务

**适用时机：**
- 生产环境部署
- 需要一致性输出的场景
- 团队协作项目

### 4. **Claude** Anthropic风格
**最佳使用场景：**
- 复杂的指令设计
- 需要详细示例的任务
- 高精度要求的应用

**适用时机：**
- 精细化控制需求
- 多步骤复杂任务
- 需要错误处理的场景

## 🧠 推理分析类

### 5. **CoT** 思维链
**最佳使用场景：**
- 需要逐步推理的复杂问题
- 医疗诊断、金融分析等专业领域
- 需要展示推理过程的任务

**适用时机：**
- 问题解决需要多个步骤
- 需要可解释的AI决策
- 专业判断类任务

**实际应用示例：**
- 医疗症状分析
- 投资风险评估
- 法律案例分析

### 6. **O1-STYLE** 高级推理
**最佳使用场景：**
- 需要深度思考的复杂问题
- 科研、学术分析
- 需要自我反思和质量评估的任务

**适用时机：**
- 高难度问题求解
- 需要多角度分析
- 质量要求极高的场景

**实际应用示例：**
- 科学研究问题
- 复杂数学证明
- 战略决策分析

### 7. **RISE** 递归改进
**最佳使用场景：**
- 需要多轮优化的任务
- 创作类工作的迭代改进
- 需要持续学习的场景

**适用时机：**
- 初始方案不够完善
- 需要反复打磨的内容
- 追求完美的项目

**实际应用示例：**
- 文章写作优化
- 产品设计迭代
- 学习计划调整

## 🎨 创作生成类

### 8. **DRAW** 绘画优化
**最佳使用场景：**
- AI绘画工具使用
- 视觉创作项目
- 艺术设计需求

**适用时机：**
- 使用Midjourney、DALL-E等工具
- 需要精确控制画面元素
- 商业设计项目

**实际应用示例：**
- 产品宣传图设计
- 插画创作
- 概念艺术设计

### 9. **VARI** 变分规划
**最佳使用场景：**
- 内容生成任务
- 需要数学化建模的创作
- 大规模内容生产

**适用时机：**
- 自动化内容生成
- 需要优化生成策略
- 批量创作需求

**实际应用示例：**
- 自动文章生成
- 个性化推荐内容
- 营销文案批量生产

## 🔧 优化改进类

### 10. **Meta Prompting** 元优化
**最佳使用场景：**
- 现有提示词效果不佳
- 需要提高token使用效率
- 提示词工程优化

**适用时机：**
- 提示词过于冗长
- 输出质量不稳定
- 成本控制需求

**实际应用示例：**
- 企业AI应用优化
- 提示词库管理
- 性能调优项目

### 11. **MicrOptimization** 微调优化
**最佳使用场景：**
- 现有提示词基础较好但需要提升
- 细节优化需求
- 渐进式改进

**适用时机：**
- 提示词已有基础版本
- 需要增加复杂度
- 精细化调整需求

## 🎯 专业算法类

### 12. **Q*** 学习路径规划
**最佳使用场景：**
- 教育培训领域
- 个性化学习方案
- 智能推荐系统

**适用时机：**
- 需要长期规划
- 个性化需求强烈
- 效果可量化的场景

**实际应用示例：**
- 在线教育平台
- 职业发展规划
- 技能提升路径

## 🛠️ 灵活定制类

### 13. **Custom** 自定义
**最佳使用场景：**
- 特殊需求无法用标准模板满足
- 行业特定的应用场景
- 实验性项目

**适用时机：**
- 标准模板都不适用
- 需要完全自主控制
- 创新性尝试

## 📊 选择建议矩阵

| 任务复杂度 | 推理需求 | 创作需求 | 优化需求 | 推荐模板 |
|------------|----------|----------|----------|----------|
| 低 | 低 | 低 | 无 | OpenAI, Custom |
| 中 | 中 | 低 | 无 | CO-STAR, CRISPE |
| 高 | 高 | 低 | 无 | CoT, O1-STYLE |
| 中 | 低 | 高 | 无 | DRAW, VARI |
| 任意 | 任意 | 任意 | 有 | Meta Prompting, MicrOptimization |
| 高 | 高 | 中 | 有 | RISE, Q* |

选择提示词模板时，建议根据具体任务的复杂度、推理要求、创作需求和优化需求来综合考虑，选择最匹配的模板类型。


---

