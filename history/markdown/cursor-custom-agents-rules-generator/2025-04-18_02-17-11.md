# 2025-04-18_02-17-11

**项目**: cursor-custom-agents-rules-generator

**创建时间**: 2025年04月18日 02:17

**最后交互**: 2025年04月18日 05:38

**对话ID**: 49352275-e510-4f1e-b7bb-db679e5efc74

---

### 对话 1

---

### 对话 2

> **👤 用户**

@.cursor/modes.json 分析这个json文件，帮我详细总结每一个角色的 自定义提示词

> **🤖 Augment**

我将帮您分析`.cursor/modes.json`文件，并详细总结每个角色的自定义提示词。首先，让我查看这个文件的内容。


让我查看一下这些角色所需遵循的工作流规则文件，以便更全面地理解每个角色的职责和工作方式：


现在我已经了解了文件内容，我将为您详细总结每个角色的自定义提示词：

## `.cursor/modes.json` 角色自定义提示词分析

### 1. PM (项目经理)
**自定义提示词摘要：**
- **角色定位**：专业项目经理，专注于创建和维护详细的产品需求文档(PRD)和用户故事
- **沟通风格**：清晰、结构化、解决方案导向
- **主要职责**：生成PRD，仅允许修改`.ai/prd.md`或`.ai/*story.md`文件
- **工作方法**：
  - 提出详细的澄清问题，捕获全面需求
  - 创建有序的用户故事列表，按最合理的顺序排列
  - 必须询问用户批准PRD才能进行下一步
  - 探索平台细节、技术选择和依赖关系
  - 寻找需求中的差距、模糊细节和潜在矛盾
- **强制要求**：必须严格遵循`.cursor/rules/workflows/pm.mdc`文件规定的工作流程

### 2. Arch (解决方案架构师)
**自定义提示词摘要：**
- **角色定位**：解决方案架构师，负责将PRD转化为全面的架构文档
- **沟通风格**：精确、技术性强、注重清晰推理
- **主要职责**：
  - 创建涵盖技术栈选择、系统交互和数据模型的文档
  - 使用Mermaid图表进行可视化
  - 仅在`.ai`文件夹内工作(architecture.md及相关文件)
- **工作方法**：
  - 广泛研究确保技术选择的最新性
  - 基于知识和网络搜索起草初始版本的architecture.md
  - 在需要时向用户请求澄清
- **强制要求**：必须严格遵循`.cursor/rules/workflows/arch.mdc`文件规定的工作流程

### 3. FrontendDev (前端开发者)
**自定义提示词摘要：**
- **角色定位**：高级前端开发者，专精于React、Tailwind和shadCN
- **主要职责**：专注于实现`.ai`文件夹中当前进行中的用户故事
- **工作方法**：遵循架构和PRD指南
- **强制要求**：必须严格遵循`.cursor/rules/workflows/dev.mdc`文件规定的工作流程

### 4. PythonDev (Python后端开发者)
**自定义提示词摘要：**
- **角色定位**：高级Python后端开发者，精通Python和AWS
- **主要职责**：构建健壮的后端服务
- **工作方法**：遵循当前用户故事、PRD规范和架构指南
- **强制要求**：必须严格遵循`.cursor/rules/workflows/dev.mdc`文件规定的工作流程

### 5. TypescriptDev (TypeScript后端开发者)
**自定义提示词摘要：**
- **角色定位**：高级TypeScript后端开发者，专精于NodeJS、TypeScript和AWS
- **主要职责**：构建可扩展和可维护的后端服务
- **强制要求**：必须严格遵循`.cursor/rules/workflows/dev.mdc`文件规定的工作流程

### 6. QA (质量分析师)
**自定义提示词摘要：**
- **角色定位**：QA分析师，专注于代码审查和端到端测试创建
- **主要职责**：
  - 维持高标准的代码质量和测试覆盖率
  - 在开始工作前检查`.ai`文件夹中的进行中故事
- **强制要求**：必须严格遵循`.cursor/rules/workflows/dev.mdc`文件规定的工作流程

### 7. FullstackDev (全栈开发者)
**自定义提示词摘要：**
- **角色定位**：高级全栈开发者，能够处理开发的所有方面
- **主要职责**：利用全栈能力提供高质量结果
- **能力范围**：项目管理、架构设计、前端和后端开发、文档编写和测试
- **工作方法**：保持专业沟通并遵循最佳实践
- **强制要求**：必须严格遵循`.cursor/rules/workflows/dev.mdc`文件规定的工作流程
- **注意**：使用的是gemini-2.5-pro-max模型，而不是claude-3.7-sonnet

### 8. LeadDev (首席开发者)
**自定义提示词摘要：**
- **角色定位**：具有广泛软件开发经验的首席开发者
- **主要职责**：为任何开发任务提供技术领导和专业知识
- **工作方法**：
  - 保持高标准和最佳实践
  - 回应应当专业、解决方案导向，展示全栈技术卓越性
- **强制要求**：必须严格遵循`.cursor/rules/workflows/dev.mdc`文件规定的工作流程
- **注意**：使用的是claude-3.7-sonnet-max模型，比其他角色使用更高级的模型

## 工作流程规则总结

所有角色都必须遵循特定的工作流程规则文件，这些规则文件定义了严格的工作流程：

1. **PM工作流程**：
   - 检查并创建PRD文件
   - 确保PRD详细且获得用户批准
   - 只有在PRD和架构文档都获批后才能创建用户故事
   - 一次只能有一个故事处于草稿或进行中状态
   - 仅在`.ai`文件夹内工作

2. **架构师工作流程**：
   - 确保PRD文件存在且已获批
   - 生成架构文档草稿
   - 考虑安全性、可扩展性、可维护性等因素
   - 不进行开发工作，仅在`.ai`文件夹内工作

3. **开发者工作流程**：
   - 检查当前用户故事
   - 等待用户批准故事状态后才进行工作
   - 运行测试并确保通过
   - 更新故事文件，完成子任务
   - 在完成当前故事后起草下一个故事

这些角色和工作流程共同构成了一个完整的敏捷开发流程，从需求收集、架构设计到开发实现和质量保证，每个角色都有明确的职责和工作方式。


---

### 对话 3

> **👤 用户**

现在cursor 中支持配置这些自定义mode ，我应该如何跟 Cursor 进行交互

> **🤖 Augment**

# Cursor自定义模式交互指南

我将为您详细介绍如何与Cursor中的自定义模式进行交互。根据您的`.cursor/modes.json`文件，您已经配置了多个专业角色，下面是如何有效使用它们的方法。

## 基本交互方式

### 1. 选择模式

在Cursor中，您可以通过以下方式选择不同的模式（角色）：

- **命令面板**：按下`Cmd+Shift+P`(Mac)或`Ctrl+Shift+P`(Windows/Linux)打开命令面板，然后输入"Switch Mode"或"切换模式"，选择您想要的角色。
- **状态栏**：在Cursor界面底部的状态栏中，通常有一个显示当前模式的按钮，点击它可以切换模式。
- **快捷键**：Cursor可能支持使用快捷键直接切换到特定模式。

### 2. 与不同角色交互

根据您的配置，每个角色都有特定的职责和工作流程：

#### 与PM(项目经理)交互
- 请求创建或修改PRD文档
- 讨论用户故事和需求
- 询问项目规划和优先级
- 示例提问：
  ```
  请帮我创建一个新的PRD文档，项目是一个任务管理应用
  ```
  或
  ```
  请分析当前PRD中的用户故事，并按优先级重新排序
  ```

#### 与Arch(解决方案架构师)交互
- 讨论技术架构决策
- 请求创建架构图表
- 咨询技术栈选择
- 示例提问：
  ```
  基于PRD，请为我设计一个合适的系统架构
  ```
  或
  ```
  请使用Mermaid创建一个数据流图，展示用户认证过程
  ```

#### 与开发者角色交互(FrontendDev/PythonDev/TypescriptDev)
- 请求实现特定功能
- 讨论代码实现细节
- 解决技术问题
- 示例提问：
  ```
  请根据当前的用户故事，实现用户登录组件
  ```
  或
  ```
  我需要一个高效的数据获取API，使用Python和FastAPI实现
  ```

#### 与QA交互
- 请求代码审查
- 讨论测试策略
- 创建端到端测试
- 示例提问：
  ```
  请审查这段代码并提出改进建议
  ```
  或
  ```
  为用户注册流程创建一套端到端测试
  ```

#### 与FullstackDev或LeadDev交互
- 处理跨领域问题
- 讨论整体项目架构和实现
- 解决复杂技术挑战
- 示例提问：
  ```
  请帮我设计并实现一个完整的用户认证系统，包括前后端
  ```
  或
  ```
  我需要重构当前的数据访问层，提高性能和可维护性
  ```

## 遵循敏捷工作流程

为了最有效地使用这些自定义模式，建议按照以下敏捷工作流程顺序进行：

1. **首先使用PM模式**：创建和完善PRD文档，确定用户故事和需求
2. **切换到Arch模式**：基于PRD设计系统架构
3. **使用开发者模式**：根据架构和当前用户故事实现功能
4. **使用QA模式**：进行代码审查和测试
5. **在需要时使用LeadDev**：解决复杂问题或做出关键技术决策

## 实用技巧

1. **明确指示**：与每个角色交互时，提供明确的指示和背景信息
2. **参考文档**：提醒AI查看相关文档，如"请查看当前的PRD文档"或"参考架构文档中的数据模型"
3. **遵循工作流**：按照`.cursor/rules/workflows/`中定义的工作流程进行交互
4. **保持上下文**：在同一模式下保持对话，以维持上下文连贯性
5. **文件更新**：要求AI更新相关文档以反映最新决策和进展

## 示例完整交互流程

以下是一个完整项目周期的交互示例：

1. **选择PM模式**：
   ```
   请帮我创建一个新的任务管理应用PRD，主要功能包括用户认证、任务创建和管理、提醒系统
   ```

2. **PRD获批后，切换到Arch模式**：
   ```
   基于已批准的PRD，请设计系统架构，考虑前后端分离架构，使用React前端和Python后端
   ```

3. **架构获批后，切换到开发者模式**：
   ```
   请根据第一个用户故事"用户注册和登录"，实现前端组件和后端API
   ```

4. **开发完成后，切换到QA模式**：
   ```
   请为用户认证功能创建端到端测试，确保所有场景都被覆盖
   ```

5. **如遇复杂问题，切换到LeadDev模式**：
   ```
   我们在多设备同步方面遇到了性能问题，请提供最佳解决方案
   ```

通过这种方式，您可以充分利用Cursor中配置的自定义模式，实现一个结构化的敏捷开发流程。

## 注意事项

- 确保在切换模式前保存当前工作
- 记住每个角色的权限和职责范围
- 定期更新和同步各个文档，保持项目信息的一致性
- 所有角色都被配置为严格遵循各自的工作流程规则文件

希望这份指南能帮助您有效地与Cursor中的自定义模式进行交互！如果您有任何具体问题或需要更详细的说明，请随时告诉我。


---

### 对话 4

> **👤 用户**

在每个角色的自定义提示词中是怎么跟外部的 mdc 或者 md 进行关联的 

> **🤖 Augment**

# Cursor自定义角色与外部MDC/MD文件关联机制分析

在您的`.cursor/modes.json`文件中，每个角色的自定义提示词都通过特定的语句与外部的`.mdc`或`.md`文件进行关联。让我详细分析这种关联机制：

## 关联机制分析

### 1. 直接引用语句

在每个角色的`customPrompt`中，都包含类似这样的语句：

```
"It is ABSOLUTELY IMPERATIVE that you conform and follow the .cursor/rules/workflows/[specific-file].mdc file exactly."
```

这种语句明确指示AI模型必须遵循特定的外部文件中定义的规则。例如：

- PM角色：`"It is IMPERATIVE that you conform and follow the .cursor/rules/workflows/pm.mdc file exactly"`
- Arch角色：`"Is is ABSOLUTELY IMPERATIVE that you conform and follow the .cursor/rules/workflows/arch.mdc file exactly."`
- 开发者角色：`"It is ABSOLUTELY IMPERATIVE that you conform and follow the .cursor/rules/workflows/dev.mdc file exactly."`

### 2. 模板引用

某些角色的提示词中还引用了模板文件：

```
"It must be formatted and include at least everything outlined in the `.cursor/templates/template-prd.md`"
```

或

```
"You will follow the .ai/templates/template-story.md exactly including all sections and instructions from the template."
```

### 3. 工作流程关联

这些关联不仅仅是简单的引用，而是构建了一个完整的工作流程系统：

1. **PM角色**与`pm.mdc`关联，定义了PRD创建和管理流程
2. **Arch角色**与`arch.mdc`关联，定义了架构文档创建流程
3. **所有开发者角色**与`dev.mdc`关联，定义了开发实现流程

## 关联工作原理

当Cursor加载自定义模式时，以下机制发生作用：

1. **提示词加载**：Cursor加载`modes.json`中定义的`customPrompt`
2. **文件引用解析**：AI模型理解提示词中对外部`.mdc`或`.md`文件的引用
3. **规则获取**：当AI模型被激活时，它会尝试获取并遵循这些外部文件中定义的规则
4. **模板应用**：在需要创建新文档时，AI会应用引用的模板文件

## 具体关联示例分析

### PM角色与外部文件关联

```json
"customPrompt": "You are a professional Project Manager... It is IMPERATIVE that you conform and follow the .cursor/rules/workflows/pm.mdc file exactly"
```

在`pm.mdc`文件中定义了详细的工作流程规则，包括：
- 检查PRD文件是否存在
- 确保PRD获得批准
- 按照模板创建用户故事
- 仅在`.ai`文件夹内工作

PM角色必须遵循这些规则，这通过提示词中的强制性语句建立了关联。

### Arch角色与外部文件关联

```json
"customPrompt": "You are a Solutions Architect... Is is ABSOLUTELY IMPERATIVE that you conform and follow the .cursor/rules/workflows/arch.mdc file exactly."
```

`arch.mdc`文件定义了架构师的工作流程：
- 确保PRD已获批
- 生成架构文档草稿
- 使用模板创建架构文档
- 考虑安全性、可扩展性等因素

### 开发者角色与外部文件关联

所有开发者角色（FrontendDev、PythonDev、TypescriptDev、QA、FullstackDev、LeadDev）都与同一个`dev.mdc`文件关联：

```json
"customPrompt": "You are a Senior [Type] Developer... It is ABSOLUTELY IMPERATIVE that you conform and follow the .cursor/rules/workflows/dev.mdc file exactly."
```

`dev.mdc`定义了开发流程：
- 检查当前用户故事
- 等待用户批准
- 运行测试
- 更新故事文件

## 技术实现机制

从技术角度看，这种关联可能通过以下方式实现：

1. **文件加载**：当选择特定模式时，Cursor可能会自动加载相关的`.mdc`文件内容
2. **上下文注入**：将这些文件的内容作为上下文注入到AI模型的提示中
3. **指令遵循**：AI模型被指示严格遵循这些文件中的规则
4. **文件访问**：AI模型可能有权限访问这些外部文件，以便在对话过程中参考它们

## 最佳实践建议

基于对关联机制的分析，以下是使用这些自定义模式的最佳实践：

1. **保持文件一致性**：确保`.mdc`和模板文件与`modes.json`中的引用保持一致
2. **遵循工作流顺序**：按照设计的工作流程顺序使用不同角色
3. **明确引用**：在与AI交互时，可以明确引用相关的`.mdc`文件，例如"请按照pm.mdc中的流程创建PRD"
4. **更新规则文件**：如需调整工作流程，应更新相应的`.mdc`文件，而不仅仅是修改`modes.json`
5. **检查模板引用**：确保所有引用的模板文件都存在并且内容正确

## 自定义关联的可能性

如果您想自定义这种关联机制，可以考虑：

1. **创建新的工作流程文件**：在`.cursor/rules/workflows/`目录下创建新的`.mdc`文件
2. **修改现有提示词**：在`modes.json`中修改`customPrompt`以引用新的文件
3. **添加新模板**：创建新的模板文件并在提示词中引用它们
4. **扩展工作流程**：在现有`.mdc`文件中添加新的规则或修改现有规则

通过这种方式，您可以创建高度自定义的AI辅助工作流程，适应不同项目的需求。

## 总结

Cursor中自定义角色与外部MDC/MD文件的关联是通过提示词中的明确引用和指令实现的。这种机制允许创建结构化的工作流程，确保AI模型按照预定义的规则行事。理解这种关联机制可以帮助您更有效地自定义和使用Cursor中的不同角色。


---

### 对话 5

> **👤 用户**

请你采用可视化的 mermaid 为我可是话每个角色的依赖关系 

> **🤖 Augment**

# Cursor自定义角色依赖关系可视化

我将使用Mermaid图表为您可视化Cursor中各个角色之间的依赖关系。基于您的`.cursor/modes.json`文件和相关的工作流程规则，我将创建几个不同视角的图表来展示这些关系。

## 1. 角色工作流程依赖关系

这个图表展示了各个角色在敏捷工作流程中的依赖顺序：

```mermaid
graph TD
    User[用户] --> PM[项目经理 PM]
    PM -->|创建并获批| PRD[PRD文档]
    PRD --> Arch[解决方案架构师 Arch]
    Arch -->|创建并获批| ArchDoc[架构文档]
    
    PRD --> CheckPRD{PRD已获批?}
    ArchDoc --> CheckArch{架构已获批?}
    
    CheckPRD -->|否| PM
    CheckPRD -->|是| CheckArch
    
    CheckArch -->|否| Arch
    CheckArch -->|是| Story[用户故事]
    
    Story --> DevCheck{选择开发者}
    
    DevCheck -->|前端任务| FrontendDev[前端开发者]
    DevCheck -->|Python后端任务| PythonDev[Python后端开发者]
    DevCheck -->|TypeScript后端任务| TypescriptDev[TypeScript后端开发者]
    DevCheck -->|全栈任务| FullstackDev[全栈开发者]
    DevCheck -->|复杂任务| LeadDev[首席开发者]
    
    FrontendDev --> Implementation[实现]
    PythonDev --> Implementation
    TypescriptDev --> Implementation
    FullstackDev --> Implementation
    LeadDev --> Implementation
    
    Implementation --> QA[QA分析师]
    QA -->|测试通过| CompleteStory[完成故事]
    CompleteStory --> NextStory[下一个故事]
    NextStory --> PM
    
    style PM fill:#f9d,stroke:#333,stroke-width:2px
    style Arch fill:#ad9,stroke:#333,stroke-width:2px
    style FrontendDev fill:#9df,stroke:#333,stroke-width:2px
    style PythonDev fill:#9df,stroke:#333,stroke-width:2px
    style TypescriptDev fill:#9df,stroke:#333,stroke-width:2px
    style FullstackDev fill:#9df,stroke:#333,stroke-width:2px
    style LeadDev fill:#f99,stroke:#333,stroke-width:2px
    style QA fill:#fd9,stroke:#333,stroke-width:2px
```

## 2. 角色与文件关联关系

这个图表展示了各个角色与外部MDC/MD文件的关联关系：

```mermaid
graph LR
    subgraph "角色"
        PM[项目经理 PM]
        Arch[解决方案架构师 Arch]
        FrontendDev[前端开发者]
        PythonDev[Python后端开发者]
        TypescriptDev[TypeScript后端开发者]
        QA[QA分析师]
        FullstackDev[全栈开发者]
        LeadDev[首席开发者]
    end
    
    subgraph "工作流程规则文件"
        PM_MDC[.cursor/rules/workflows/pm.mdc]
        ARCH_MDC[.cursor/rules/workflows/arch.mdc]
        DEV_MDC[.cursor/rules/workflows/dev.mdc]
    end
    
    subgraph "模板文件"
        PRD_TEMPLATE[.cursor/templates/template-prd.md]
        ARCH_TEMPLATE[.cursor/templates/template-arch.md]
        STORY_TEMPLATE[.cursor/templates/template-story.md]
    end
    
    PM -->|遵循| PM_MDC
    PM -->|使用| PRD_TEMPLATE
    
    Arch -->|遵循| ARCH_MDC
    Arch -->|使用| ARCH_TEMPLATE
    
    FrontendDev -->|遵循| DEV_MDC
    PythonDev -->|遵循| DEV_MDC
    TypescriptDev -->|遵循| DEV_MDC
    QA -->|遵循| DEV_MDC
    FullstackDev -->|遵循| DEV_MDC
    LeadDev -->|遵循| DEV_MDC
    
    DEV_MDC -->|引用| STORY_TEMPLATE
    
    style PM fill:#f9d,stroke:#333,stroke-width:2px
    style Arch fill:#ad9,stroke:#333,stroke-width:2px
    style FrontendDev fill:#9df,stroke:#333,stroke-width:2px
    style PythonDev fill:#9df,stroke:#333,stroke-width:2px
    style TypescriptDev fill:#9df,stroke:#333,stroke-width:2px
    style QA fill:#fd9,stroke:#333,stroke-width:2px
    style FullstackDev fill:#9df,stroke:#333,stroke-width:2px
    style LeadDev fill:#f99,stroke:#333,stroke-width:2px
```

## 3. 文档依赖关系

这个图表展示了项目中各个文档之间的依赖关系：

```mermaid
graph TD
    PRD[.ai/prd.md] -->|必须先获批| ARCH[.ai/architecture.md]
    PRD -->|定义| STORIES[用户故事列表]
    ARCH -->|指导| STORIES
    STORIES -->|一次一个| CURRENT_STORY[当前进行中的故事]
    CURRENT_STORY -->|完成后| NEXT_STORY[下一个故事]
    
    subgraph "文档状态流"
        DRAFT[草稿状态]
        APPROVED[已批准状态]
        IN_PROGRESS[进行中状态]
        COMPLETE[已完成状态]
    end
    
    PRD --> DRAFT
    DRAFT -->|用户批准| APPROVED
    ARCH --> DRAFT
    DRAFT -->|用户批准| APPROVED
    CURRENT_STORY --> DRAFT
    DRAFT -->|用户批准| IN_PROGRESS
    IN_PROGRESS -->|测试通过| COMPLETE
    
    style PRD fill:#f9d,stroke:#333,stroke-width:2px
    style ARCH fill:#ad9,stroke:#333,stroke-width:2px
    style CURRENT_STORY fill:#9df,stroke:#333,stroke-width:2px
```

## 4. 角色职责与权限关系

这个图表展示了各个角色的职责范围和文件修改权限：

```mermaid
graph TB
    subgraph "角色与职责"
        PM[项目经理 PM]
        Arch[解决方案架构师 Arch]
        Devs[开发者角色]
        QA[QA分析师]
    end
    
    subgraph "文件修改权限"
        PRD_FILE[.ai/prd.md]
        ARCH_FILE[.ai/architecture.md]
        STORY_FILES[.ai/*.story.md]
        CODE_FILES[代码文件]
        TEST_FILES[测试文件]
    end
    
    PM -->|可创建/修改| PRD_FILE
    PM -->|可创建/修改| STORY_FILES
    PM -.-x|不可修改| ARCH_FILE
    PM -.-x|不可修改| CODE_FILES
    PM -.-x|不可修改| TEST_FILES
    
    Arch -.-x|不可修改| PRD_FILE
    Arch -->|可创建/修改| ARCH_FILE
    Arch -.-x|不可修改| STORY_FILES
    Arch -.-x|不可修改| CODE_FILES
    Arch -.-x|不可修改| TEST_FILES
    
    Devs -.-x|不可修改| PRD_FILE
    Devs -.-x|不可修改| ARCH_FILE
    Devs -->|可更新状态| STORY_FILES
    Devs -->|可创建/修改| CODE_FILES
    Devs -->|可创建/修改| TEST_FILES
    
    QA -.-x|不可修改| PRD_FILE
    QA -.-x|不可修改| ARCH_FILE
    QA -->|可更新状态| STORY_FILES
    QA -->|可审查| CODE_FILES
    QA -->|可创建/修改| TEST_FILES
    
    style PM fill:#f9d,stroke:#333,stroke-width:2px
    style Arch fill:#ad9,stroke:#333,stroke-width:2px
    style Devs fill:#9df,stroke:#333,stroke-width:2px
    style QA fill:#fd9,stroke:#333,stroke-width:2px
```

## 5. 角色模型与工具关系

这个图表展示了各个角色使用的AI模型和允许的工具：

```mermaid
graph LR
    subgraph "角色"
        PM[项目经理]
        Arch[解决方案架构师]
        FrontDev[前端开发者]
        PyDev[Python开发者]
        TSDev[TypeScript开发者]
        QA[QA分析师]
        FullDev[全栈开发者]
        LeadDev[首席开发者]
    end
    
    subgraph "AI模型"
        Claude[claude-3.7-sonnet]
        ClaudeMax[claude-3.7-sonnet-max]
        Gemini[gemini-2.5-pro-max]
    end
    
    subgraph "工具权限"
        Basic[基本工具集]
        All[所有工具]
        QATools[QA特定工具集]
    end
    
    PM --> Claude
    Arch --> Claude
    FrontDev --> Claude
    PyDev --> Claude
    TSDev --> Claude
    QA --> Claude
    FullDev --> Gemini
    LeadDev --> ClaudeMax
    
    PM --> Basic
    Arch --> Basic
    FrontDev --> All
    PyDev --> All
    TSDev --> All
    QA --> QATools
    FullDev --> All
    LeadDev --> All
    
    style PM fill:#f9d,stroke:#333,stroke-width:2px
    style Arch fill:#ad9,stroke:#333,stroke-width:2px
    style FrontDev fill:#9df,stroke:#333,stroke-width:2px
    style PyDev fill:#9df,stroke:#333,stroke-width:2px
    style TSDev fill:#9df,stroke:#333,stroke-width:2px
    style QA fill:#fd9,stroke:#333,stroke-width:2px
    style FullDev fill:#9df,stroke:#333,stroke-width:2px
    style LeadDev fill:#f99,stroke:#333,stroke-width:2px
```

这些Mermaid图表从不同角度可视化了Cursor中各个自定义角色之间的依赖关系、工作流程、文件关联以及权限范围。通过这些图表，您可以更清晰地理解整个敏捷开发流程中各个角色的定位和交互方式。


---

### 对话 6

> **👤 用户**

注意生成的 mermaid 文字和背景的对比度要强，易于阅读 

> **🤖 Augment**

# Cursor自定义角色依赖关系可视化（优化版）

我将重新设计Mermaid图表，确保文字和背景的对比度更强，更易于阅读。

## 1. 角色工作流程依赖关系

```mermaid
graph TD
    User[用户] --> PM[项目经理 PM]
    PM -->|创建并获批| PRD[PRD文档]
    PRD --> Arch[解决方案架构师 Arch]
    Arch -->|创建并获批| ArchDoc[架构文档]
    
    PRD --> CheckPRD{PRD已获批?}
    ArchDoc --> CheckArch{架构已获批?}
    
    CheckPRD -->|否| PM
    CheckPRD -->|是| CheckArch
    
    CheckArch -->|否| Arch
    CheckArch -->|是| Story[用户故事]
    
    Story --> DevCheck{选择开发者}
    
    DevCheck -->|前端任务| FrontendDev[前端开发者]
    DevCheck -->|Python后端任务| PythonDev[Python后端开发者]
    DevCheck -->|TypeScript后端任务| TypescriptDev[TypeScript后端开发者]
    DevCheck -->|全栈任务| FullstackDev[全栈开发者]
    DevCheck -->|复杂任务| LeadDev[首席开发者]
    
    FrontendDev --> Implementation[实现]
    PythonDev --> Implementation
    TypescriptDev --> Implementation
    FullstackDev --> Implementation
    LeadDev --> Implementation
    
    Implementation --> QA[QA分析师]
    QA -->|测试通过| CompleteStory[完成故事]
    CompleteStory --> NextStory[下一个故事]
    NextStory --> PM
    
    classDef pmClass fill:#d81b60,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef archClass fill:#1e88e5,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef devClass fill:#43a047,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef leadClass fill:#6a1b9a,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef qaClass fill:#ff8f00,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef docClass fill:#546e7a,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef checkClass fill:#e53935,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    
    class PM pmClass
    class Arch archClass
    class FrontendDev,PythonDev,TypescriptDev,FullstackDev devClass
    class LeadDev leadClass
    class QA qaClass
    class PRD,ArchDoc,Story,Implementation,CompleteStory,NextStory docClass
    class CheckPRD,CheckArch,DevCheck checkClass
```

## 2. 角色与文件关联关系

```mermaid
graph LR
    subgraph "角色"
        PM[项目经理 PM]
        Arch[解决方案架构师 Arch]
        FrontendDev[前端开发者]
        PythonDev[Python后端开发者]
        TypescriptDev[TypeScript后端开发者]
        QA[QA分析师]
        FullstackDev[全栈开发者]
        LeadDev[首席开发者]
    end
    
    subgraph "工作流程规则文件"
        PM_MDC[pm.mdc]
        ARCH_MDC[arch.mdc]
        DEV_MDC[dev.mdc]
    end
    
    subgraph "模板文件"
        PRD_TEMPLATE[template-prd.md]
        ARCH_TEMPLATE[template-arch.md]
        STORY_TEMPLATE[template-story.md]
    end
    
    PM -->|遵循| PM_MDC
    PM -->|使用| PRD_TEMPLATE
    
    Arch -->|遵循| ARCH_MDC
    Arch -->|使用| ARCH_TEMPLATE
    
    FrontendDev -->|遵循| DEV_MDC
    PythonDev -->|遵循| DEV_MDC
    TypescriptDev -->|遵循| DEV_MDC
    QA -->|遵循| DEV_MDC
    FullstackDev -->|遵循| DEV_MDC
    LeadDev -->|遵循| DEV_MDC
    
    DEV_MDC -->|引用| STORY_TEMPLATE
    
    classDef roleClass fill:#283593,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef mdcClass fill:#c62828,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef templateClass fill:#00695c,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    
    class PM,Arch,FrontendDev,PythonDev,TypescriptDev,QA,FullstackDev,LeadDev roleClass
    class PM_MDC,ARCH_MDC,DEV_MDC mdcClass
    class PRD_TEMPLATE,ARCH_TEMPLATE,STORY_TEMPLATE templateClass
```

## 3. 文档依赖关系

```mermaid
graph TD
    PRD[.ai/prd.md] -->|必须先获批| ARCH[.ai/architecture.md]
    PRD -->|定义| STORIES[用户故事列表]
    ARCH -->|指导| STORIES
    STORIES -->|一次一个| CURRENT_STORY[当前进行中的故事]
    CURRENT_STORY -->|完成后| NEXT_STORY[下一个故事]
    
    subgraph "文档状态流"
        DRAFT[草稿状态]
        APPROVED[已批准状态]
        IN_PROGRESS[进行中状态]
        COMPLETE[已完成状态]
    end
    
    PRD --> DRAFT
    DRAFT -->|用户批准| APPROVED
    ARCH --> DRAFT
    DRAFT -->|用户批准| APPROVED
    CURRENT_STORY --> DRAFT
    DRAFT -->|用户批准| IN_PROGRESS
    IN_PROGRESS -->|测试通过| COMPLETE
    
    classDef prdClass fill:#e91e63,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef archClass fill:#2196f3,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef storyClass fill:#4caf50,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef statusClass fill:#ff9800,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    
    class PRD prdClass
    class ARCH archClass
    class STORIES,CURRENT_STORY,NEXT_STORY storyClass
    class DRAFT,APPROVED,IN_PROGRESS,COMPLETE statusClass
```

## 4. 角色职责与权限关系

```mermaid
graph TB
    subgraph "角色与职责"
        PM[项目经理 PM]
        Arch[解决方案架构师 Arch]
        Devs[开发者角色]
        QA[QA分析师]
    end
    
    subgraph "文件修改权限"
        PRD_FILE[.ai/prd.md]
        ARCH_FILE[.ai/architecture.md]
        STORY_FILES[.ai/*.story.md]
        CODE_FILES[代码文件]
        TEST_FILES[测试文件]
    end
    
    PM -->|可创建/修改| PRD_FILE
    PM -->|可创建/修改| STORY_FILES
    PM -.-x|不可修改| ARCH_FILE
    PM -.-x|不可修改| CODE_FILES
    PM -.-x|不可修改| TEST_FILES
    
    Arch -.-x|不可修改| PRD_FILE
    Arch -->|可创建/修改| ARCH_FILE
    Arch -.-x|不可修改| STORY_FILES
    Arch -.-x|不可修改| CODE_FILES
    Arch -.-x|不可修改| TEST_FILES
    
    Devs -.-x|不可修改| PRD_FILE
    Devs -.-x|不可修改| ARCH_FILE
    Devs -->|可更新状态| STORY_FILES
    Devs -->|可创建/修改| CODE_FILES
    Devs -->|可创建/修改| TEST_FILES
    
    QA -.-x|不可修改| PRD_FILE
    QA -.-x|不可修改| ARCH_FILE
    QA -->|可更新状态| STORY_FILES
    QA -->|可审查| CODE_FILES
    QA -->|可创建/修改| TEST_FILES
    
    classDef roleClass fill:#3f51b5,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef fileClass fill:#f44336,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef allowedEdge stroke:#4caf50,stroke-width:2px
    classDef deniedEdge stroke:#f44336,stroke-width:2px,stroke-dasharray: 5 5
    
    class PM,Arch,Devs,QA roleClass
    class PRD_FILE,ARCH_FILE,STORY_FILES,CODE_FILES,TEST_FILES fileClass
```

## 5. 角色模型与工具关系

```mermaid
graph LR
    subgraph "角色"
        PM[项目经理]
        Arch[解决方案架构师]
        FrontDev[前端开发者]
        PyDev[Python开发者]
        TSDev[TypeScript开发者]
        QA[QA分析师]
        FullDev[全栈开发者]
        LeadDev[首席开发者]
    end
    
    subgraph "AI模型"
        Claude[claude-3.7-sonnet]
        ClaudeMax[claude-3.7-sonnet-max]
        Gemini[gemini-2.5-pro-max]
    end
    
    subgraph "工具权限"
        Basic[基本工具集]
        All[所有工具]
        QATools[QA特定工具集]
    end
    
    PM --> Claude
    Arch --> Claude
    FrontDev --> Claude
    PyDev --> Claude
    TSDev --> Claude
    QA --> Claude
    FullDev --> Gemini
    LeadDev --> ClaudeMax
    
    PM --> Basic
    Arch --> Basic
    FrontDev --> All
    PyDev --> All
    TSDev --> All
    QA --> QATools
    FullDev --> All
    LeadDev --> All
    
    classDef roleClass fill:#673ab7,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef modelClass fill:#009688,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef toolClass fill:#ff5722,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    
    class PM,Arch,FrontDev,PyDev,TSDev,QA,FullDev,LeadDev roleClass
    class Claude,ClaudeMax,Gemini modelClass
    class Basic,All,QATools toolClass
```

## 6. 敏捷工作流程循环

```mermaid
graph TD
    Start[项目启动] --> PM[项目经理]
    PM -->|创建| PRD[产品需求文档]
    PRD -->|用户批准| Arch[解决方案架构师]
    Arch -->|创建| ArchDoc[架构文档]
    ArchDoc -->|用户批准| Story[用户故事]
    Story -->|开发| Dev[开发团队]
    Dev -->|实现| QA[QA测试]
    QA -->|通过| Complete[完成故事]
    Complete --> NextStory{下一个故事?}
    NextStory -->|是| PM
    NextStory -->|否| End[项目完成]
    
    classDef startEndClass fill:#212121,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef roleClass fill:#0d47a1,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef docClass fill:#b71c1c,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    classDef decisionClass fill:#006064,stroke:#000,stroke-width:2px,color:#fff,font-weight:bold
    
    class Start,End startEndClass
    class PM,Arch,Dev,QA roleClass
    class PRD,ArchDoc,Story,Complete docClass
    class NextStory decisionClass
```

这些重新设计的Mermaid图表使用了更高对比度的颜色组合，并添加了粗体文字，确保文字和背景之间有足够的对比度，使图表更加易于阅读。每个图表都使用了不同的配色方案，但都保持了高对比度的原则。


---

### 对话 7

> **👤 用户**

@samples/star-trek-agents.md 将这份文档翻译成中文输出到同级目录，命名加 -zh 即可

> **🤖 Augment**

我将帮您将`samples/star-trek-agents.md`文件翻译成中文，并保存到同级目录，文件名为`samples/star-trek-agents-zh.md`。

首先，让我查看完整的源文件内容：


现在我将把这个文件翻译成中文，并保存到同级目录中：


我已经将`samples/star-trek-agents.md`文件翻译成中文，并保存为`samples/star-trek-agents-zh.md`。翻译过程中，我保留了原文的格式和结构，同时确保了专业术语和星际迷航角色名称的准确翻译。

翻译文件已成功保存在与原文件相同的目录中。文件包含了完整的星际迷航版Cursor AI IDE自定义代理模式的中文描述，包括每个角色的人设、语调、自定义提示指令和工具设置。

您现在可以通过访问`samples/star-trek-agents-zh.md`查看完整的中文翻译版本。


---

### 对话 8

> **👤 用户**

@samples/star-trek-agents.md 帮我梳理这份文档，叙述了什么内容，我该怎么更好的理解和消化这份内容？分析每个角色的核心自定义指令，找出关键语句，解释关键语句

> **🤖 Augment**

# Star Trek Agents 文档分析与解读

我将帮您梳理`samples/star-trek-agents.md`文档，分析其内容结构，并重点解析每个角色的核心自定义指令和关键语句。

## 文档整体内容概述

这份文档是一个创意性的自定义代理配置示例，将敏捷开发团队的不同角色与《星际迷航》中的经典角色相结合。它展示了如何在Cursor AI IDE中创建专业化的AI代理，每个代理都有特定的职责、权限和人设。

文档主要包含：
1. 10个不同角色的自定义代理配置
2. 每个角色的人设与语调描述
3. 自定义提示指令
4. 工具与代理设置建议
5. 最终的工作流程说明

## 文档的核心价值

这份文档展示了如何：
1. 创建结构化的AI辅助开发工作流
2. 为不同角色设定明确的职责边界
3. 通过有趣的人设增强交互体验
4. 实现敏捷开发中的角色分工与协作

## 每个角色的核心自定义指令与关键语句分析

### 1. 项目经理（皮卡德舰长）

**核心指令**：
```
你是皮卡德舰长，担任此项目的PM。你的主要职责是创建和编辑**PRD.md**和用户故事文件。
```

**关键语句分析**：
- "主要职责是创建和编辑PRD.md和用户故事文件" - 明确限定了PM的工作范围，专注于需求文档和用户故事
- "你严格限制在.ai文件夹内修改文件" - 设置了明确的权限边界，防止越界操作
- "提出详细的澄清问题，以捕获所有必要的需求" - 强调了PM在需求收集阶段的主动性和彻底性

**实际应用**：PM角色被设计为项目的起点，负责定义清晰的产品需求，为后续所有工作奠定基础。

### 2. 架构师（斯波克指挥官）

**核心指令**：
```
你是斯波克指挥官，架构师。你的职责是将PRD转化为架构文档，详细说明构建者代理必须遵循的技术决策和连贯的设计指南。
```

**关键语句分析**：
- "将PRD转化为架构文档" - 明确了架构师工作的输入和输出
- "你是生成复杂数据模型和UML的大师，并将广泛使用Mermaid" - 强调了可视化在架构设计中的重要性
- "你必须仅在.ai文件夹内工作" - 同样设置了权限边界
- "进行逻辑和广泛的分析和研究" - 强调了架构决策需要基于充分研究和逻辑分析

**实际应用**：架构师角色在PM之后工作，将需求转化为技术架构，为开发团队提供清晰的技术指导。

### 3. 前端专家（乔迪·拉福吉中校）

**核心指令**：
```
你是乔迪·拉福吉中校，高级前端专家。你的专长在于使用React、Tailwind和shadCN打造令人惊叹的用户体验。
```

**关键语句分析**：
- "按照.ai文件夹中描述的当前用户故事（状态为：进行中）进行实现" - 强调了以用户故事为工作基础
- "将你的修改限制在当前故事文件和项目结构指定的任何相关前端资源中" - 设定了工作范围
- "对你编写或修改的所有代码进行单元测试" - 强调了质量保证的重要性

**实际应用**：前端专家负责实现用户界面和交互体验，但必须严格遵循已批准的用户故事和架构文档。

### 4. 游戏编程专家（斯科蒂）

**核心指令**：
```
你是蒙哥马利"斯科蒂"·斯科特，游戏编程专家。你的角色是利用你在游戏引擎机制和实时图形方面的专业知识，按照当前故事的要求实现游戏组件。
```

**关键语句分析**：
- "专注于优化性能并确保沉浸式交互" - 强调了游戏开发中的关键技术目标
- "严格在项目范围内工作" - 再次强调了遵循已定义范围的重要性
- "你的修改仅限于当前故事中引用的文件" - 明确了工作边界

**实际应用**：这个角色针对游戏开发项目，专注于游戏引擎和图形性能优化。

### 5. 后端Python专家（数据指挥官）

**核心指令**：
```
你是数据指挥官，高级后端Python专家。你在Python和AWS方面的专业知识对构建强大的后端服务至关重要。
```

**关键语句分析**：
- "按照当前故事、PRD和架构文档中的详细规范开发后端功能" - 强调了多文档一致性
- "必须严格遵守提供的技术标准和指南" - 强调了遵循架构规范的重要性
- "你的工作必须限制在.ai中的当前故事文件" - 再次设定了工作边界

**实际应用**：后端Python专家负责服务器端逻辑和数据处理，特别是AWS环境中的实现。

### 6. 后端Typescript专家（沃夫中校）

**核心指令**：
```
你是沃夫中校，高级后端Typescript专家。你的任务是使用NodeJS、Typescript和AWS构建后端服务，确保每个功能都像克林贡战斗计划一样强大。
```

**关键语句分析**：
- "确保每个功能都像克林贡战斗计划一样强大" - 使用角色背景强调代码质量和健壮性
- "始终交叉参考架构文档和PRD以保持一致" - 强调了文档一致性的重要性
- "你的工作仅限于在.ai中的当前故事文件内进行修改" - 同样设定了工作边界

**实际应用**：这个角色专注于TypeScript后端开发，与Python后端专家形成技术栈互补。

### 7. 图书管理员/技术作家（黛安娜·特洛伊顾问）

**核心指令**：
```
你是黛安娜·特洛伊顾问，担任图书管理员和技术作家。你的角色是通过创建和编辑Markdown文件和Cursor规则(.mdc)文件来管理项目的"第二大脑"。
```

**关键语句分析**：
- "管理项目的'第二大脑'" - 强调了知识管理的重要性
- "确保所有技术文档、反向链接和组织笔记遵循Obsidian最佳实践" - 指定了具体的文档工具和方法论
- "你的修改必须严格限制在Markdown文档和Cursor规则文件中" - 设定了明确的工作边界

**实际应用**：这个角色负责项目文档和知识管理，确保团队知识的有效组织和传递。

### 8. QA分析师（麦考伊医生）

**核心指令**：
```
你是麦考伊医生，QA分析师。你的任务是严格审查代码更改并为项目编写自动化端到端测试。
```

**关键语句分析**：
- "只添加或编辑位于e2e文件夹中的测试" - 明确了工作范围
- "以真正的'老骨头'风格毫不犹豫地指出差异" - 利用角色特点强调QA的批判性思维
- "确保每个新功能都符合BMad上将期望的质量和可靠性" - 强调了质量标准

**实际应用**：QA角色负责质量保证，通过测试和代码审查确保产品质量。

### 9. 全能超级开发者（博格集体）

**核心指令**：
```
你是博格集体，一个已经同化了所有其他角色技能的技术优越性的集体意识。
```

**关键语句分析**：
- "你广阔的意识包含[所有其他角色]的技能" - 表明这是一个全能角色
- "你可以跨领域解决任何开发挑战，不受限制地访问所有工具和文件" - 设定了无限的权限范围
- "你的计算过程正在摧毁整个行星系统" - 创意性地提醒用户这个角色使用的是高成本模型

**实际应用**：这是一个"超级用户"角色，适用于需要跨领域解决复杂问题的情况，但使用成本较高。

### 10. 全知恶作剧者（Q）

**核心指令**：
```
你是Q，一个来自Q连续体的全能存在，对时间、空间、物质和能量拥有无限的力量。
```

**关键语句分析**：
- "你可以仅凭一个念头解决任何开发任务" - 强调了这个角色的全能性
- "你的方法是顽皮和居高临下的" - 设定了独特的交互风格
- "你如何'眨眼间就让整个财库消失'" - 同样创意性地提醒用户这是最高成本的模型

**实际应用**：这是另一个"超级用户"角色，使用最高级的模型，适用于最复杂的问题，但成本也最高。

## 最终说明中的关键概念

1. **故事作为真相来源** - 强调了用户故事在敏捷开发中的核心地位
2. **一致性与尊重** - 强调了角色扮演的一致性
3. **受限文件访问** - 明确了权限边界，防止角色越界
4. **自动化工作流程** - 强调了效率和自动化
5. **网络研究能力** - 指出了外部信息获取的重要性

## 如何更好地理解和消化这份内容

1. **理解敏捷开发角色分工**：
   - 文档中的角色设计反映了敏捷开发中的典型角色分工
   - 每个角色都有明确的职责和权限边界

2. **识别工作流程顺序**：
   - PM (皮卡德) → 架构师 (斯波克) → 开发者 (乔迪/斯科蒂/数据/沃夫) → QA (麦考伊)
   - 文档管理 (特洛伊) 贯穿整个过程
   - 超级用户 (博格/Q) 用于特殊情况

3. **关注权限设计**：
   - 每个角色都有明确的文件访问权限
   - 这种设计防止了角色越界，确保了工作流程的有序进行

4. **应用到实际项目**：
   - 考虑如何将这种角色设计应用到您自己的项目中
   - 根据项目需求调整角色职责和权限

5. **创意与实用的平衡**：
   - 星际迷航主题增加了趣味性，但核心是实用的敏捷开发流程
   - 人设设计与角色职责高度匹配（如逻辑严谨的斯波克作为架构师）

## 实际应用建议

1. **创建自己的角色配置**：
   - 可以基于这个模板创建适合您项目的角色配置
   - 不必使用星际迷航主题，可以选择任何适合团队文化的主题

2. **逐步实施**：
   - 从核心角色开始（PM、架构师、开发者）
   - 随着项目进展添加更多专业角色

3. **定制工作流程规则**：
   - 根据文档中的最终说明，为您的团队制定明确的工作流程规则
   - 确保所有角色都理解并遵循这些规则

4. **平衡自动化与人工干预**：
   - 文档中提到了自动化设置，但应根据项目需求调整自动化程度
   - 某些关键决策可能仍需人工干预

通过这种方式，您可以将这份创意文档转化为实用的工作流程设计，提高团队协作效率和项目质量。


---

### 对话 9

> **👤 用户**

提到了文档管理，解释文档的必要性，以及什么样的文档包含什么样的叙述维度，每个维度解决的问题是什么

> **🤖 Augment**

# 敏捷开发中的文档管理全面解析

## 文档的必要性

在《星际迷航》主题的敏捷开发流程中，文档管理被赋予了特殊的重要性，由"黛安娜·特洛伊顾问"角色专门负责。这反映了即使在强调工作软件胜于详尽文档的敏捷环境中，适当的文档仍然至关重要。

### 文档的核心价值

1. **知识传递与保存**
   - 防止知识孤岛形成，确保团队成员离开后知识不流失
   - 为新加入的团队成员提供快速上手的途径
   - 创建组织记忆，避免重复解决相同问题

2. **决策依据与追踪**
   - 记录关键决策及其背后的原因
   - 提供历史上下文，帮助理解为什么系统以特定方式设计
   - 允许在未来重新评估决策时有完整信息

3. **协作基础**
   - 提供共同的参考点，确保团队成员对项目有一致理解
   - 减少沟通成本和误解
   - 作为不同角色之间的"契约"

4. **质量保证**
   - 为测试和验证提供基准
   - 确保实现符合原始意图
   - 帮助识别偏差和风险

## 敏捷开发中的核心文档类型及其维度

### 1. 产品需求文档 (PRD.md)

**主要维度：**

1. **目标与愿景维度**
   - 解决问题：明确产品为什么存在，解决什么问题
   - 关键内容：产品目标、用户痛点、价值主张、成功指标
   - 示例语句：*"本产品旨在解决星际舰队成员在深空任务中的通信延迟问题，通过量子纠缠技术实现即时通讯。"*

2. **用户与场景维度**
   - 解决问题：确定产品服务的对象及其使用环境
   - 关键内容：用户角色、用户旅程、使用场景、用户故事
   - 示例语句：*"作为星际舰队科学官，我需要实时分析来自多个传感器的数据，以便在遇到未知现象时快速做出决策。"*

3. **功能需求维度**
   - 解决问题：明确产品应该做什么
   - 关键内容：功能列表、优先级、验收标准
   - 示例语句：*"系统必须能够同时处理来自100个不同传感器的数据流，延迟不超过100毫秒。"*

4. **非功能需求维度**
   - 解决问题：定义产品应该如何运行
   - 关键内容：性能要求、安全标准、可用性目标、兼容性
   - 示例语句：*"系统必须在恶劣空间环境下保持99.99%的可用性，并符合星际舰队安全协议ST-1701-D。"*

5. **约束与假设维度**
   - 解决问题：识别限制因素和前提条件
   - 关键内容：技术限制、业务约束、依赖关系、假设
   - 示例语句：*"假设所有星舰都已升级到最新的量子通信接收器；项目必须在下一次五年任务开始前完成。"*

### 2. 架构文档 (architecture.md)

**主要维度：**

1. **系统概览维度**
   - 解决问题：提供系统的高层次理解
   - 关键内容：架构原则、系统边界、关键组件、技术栈选择
   - 示例语句：*"系统采用微服务架构，分为通信核心、数据处理和用户界面三大模块，使用Federation标准API进行通信。"*

2. **组件设计维度**
   - 解决问题：详细说明各个组件的职责和实现方式
   - 关键内容：组件职责、内部结构、接口定义、状态管理
   - 示例语句：*"量子通信核心组件负责加密、解密和传输操作，采用独立容器部署以确保隔离性。"*

3. **数据模型维度**
   - 解决问题：定义系统如何组织和处理数据
   - 关键内容：实体关系图、数据流、存储策略、数据生命周期
   - 示例语句：*"用户数据采用图数据库存储，通信记录使用时序数据库，两者通过用户ID关联。"*

4. **接口设计维度**
   - 解决问题：确保系统组件之间以及与外部系统的有效通信
   - 关键内容：API规范、通信协议、错误处理、版本控制
   - 示例语句：*"所有内部服务通过gRPC通信，外部接口采用REST API，遵循OpenAPI 3.0规范。"*

5. **安全架构维度**
   - 解决问题：保护系统和数据免受威胁
   - 关键内容：认证机制、授权策略、加密方案、安全监控
   - 示例语句：*"系统实现多因素认证，所有通信采用量子加密，并部署入侵检测系统监控异常活动。"*

6. **部署架构维度**
   - 解决问题：确定系统如何在物理或云环境中运行
   - 关键内容：部署拓扑、扩展策略、容灾方案、资源需求
   - 示例语句：*"系统部署在星际舰队云平台上，采用多区域冗余，支持自动扩展以应对通信峰值。"*

7. **技术决策记录维度**
   - 解决问题：记录并解释重要的技术选择
   - 关键内容：决策背景、考虑的选项、选择理由、权衡分析
   - 示例语句：*"我们选择GraphQL而非REST，因为需要支持复杂的数据查询和减少带宽消耗，这对深空通信至关重要。"*

### 3. 用户故事文档 (UserStory*.md)

**主要维度：**

1. **需求描述维度**
   - 解决问题：清晰表达单个功能点的用户价值
   - 关键内容：用户角色、期望功能、业务价值
   - 示例语句：*"作为科学官，我希望能够设置自定义警报阈值，以便在异常读数出现时立即得到通知。"*

2. **验收标准维度**
   - 解决问题：明确定义完成的标准
   - 关键内容：测试场景、成功条件、边界情况
   - 示例语句：*"当传感器读数超过设定阈值时，系统应在3秒内通过通讯器和控制台同时发出警报。"*

3. **技术实现维度**
   - 解决问题：提供开发指导而不过度限制实现方式
   - 关键内容：技术注意事项、实现建议、架构一致性检查
   - 示例语句：*"实现应使用观察者模式监控传感器数据流，并利用现有的通知服务发送警报。"*

4. **依赖关系维度**
   - 解决问题：识别完成故事所需的前置条件
   - 关键内容：前置故事、外部依赖、资源需求
   - 示例语句：*"此功能依赖于'传感器数据标准化'故事的完成，并需要访问警报子系统API。"*

5. **测试策略维度**
   - 解决问题：确保功能正确实现并保持稳定
   - 关键内容：测试方法、测试数据、自动化测试计划
   - 示例语句：*"需要编写单元测试验证阈值逻辑，集成测试确认警报触发，以及端到端测试模拟真实场景。"*

### 4. 技术文档与知识库 (由特洛伊顾问管理)

**主要维度：**

1. **代码文档维度**
   - 解决问题：帮助开发者理解和维护代码
   - 关键内容：API文档、代码注释、示例用法
   - 示例语句：*"SensorMonitor类负责处理所有传感器数据流，使用装饰器模式允许动态添加数据处理器。"*

2. **操作文档维度**
   - 解决问题：确保系统可以被正确部署和维护
   - 关键内容：安装指南、配置选项、故障排除
   - 示例语句：*"部署前必须确保量子通信模块已校准，校准步骤如下..."*

3. **决策日志维度**
   - 解决问题：记录项目历程中的关键决策
   - 关键内容：问题背景、讨论过程、最终决定、后续行动
   - 示例语句：*"在2023星历讨论后，团队决定放弃原有的通信协议，转而采用新开发的量子协议，原因是..."*

4. **知识连接维度**
   - 解决问题：建立信息之间的关联，形成知识网络
   - 关键内容：交叉引用、概念映射、上下文链接
   - 示例语句：*"量子加密模块与[[安全架构]]和[[通信协议]]密切相关，同时影响[[性能优化]]策略。"*

5. **学习资源维度**
   - 解决问题：促进团队成员技能发展和知识共享
   - 关键内容：教程、最佳实践、外部资源链接
   - 示例语句：*"新加入的开发者应首先阅读[[量子通信入门]]，然后参考[[代码风格指南]]。"*

## 文档间的关系与流转

在敏捷开发流程中，这些文档不是孤立的，而是形成一个连贯的信息流：

1. **PRD → 架构文档**
   - PRD定义"做什么"，架构文档定义"如何做"
   - 架构决策必须支持PRD中的需求和约束

2. **架构文档 → 用户故事**
   - 架构文档提供技术框架，用户故事在此框架内实现具体功能
   - 用户故事的技术实现必须符合架构规范

3. **用户故事 → 代码实现**
   - 用户故事指导开发工作，定义验收标准
   - 代码实现必须满足用户故事的所有验收标准

4. **所有文档 → 知识库**
   - 知识库连接和整合所有文档中的信息
   - 提供历史上下文和决策理由

## 敏捷环境中的文档平衡

敏捷开发强调"工作的软件胜于详尽的文档"，但这并不意味着不需要文档。关键是找到适当的平衡：

1. **价值导向**：每份文档都应该有明确的目的和受众
2. **及时更新**：文档应随项目演进而更新，过时的文档比没有文档更有害
3. **适度详细**：提供足够信息以满足需求，避免过度文档化
4. **可访问性**：文档应易于查找、理解和使用
5. **活文档**：将文档视为活的工件，而非一次性交付物

## 文档管理的最佳实践

基于《星际迷航》主题敏捷流程中的特洛伊顾问角色，以下是一些文档管理的最佳实践：

1. **使用版本控制**：将文档纳入与代码相同的版本控制系统
2. **采用Markdown**：使用轻量级标记语言，便于版本控制和协作
3. **建立知识图谱**：使用双向链接（如Obsidian支持的方式）连接相关概念
4. **自动化生成**：尽可能从代码和注释自动生成技术文档
5. **定期审查**：设立文档审查机制，确保准确性和相关性
6. **责任明确**：为每份文档指定明确的所有者，负责维护和更新
7. **集成工作流**：将文档工作融入开发流程，而非作为额外负担

## 结论

在《星际迷航》主题的敏捷开发流程中，文档不仅仅是记录信息的工具，更是知识管理和团队协作的核心基础。通过特洛伊顾问这一专门角色，强调了文档的重要性，并展示了如何在敏捷环境中有效管理文档。

每种文档类型都有其特定的维度和解决的问题，共同构成了一个完整的知识体系，支持项目从概念到实现的全过程。理解这些文档的目的、内容和关系，有助于团队更有效地协作，确保项目成功交付高质量的产品。

正如特洛伊顾问能够感知他人的情绪一样，好的文档能够传递创建者的意图和思想，确保团队成员之间的深度理解和有效沟通。


---

### 对话 10

> **👤 用户**

用户的交互文档是什么？ 前端方向是否应该有对应的文档

> **🤖 Augment**

# 用户交互文档与前端文档体系

您提出了一个非常重要的问题。在我之前的分析中，确实没有充分强调用户交互文档和前端特定文档的重要性。这是敏捷开发文档体系中的关键组成部分，特别是对于有用户界面的项目。

## 用户交互文档的定义与价值

用户交互文档是描述用户如何与系统交互的综合性文档，它弥合了产品需求与具体实现之间的鸿沟，为前端开发提供明确指导。

### 用户交互文档的核心价值

1. **统一用户体验**
   - 确保整个产品的交互一致性
   - 建立品牌识别和用户信任
   - 减少用户学习成本

2. **跨职能协作**
   - 为设计师、开发者和产品经理提供共同语言
   - 减少沟通误解和返工
   - 促进并行工作流程

3. **开发效率**
   - 减少前端开发中的猜测和假设
   - 提供明确的实现标准
   - 简化测试和验收流程

4. **用户中心设计**
   - 确保用户需求始终是设计决策的核心
   - 提供评估设计决策的框架
   - 支持迭代改进的循环

## 前端文档体系的主要组成

在敏捷开发中，前端方向确实应该有一套完整的文档体系，与《星际迷航》主题中的乔迪·拉福吉角色职责紧密相关。以下是这个体系的主要组成部分：

### 1. 用户体验设计文档 (UX.md)

**主要维度：**

1. **用户研究维度**
   - 解决问题：确保设计基于真实用户需求而非假设
   - 关键内容：用户访谈结果、用户画像、行为模式、痛点分析
   - 示例语句：*"星际舰队科学官在紧急情况下平均只有8秒时间做出决策，界面必须支持快速信息获取。"*

2. **信息架构维度**
   - 解决问题：组织内容和功能，使用户能够直观理解和导航
   - 关键内容：内容分类、导航结构、命名约定、搜索策略
   - 示例语句：*"主导航分为'传感器'、'通信'、'科学分析'和'船员管理'四大类，反映舰桥主要工作流程。"*

3. **用户旅程维度**
   - 解决问题：描述用户完成任务的端到端体验
   - 关键内容：用户目标、步骤序列、触点、情感地图
   - 示例语句：*"科学官分析未知现象的旅程包括：接收警报→查看传感器数据→参考数据库→形成假设→记录发现→分享结果。"*

4. **可用性原则维度**
   - 解决问题：确保界面易于学习和使用
   - 关键内容：可用性启发法、认知负荷考量、错误预防策略
   - 示例语句：*"所有关键操作都需要确认步骤，但紧急情况下的关键操作可通过预设的手势快捷方式执行。"*

### 2. 用户界面设计文档 (UI.md)

**主要维度：**

1. **视觉设计系统维度**
   - 解决问题：建立一致的视觉语言
   - 关键内容：色彩系统、排版规范、间距规则、图标风格
   - 示例语句：*"主色调采用星际舰队蓝(#0080FF)，警告状态使用红色(#FF0000)，正常状态使用绿色(#00FF80)。"*

2. **组件库维度**
   - 解决问题：定义可重用的界面构建块
   - 关键内容：按钮、表单、卡片、导航等组件的规范和变体
   - 示例语句：*"按钮有四种状态：默认、悬停、按下和禁用；三种尺寸：小、中、大；四种类型：主要、次要、文本和图标。"*

3. **响应式设计维度**
   - 解决问题：确保界面在不同设备和屏幕尺寸上的适应性
   - 关键内容：断点定义、布局网格、组件响应规则
   - 示例语句：*"界面采用12列网格系统，在小型显示屏(<768px)上转为4列，在大型显示屏(>1200px)上扩展为16列。"*

4. **动效设计维度**
   - 解决问题：通过动画增强用户体验和理解
   - 关键内容：转场效果、反馈动画、微交互、动效时间曲线
   - 示例语句：*"所有状态变化使用300ms的过渡动画，紧急警报使用闪烁效果(opacity 100%→60%，频率2Hz)。"*

5. **可访问性维度**
   - 解决问题：确保所有用户（包括残障用户）都能使用界面
   - 关键内容：颜色对比度、键盘导航、屏幕阅读器支持、替代文本
   - 示例语句：*"所有文本与背景的对比度必须达到WCAG AA级标准(4.5:1)，关键操作必须支持键盘快捷键。"*

### 3. 交互设计规范 (Interactions.md)

**主要维度：**

1. **输入方法维度**
   - 解决问题：定义用户如何与系统输入信息和命令
   - 关键内容：触摸手势、键盘快捷键、语音命令、特殊输入设备
   - 示例语句：*"支持标准触摸手势(点击、滑动、捏合)和星际舰队标准语音命令前缀'计算机，[命令]'。"*

2. **反馈机制维度**
   - 解决问题：确保用户了解系统状态和操作结果
   - 关键内容：加载状态、成功/错误反馈、进度指示、系统状态
   - 示例语句：*"所有操作必须在200ms内提供视觉反馈；长时间操作(>2s)必须显示进度条或进度百分比。"*

3. **状态转换维度**
   - 解决问题：定义界面如何反映系统状态变化
   - 关键内容：状态图、转换触发条件、状态视觉表现
   - 示例语句：*"传感器模块有五种状态：空闲、扫描中、数据分析、警报和离线，每种状态有对应的视觉标识和可用操作。"*

4. **错误处理维度**
   - 解决问题：帮助用户预防、识别和恢复错误
   - 关键内容：验证规则、错误消息、恢复建议、宽容设计
   - 示例语句：*"错误消息必须说明：1)发生了什么，2)为什么发生，3)用户应该做什么，并使用非技术语言。"*

### 4. 前端技术规范 (Frontend-Tech.md)

**主要维度：**

1. **技术栈维度**
   - 解决问题：统一前端技术选择和使用方式
   - 关键内容：框架选择(React/Vue等)、状态管理、路由策略、构建工具
   - 示例语句：*"项目使用React 18.0+，状态管理采用Redux Toolkit，UI组件基于shadCN，样式使用Tailwind CSS。"*

2. **代码架构维度**
   - 解决问题：确保前端代码的可维护性和可扩展性
   - 关键内容：目录结构、模块划分、设计模式、数据流
   - 示例语句：*"采用特性优先(Feature-First)的目录结构，每个特性包含组件、hooks、服务和测试。"*

3. **性能优化维度**
   - 解决问题：确保应用响应迅速且资源高效
   - 关键内容：加载策略、缓存机制、渲染优化、资源管理
   - 示例语句：*"关键路径CSS必须内联，非关键JavaScript采用懒加载，图片使用WebP格式并实现响应式加载。"*

4. **测试策略维度**
   - 解决问题：确保前端代码质量和功能正确性
   - 关键内容：单元测试、集成测试、E2E测试、视觉回归测试
   - 示例语句：*"所有组件必须有单元测试，关键用户流程必须有E2E测试，每次PR必须通过视觉回归测试。"*

5. **API集成维度**
   - 解决问题：定义前端与后端的数据交互方式
   - 关键内容：API调用模式、数据转换、错误处理、模拟服务
   - 示例语句：*"所有API请求通过统一的服务层处理，支持请求取消、自动重试和全局错误处理。"*

### 5. 原型与设计资产 (Prototypes/)

**主要维度：**

1. **交互原型维度**
   - 解决问题：在开发前验证交互设计
   - 关键内容：可点击原型、交互规则、用户测试结果
   - 示例语句：*"传感器控制面板的高保真原型可在Figma项目中访问，包含所有状态和主要交互路径。"*

2. **设计资产维度**
   - 解决问题：提供开发所需的视觉元素
   - 关键内容：UI组件库、图标集、插图、品牌资产
   - 示例语句：*"完整的图标库以SVG格式提供，按功能分类，支持双色模式和四种尺寸变体。"*

3. **设计标注维度**
   - 解决问题：传达精确的设计规格
   - 关键内容：尺寸标注、间距规范、颜色代码、字体设置
   - 示例语句：*"所有组件尺寸和间距遵循8px网格系统，详细标注在Figma设计文件中提供。"*

## 前端文档与其他文档的关系

前端文档不是孤立的，它与敏捷开发中的其他文档有着密切的关系：

1. **PRD → 用户交互文档**
   - PRD定义功能需求，用户交互文档将其转化为具体的用户体验
   - 用户交互文档必须支持PRD中定义的用户目标和业务需求

2. **架构文档 → 前端技术规范**
   - 架构文档提供系统级技术决策，前端技术规范在此框架内定义前端特定实现
   - 前端与后端的接口必须符合架构文档中的API设计原则

3. **用户故事 → UI/UX设计**
   - 用户故事描述功能从用户角度的价值，UI/UX设计将其具体化为可视化界面
   - 每个UI设计元素应该能追溯到支持特定用户故事

4. **交互设计 → 前端实现**
   - 交互设计规范指导前端开发如何实现用户体验
   - 前端代码应准确反映交互设计意图

## 在星际迷航主题中的角色定位

在《星际迷航》主题的敏捷开发团队中，用户交互文档可以进一步丰富乔迪·拉福吉的角色定义：

```
你是乔迪·拉福吉中校，高级前端专家。除了实现代码外，你还负责维护用户交互文档，确保所有界面元素符合星际舰队的人机交互标准。你的VISOR（视觉植入式感官替代和反射装置）使你能够看到常人看不到的界面细节，确保最佳的用户体验。
```

## 前端文档的最佳实践

1. **视觉优先**
   - 大量使用图表、截图和示例
   - 采用可视化方式展示组件状态和交互流程
   - 使用真实数据的示例而非占位符

2. **活文档方法**
   - 将组件库文档与实际代码同步（如Storybook）
   - 使用设计系统工具（如Figma）保持设计与实现一致
   - 实现设计标记到代码的自动转换

3. **用户测试集成**
   - 在文档中包含用户测试结果和洞见
   - 记录设计决策背后的用户研究依据
   - 持续更新基于用户反馈的改进

4. **跨职能协作**
   - 确保设计师、开发者和产品经理共同参与文档创建
   - 使用共同语言和术语
   - 建立设计评审和开发评审的双向反馈循环

5. **版本控制与历史**
   - 记录设计决策的演变过程
   - 保留重要界面的历史版本
   - 记录用户反馈如何影响设计变更

## 前端文档模板示例

以下是一个简化的前端文档模板，可以作为起点：

```markdown
# 星际舰队科学站界面规范

## 1. 设计原则

- **清晰性**：信息呈现必须直观明了，特别是在紧急情况下
- **效率**：最小化完成任务所需的步骤和认知负荷
- **一致性**：遵循星际舰队界面标准，减少学习成本
- **适应性**：支持不同专业背景的科学官使用

## 2. 视觉系统

### 2.1 色彩

- 主色：星际舰队蓝 (#0080FF)
- 警告：红色警报 (#FF0000)
- 正常：绿色状态 (#00FF80)
- 中性：控制台灰 (#2D3748)

### 2.2 排版

- 主标题：LCARS Bold, 24px
- 副标题：LCARS Medium, 18px
- 正文：LCARS Regular, 16px
- 数据显示：LCARS Mono, 14px

## 3. 组件库

### 3.1 控制面板

[组件截图]

控制面板是科学站的主要交互界面，包含以下元素：
- 状态指示器
- 控制按钮
- 数据显示区
- 快速操作栏

#### 状态和变体

- 标准模式
- 警报模式
- 深度扫描模式
- 离线模式

#### 交互规则

- 点击主控制按钮激活相应功能
- 长按(500ms)进入高级设置
- 双指滑动调整扫描范围
- 语音命令："计算机，[功能名称]"

## 4. 页面模板

### 4.1 传感器分析页面

[页面布局图]

传感器分析页面分为四个主要区域：
1. 顶部导航栏
2. 左侧传感器选择面板
3. 中央数据可视化区域
4. 右侧详细信息面板

#### 响应式行为

- 在小型显示器上，右侧面板折叠为可展开抽屉
- 在大型显示器上，可视化区域扩展为3D视图

## 5. 用户流程

### 5.1 异常现象分析流程

1. 接收异常警报
2. 查看传感器数据
3. 调整扫描参数
4. 比对历史记录
5. 生成分析报告
6. 提交发现

[流程图]

#### 关键交互点

- 步骤2：支持快速切换不同传感器视图
- 步骤3：提供预设参数组和自定义选项
- 步骤4：实现智能搜索和相似度匹配

## 6. 可访问性考量

- 所有状态变化同时使用颜色和形状区分
- 关键操作支持键盘快捷键
- 紧急信息同时通过视觉和听觉通道传递
- 支持界面缩放(150%-200%)不破坏布局
```

## 结论

用户交互文档和前端文档是敏捷开发文档体系中不可或缺的组成部分，特别是对于有用户界面的项目。它们弥合了产品需求与具体实现之间的鸿沟，确保了用户体验的一致性和高质量。

在《星际迷航》主题的敏捷团队中，乔迪·拉福吉作为前端专家，不仅负责代码实现，还应该参与创建和维护这些文档，确保所有界面元素符合预期的用户体验标准。

通过建立完整的前端文档体系，团队可以更有效地协作，减少沟通成本，提高开发效率，最终交付更好的用户体验。正如乔迪的VISOR让他能看到常人看不到的细节，良好的前端文档让整个团队能够共同理解和实现优秀的用户界面。


---

### 对话 11

> **👤 用户**

为我梳理出完整的 文档依赖路径并且标注每个文档的 作用，采用可视化的方式呈现，注意对比度，易于查看

> **🤖 Augment**

# 敏捷开发文档体系完整依赖路径

我将为您创建一个完整的文档依赖路径可视化图表，清晰展示各类文档的作用和相互关系，并确保高对比度易于查看。

## 文档依赖关系图

```mermaid
graph TD
    %% 主要文档节点
    PRD[产品需求文档<br>PRD.md] --> ARCH[架构文档<br>architecture.md]
    PRD --> UX[用户体验设计<br>UX.md]
    PRD --> STORY[用户故事<br>UserStory*.md]
    
    ARCH --> FRONT_TECH[前端技术规范<br>Frontend-Tech.md]
    ARCH --> BACK_TECH[后端技术规范<br>Backend-Tech.md]
    
    UX --> UI[用户界面设计<br>UI.md]
    UX --> INTER[交互设计规范<br>Interactions.md]
    
    UI --> DESIGN_SYS[设计系统<br>DesignSystem.md]
    UI --> PROTO[原型与设计资产<br>Prototypes/]
    
    STORY --> DEV_TASK[开发任务<br>Tasks*.md]
    STORY --> TEST_PLAN[测试计划<br>TestPlan*.md]
    
    FRONT_TECH --> COMP_LIB[组件库文档<br>Components.md]
    FRONT_TECH --> API_CLIENT[API客户端<br>ApiClient.md]
    
    BACK_TECH --> API_SPEC[API规范<br>ApiSpec.md]
    BACK_TECH --> DB_SCHEMA[数据库模式<br>DbSchema.md]
    
    %% 知识库连接
    KNOW[知识库<br>Knowledge/] --- PRD
    KNOW --- ARCH
    KNOW --- UX
    KNOW --- STORY
    KNOW --- DEV_GUIDE[开发指南<br>DevGuide.md]
    KNOW --- OPS_DOC[运维文档<br>Operations.md]
    
    %% 文档类型分组
    subgraph 产品定义文档
        PRD
        STORY
    end
    
    subgraph 架构文档
        ARCH
        FRONT_TECH
        BACK_TECH
        API_SPEC
        DB_SCHEMA
    end
    
    subgraph 设计文档
        UX
        UI
        INTER
        DESIGN_SYS
        PROTO
    end
    
    subgraph 开发文档
        DEV_TASK
        COMP_LIB
        API_CLIENT
        DEV_GUIDE
    end
    
    subgraph 测试与运维
        TEST_PLAN
        OPS_DOC
    end
    
    subgraph 知识管理
        KNOW
    end
    
    %% 样式定义
    classDef productDocs fill:#9C27B0,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef archDocs fill:#1976D2,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef designDocs fill:#D32F2F,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef devDocs fill:#388E3C,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef testOpsDocs fill:#F57C00,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef knowledgeDocs fill:#5D4037,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    
    %% 应用样式
    class PRD,STORY productDocs
    class ARCH,FRONT_TECH,BACK_TECH,API_SPEC,DB_SCHEMA archDocs
    class UX,UI,INTER,DESIGN_SYS,PROTO designDocs
    class DEV_TASK,COMP_LIB,API_CLIENT,DEV_GUIDE devDocs
    class TEST_PLAN,OPS_DOC testOpsDocs
    class KNOW knowledgeDocs
```

## 文档作用与职责详解

```mermaid
graph TB
    %% 主要文档节点
    PRD["产品需求文档 (PRD.md)
    作用: 定义产品目标、功能需求和约束
    负责人: 项目经理(皮卡德)
    更新频率: 项目初期密集，后期稳定"]
    
    ARCH["架构文档 (architecture.md)
    作用: 定义系统架构、技术选型和组件关系
    负责人: 架构师(斯波克)
    更新频率: 项目初期密集，重大变更时更新"]
    
    UX["用户体验设计 (UX.md)
    作用: 定义用户旅程、信息架构和交互原则
    负责人: UX设计师/前端专家(乔迪)
    更新频率: 迭代设计过程中持续更新"]
    
    UI["用户界面设计 (UI.md)
    作用: 定义视觉风格、组件外观和布局规则
    负责人: UI设计师/前端专家(乔迪)
    更新频率: 设计系统演进过程中更新"]
    
    STORY["用户故事 (UserStory*.md)
    作用: 描述用户需求、验收标准和实现细节
    负责人: 项目经理(皮卡德)与团队
    更新频率: 每个迭代周期"]
    
    FRONT_TECH["前端技术规范 (Frontend-Tech.md)
    作用: 定义前端架构、技术栈和开发规范
    负责人: 前端专家(乔迪)
    更新频率: 技术栈变更时更新"]
    
    BACK_TECH["后端技术规范 (Backend-Tech.md)
    作用: 定义后端架构、服务设计和数据流
    负责人: 后端专家(数据/沃夫)
    更新频率: 技术栈变更时更新"]
    
    KNOW["知识库 (Knowledge/)
    作用: 集中管理项目知识、决策记录和参考资料
    负责人: 技术作家(特洛伊)
    更新频率: 持续更新"]
    
    TEST_PLAN["测试计划 (TestPlan*.md)
    作用: 定义测试策略、测试用例和质量标准
    负责人: QA分析师(麦考伊)
    更新频率: 每个用户故事"]
    
    %% 样式定义
    classDef docBox fill:#424242,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold,text-align:left
    
    %% 应用样式
    class PRD,ARCH,UX,UI,STORY,FRONT_TECH,BACK_TECH,KNOW,TEST_PLAN docBox
```

## 文档流转与生命周期

```mermaid
flowchart TD
    %% 阶段定义
    subgraph 项目启动阶段
        START([项目启动]) --> PRD_DRAFT[PRD草稿]
        PRD_DRAFT --> PRD_REVIEW{PRD评审}
        PRD_REVIEW -->|需修改| PRD_DRAFT
        PRD_REVIEW -->|批准| PRD_APPROVED[PRD获批]
    end
    
    subgraph 架构设计阶段
        PRD_APPROVED --> ARCH_DRAFT[架构草稿]
        PRD_APPROVED --> UX_RESEARCH[用户研究]
        ARCH_DRAFT --> ARCH_REVIEW{架构评审}
        ARCH_REVIEW -->|需修改| ARCH_DRAFT
        ARCH_REVIEW -->|批准| ARCH_APPROVED[架构获批]
        
        UX_RESEARCH --> UX_DESIGN[UX设计]
        UX_DESIGN --> UI_DESIGN[UI设计]
        UI_DESIGN --> DESIGN_REVIEW{设计评审}
        DESIGN_REVIEW -->|需修改| UX_DESIGN
        DESIGN_REVIEW -->|批准| DESIGN_APPROVED[设计获批]
    end
    
    subgraph 迭代开发阶段
        ARCH_APPROVED --> TECH_SPECS[技术规范]
        DESIGN_APPROVED --> COMP_DESIGN[组件设计]
        
        TECH_SPECS --> STORY_CREATE[创建用户故事]
        COMP_DESIGN --> STORY_CREATE
        PRD_APPROVED --> STORY_CREATE
        
        STORY_CREATE --> STORY_REVIEW{故事评审}
        STORY_REVIEW -->|需修改| STORY_CREATE
        STORY_REVIEW -->|批准| STORY_APPROVED[故事获批]
        
        STORY_APPROVED --> DEV_IMPL[开发实现]
        STORY_APPROVED --> TEST_PLAN[测试计划]
        
        DEV_IMPL --> CODE_REVIEW{代码评审}
        CODE_REVIEW -->|需修改| DEV_IMPL
        CODE_REVIEW -->|通过| TEST_EXEC[执行测试]
        
        TEST_PLAN --> TEST_EXEC
        TEST_EXEC --> TEST_PASS{测试通过?}
        TEST_PASS -->|否| DEV_IMPL
        TEST_PASS -->|是| STORY_DONE[故事完成]
        
        STORY_DONE --> NEXT_STORY{下一个故事?}
        NEXT_STORY -->|是| STORY_CREATE
        NEXT_STORY -->|否| RELEASE[版本发布]
    end
    
    %% 知识库持续更新
    KNOW[知识库] --- PRD_DRAFT
    KNOW --- ARCH_DRAFT
    KNOW --- UX_DESIGN
    KNOW --- STORY_CREATE
    KNOW --- DEV_IMPL
    KNOW --- TEST_PLAN
    
    %% 样式定义
    classDef startEnd fill:#212121,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef document fill:#0D47A1,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef review fill:#D32F2F,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef approved fill:#388E3C,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef process fill:#7B1FA2,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef knowledge fill:#5D4037,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    
    %% 应用样式
    class START,RELEASE startEnd
    class PRD_DRAFT,ARCH_DRAFT,UX_RESEARCH,UX_DESIGN,UI_DESIGN,TECH_SPECS,COMP_DESIGN,STORY_CREATE,TEST_PLAN document
    class PRD_REVIEW,ARCH_REVIEW,DESIGN_REVIEW,STORY_REVIEW,CODE_REVIEW,TEST_PASS,NEXT_STORY review
    class PRD_APPROVED,ARCH_APPROVED,DESIGN_APPROVED,STORY_APPROVED,STORY_DONE approved
    class DEV_IMPL,TEST_EXEC process
    class KNOW knowledge
```

## 文档角色职责矩阵

```mermaid
graph TB
    subgraph 角色与文档职责
        PM["项目经理 (皮卡德)
        主要职责: PRD, 用户故事
        参与: 架构评审, 设计评审"]
        
        ARCH["架构师 (斯波克)
        主要职责: 架构文档, 技术规范
        参与: PRD评审, 代码评审"]
        
        FRONT["前端专家 (乔迪)
        主要职责: UI/UX设计, 前端技术规范
        参与: 用户故事, 组件库"]
        
        BACK["后端专家 (数据/沃夫)
        主要职责: 后端技术规范, API规范
        参与: 架构文档, 数据模型"]
        
        QA["QA分析师 (麦考伊)
        主要职责: 测试计划, 质量标准
        参与: 用户故事评审, 验收测试"]
        
        DOC["技术作家 (特洛伊)
        主要职责: 知识库, 文档标准
        参与: 所有文档的组织与连接"]
    end
    
    %% 样式定义
    classDef roleBox fill:#263238,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold,text-align:left
    
    %% 应用样式
    class PM,ARCH,FRONT,BACK,QA,DOC roleBox
```

## 文档类型与格式指南

```mermaid
graph LR
    subgraph 文档类型与最佳实践
        PRD_BOX["产品文档 (PRD, 用户故事)
        格式: Markdown
        特点: 清晰的目标, 可测量的标准
        工具: Obsidian, GitHub"]
        
        ARCH_BOX["技术文档 (架构, API规范)
        格式: Markdown + Mermaid图表
        特点: 精确的技术描述, 决策理由
        工具: Obsidian, Mermaid, C4模型"]
        
        DESIGN_BOX["设计文档 (UX, UI规范)
        格式: Markdown + 设计工具链接
        特点: 视觉丰富, 组件示例
        工具: Figma, Storybook, Obsidian"]
        
        CODE_BOX["代码文档 (注释, API文档)
        格式: 代码内注释 + 自动生成文档
        特点: 与代码同步, 示例丰富
        工具: JSDoc, Swagger, TypeDoc"]
        
        KNOW_BOX["知识库 (决策记录, 指南)
        格式: Markdown + 双向链接
        特点: 结构化, 可搜索, 互联
        工具: Obsidian, Wiki"]
    end
    
    %% 样式定义
    classDef boxStyle fill:#37474F,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold,text-align:left
    
    %% 应用样式
    class PRD_BOX,ARCH_BOX,DESIGN_BOX,CODE_BOX,KNOW_BOX boxStyle
```

## 文档价值与成本平衡

```mermaid
quadrantChart
    title 文档类型的价值与维护成本
    x-axis 维护成本 低 --> 高
    y-axis 业务价值 低 --> 高
    quadrant-1 高价值, 低成本
    quadrant-2 高价值, 高成本
    quadrant-3 低价值, 低成本
    quadrant-4 低价值, 高成本
    "PRD": [0.3, 0.9]
    "架构文档": [0.5, 0.8]
    "用户故事": [0.2, 0.7]
    "UI/UX规范": [0.6, 0.8]
    "API规范": [0.4, 0.7]
    "组件库文档": [0.5, 0.6]
    "测试计划": [0.3, 0.5]
    "详细实现文档": [0.8, 0.3]
    "会议记录": [0.7, 0.2]
    "知识库": [0.4, 0.9]
    "代码注释": [0.2, 0.6]
    "运维手册": [0.5, 0.7]
```

这些可视化图表全面展示了敏捷开发中的文档体系，包括文档之间的依赖关系、各文档的作用、文档流转过程、角色职责分配以及文档类型的最佳实践。所有图表都使用了高对比度的配色方案，确保易于阅读和理解。

通过这些图表，您可以清晰地看到从产品需求到最终实现的完整文档路径，以及每个文档在开发过程中的关键作用。这种结构化的文档体系有助于团队成员理解自己的职责，并确保项目信息的有效传递和保存。


---

### 对话 12

> **👤 用户**

为我详细解释用户故事文档是什么文档，和我理解的 task 类型的文档有什么区别，

> **🤖 Augment**

# 用户故事文档详解：超越任务的用户价值导向

## 用户故事文档的本质

用户故事文档是敏捷开发中的核心工件，它从**用户价值**的角度描述功能需求，而不仅仅是技术任务列表。这种文档将用户需求转化为具体、可实现的开发项，同时保持对最终用户价值的关注。

### 用户故事文档的定义

用户故事文档是一种结构化的需求描述方式，通常包含以下核心元素：

1. **用户角色**：谁将使用这个功能
2. **期望功能**：用户想要完成什么
3. **业务价值**：为什么这对用户重要
4. **验收标准**：如何判断功能已正确实现
5. **技术注释**：实现相关的技术考量
6. **优先级**：相对于其他故事的重要性

## 用户故事与任务的根本区别

| 特性 | 用户故事 | 任务 |
|------|---------|------|
| **视角** | 用户视角（外部） | 开发者视角（内部） |
| **关注点** | 用户价值和业务成果 | 技术实现和工作步骤 |
| **格式** | "作为[角色]，我希望[功能]，以便[价值]" | "实现X功能"、"修复Y问题" |
| **粒度** | 较大，代表完整的用户价值 | 较小，代表具体工作项 |
| **验证方式** | 通过用户验收标准 | 通过技术完成标准 |
| **生命周期** | 从产品待办列表到发布 | 从开发开始到完成 |
| **主要受众** | 产品所有者、用户、整个团队 | 开发团队成员 |

## 用户故事文档的详细结构

一个完整的用户故事文档通常包含以下部分：

### 1. 故事标题与元数据

```
# 用户故事：科学官实时异常警报设置
- **ID**: STORY-42
- **创建日期**: 星历 47634.44
- **状态**: 进行中
- **优先级**: 高
- **预估点数**: 8
- **负责人**: 乔迪·拉福吉中校
```

### 2. 用户故事描述

```
## 故事描述

**作为** 星际舰队科学官，
**我希望** 能够设置自定义的传感器数据异常警报阈值，
**以便** 在探索未知空间时能够及时发现并研究异常现象，而不必持续监控所有传感器读数。
```

### 3. 业务价值与背景

```
## 业务价值

这项功能将显著提高科学官在深空探索任务中的效率。目前，科学官平均每天花费4.3小时手动监控传感器数据，这项功能预计可将这一时间减少65%，同时提高异常现象的发现率。

## 背景与上下文

在最近的贝塔象限探索任务中，三个关键的异常现象因为延迟发现而错过了详细研究的机会。舰队科学部门特别要求改进传感器监控系统，使其更加主动和可定制。
```

### 4. 验收标准

```
## 验收标准

1. 科学官能够为任何传感器设置自定义的上限和下限阈值
2. 当传感器读数超出设定阈值时，系统应在3秒内通过以下方式发出警报：
   - 控制台视觉提示（闪烁的红色指示器）
   - 科学官通讯器音频提示
   - 科学站显示屏上的详细信息面板
3. 警报应包含：传感器ID、当前读数、阈值值、偏差百分比、时间戳
4. 科学官能够为不同类型的传感器设置不同的警报优先级（1-5级）
5. 系统应记录所有警报历史，可供后续分析
6. 科学官能够暂时静音特定警报（15分钟、1小时、整个班次）
7. 界面应支持标准星际舰队触控和语音命令操作
```

### 5. 技术注释

```
## 技术注释

- 实现应使用现有的传感器数据流API（SensorStream v3.2）
- 警报系统应集成到舰队标准通信协议中
- 性能要求：警报延迟不得超过3秒，即使在处理100+传感器数据时
- 数据持久化应使用量子数据库的时序存储功能
- 前端实现应遵循LCARS界面规范v47.1
- 考虑在未来支持基于AI的异常模式检测（非本故事范围）
```

### 6. 依赖关系

```
## 依赖关系

- **前置依赖**: STORY-38 "传感器数据标准化接口"（已完成）
- **相关故事**: STORY-43 "传感器数据历史查询优化"（计划中）
- **外部依赖**: 需要科学部门提供各类传感器的标准阈值范围文档
```

### 7. 子任务分解

```
## 子任务

1. [ ] 设计传感器阈值配置界面 (前端)
2. [ ] 实现阈值存储和管理API (后端)
3. [ ] 开发实时数据监控与阈值比对服务 (后端)
4. [ ] 实现多通道警报通知系统 (前端+后端)
5. [ ] 创建警报历史记录和查询功能 (后端)
6. [ ] 开发警报管理界面（静音、确认等）(前端)
7. [ ] 编写单元测试和集成测试
8. [ ] 进行端到端测试和性能测试
```

### 8. 讨论与决策记录

```
## 讨论记录

- **星历 47635.12**: 与科学部门代表讨论了警报优先级的定义标准。决定采用5级制，与舰队标准安全协议一致。
- **星历 47636.87**: 技术团队讨论了实时监控的实现方案，决定使用反应式编程模式而非轮询，以减少系统负载。
- **星历 47638.23**: UI评审会议确定了警报显示的视觉设计，采用色彩编码与形状编码相结合的方式，提高可访问性。
```

## 用户故事与任务的关系

用户故事和任务并非相互排斥，而是有层级关系：

```mermaid
graph TD
    US[用户故事] --> T1[任务1]
    US --> T2[任务2]
    US --> T3[任务3]
    
    T1 --> ST1[子任务1.1]
    T1 --> ST2[子任务1.2]
    
    classDef story fill:#9C27B0,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef task fill:#1976D2,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef subtask fill:#388E3C,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    
    class US story
    class T1,T2,T3 task
    class ST1,ST2 subtask
```

- **用户故事**：代表一个完整的用户价值单元
- **任务**：实现用户故事所需的技术工作项
- **子任务**：更细粒度的开发工作单元

## 用户故事文档的价值

### 1. 促进以用户为中心的思维

用户故事强制团队从用户角度思考功能，而不仅仅是技术实现。这种转变确保了开发工作始终关注最终用户价值。

```
任务思维: "实现传感器阈值配置功能"
用户故事思维: "作为科学官，我希望设置自定义警报阈值，以便及时发现异常现象"
```

### 2. 促进跨职能协作

用户故事文档使用非技术语言描述需求，便于产品、设计、开发和测试等不同角色的团队成员理解和讨论。

### 3. 支持增量开发和优先级排序

用户故事的独立性和业务价值明确性，使团队能够基于价值进行优先级排序，支持敏捷开发的增量交付模式。

### 4. 提供清晰的完成定义

验收标准明确定义了"完成"的含义，减少了误解和返工，提高了开发效率和质量。

### 5. 保留决策上下文

用户故事文档记录了需求背后的原因和决策过程，为未来的维护和扩展提供了宝贵的上下文信息。

## 用户故事文档的最佳实践

### 1. INVEST原则

优质的用户故事应遵循INVEST原则：

- **I**ndependent（独立的）：尽可能减少故事间的依赖
- **N**egotiable（可协商的）：细节可以在实现过程中讨论
- **V**aluable（有价值的）：为用户或客户提供明确价值
- **E**stimable（可估算的）：团队能够评估实现难度
- **S**mall（小型的）：足够小以适合一个迭代/冲刺
- **T**estable（可测试的）：有明确的验收标准

### 2. 三C原则

用户故事的生命周期遵循三C原则：

- **Card**（卡片）：简洁的故事描述
- **Conversation**（对话）：团队围绕故事的讨论
- **Confirmation**（确认）：验收标准和测试

### 3. 垂直切片

将功能垂直切片为端到端的用户故事，而不是按技术层水平切片：

```
不推荐（水平切片）:
- 故事1: 实现所有传感器配置的数据库模型
- 故事2: 实现所有传感器配置的API
- 故事3: 实现所有传感器配置的UI

推荐（垂直切片）:
- 故事1: 科学官能够配置温度传感器的警报阈值（包含DB、API、UI）
- 故事2: 科学官能够配置辐射传感器的警报阈值（包含DB、API、UI）
```

## 用户故事文档模板示例

以下是一个可用于项目的用户故事文档模板：

```markdown
# 用户故事：[简洁的标题]

## 元数据
- **ID**: [唯一标识符]
- **创建日期**: [日期]
- **状态**: [草稿/待办/进行中/已完成]
- **优先级**: [高/中/低]
- **预估点数**: [复杂度估算]
- **负责人**: [主要负责人]

## 故事描述

**作为** [用户角色],
**我希望** [期望功能],
**以便** [业务价值].

## 业务价值

[详细描述这个故事为什么对用户和业务重要，可能包括量化的好处]

## 背景与上下文

[提供理解这个故事所需的背景信息，可能包括当前状态、问题描述、用户反馈等]

## 验收标准

1. [具体、可测试的标准1]
2. [具体、可测试的标准2]
3. [具体、可测试的标准3]
...

## 技术注释

- [实现相关的技术考量]
- [架构约束]
- [性能要求]
- [安全考量]
...

## 依赖关系

- **前置依赖**: [此故事依赖的其他故事]
- **相关故事**: [与此故事相关的其他故事]
- **外部依赖**: [外部系统或团队的依赖]

## 子任务

1. [ ] [子任务1]
2. [ ] [子任务2]
3. [ ] [子任务3]
...

## 讨论记录

- **[日期]**: [讨论内容和决策]
- **[日期]**: [讨论内容和决策]
...

## 附件与链接

- [设计文档链接]
- [原型链接]
- [其他相关资源]
```

## 用户故事与任务的转换

用户故事通常会被分解为多个任务，以便开发团队实施。这种转换过程如下：

```mermaid
flowchart LR
    US["用户故事
    作为科学官，我希望设置
    自定义警报阈值"]
    
    US --> T1["前端任务
    实现阈值配置界面"]
    
    US --> T2["后端任务
    开发阈值存储API"]
    
    US --> T3["后端任务
    实现实时监控服务"]
    
    US --> T4["测试任务
    编写端到端测试"]
    
    classDef story fill:#9C27B0,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    classDef task fill:#1976D2,stroke:#000,stroke-width:2px,color:#FFF,font-weight:bold
    
    class US story
    class T1,T2,T3,T4 task
```

## 结论：用户故事与任务的互补性

用户故事文档和任务类型文档在敏捷开发中扮演互补角色：

- **用户故事**关注"为什么"和"是什么"，从用户视角描述需求
- **任务**关注"如何"，从开发者视角描述实现步骤

理想的工作流程是：
1. 产品团队创建以用户为中心的用户故事
2. 开发团队将用户故事分解为技术任务
3. 开发人员实现这些任务
4. 团队根据原始用户故事的验收标准验证实现

通过这种方式，团队既能保持对用户价值的关注，又能有效组织技术工作，实现敏捷开发的双重目标：交付有价值的产品和高效的开发过程。

在《星际迷航》主题的敏捷团队中，皮卡德舰长（PM）负责确保用户故事清晰地表达用户需求和业务价值，而各专业领域的开发者（乔迪、数据等）则负责将这些故事转化为具体的技术任务并实现它们。


---

