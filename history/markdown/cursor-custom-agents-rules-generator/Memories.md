# cursor-custom-agents-rules-generator 的记忆

# User Preferences
- User prefers communication in Chinese language.
- 用户希望文档翻译成中文，保存在原文档同级目录。
- 用户希望了解如何与Cursor中配置的自定义mode进行交互。
- 用户希望了解自定义提示词如何与外部mdc或md文件进行关联。
- User希望使用mermaid图表可视化展示项目中各角色之间的依赖关系。
- User希望生成的mermaid图表中文字和背景的对比度要强，易于阅读。
- User希望文档关系以高对比度、易于阅读的图表进行可视化。
- User希望对文档内容进行深入分析，包括梳理核心自定义指令、找出关键语句并解释其含义。
- User希望了解文档管理的最佳实践，包括文档的必要性、不同类型文档的叙述维度以及每个维度解决的问题。
- User关心交互文档的定义以及前端开发是否应该有专门的文档。
- User希望生成规则用于分析前端网站截图，目的是将分析结果作为开发规范交给AI完成前端网站的复刻。
- 用户希望使用puppeteer工具和前端截图分析专家提示词来分析网页界面。
- User希望使用AI分析网页界面，生成详细的分析维度和指导策略，目的是将分析结果作为开发规范交给AI编程软件(如lovable bolt)完成开发。
- User分析的页面是提示词平台的详情页面，Instagram元素仅作为封面图出现，不是主要分析对象。
- User希望UI分析系统设计为通用型，不要局限于提示词平台，应能适用于分析各种网站或截图。
- 用户希望了解modes.json中的四个提示词如何协同工作。
- 用户希望在UI分析中包含用户交互路径叙述部分。

# Project Interests
- User is interested in exploring VSCode plugin implementation or other platforms to make the project more accessible and user-friendly.
- User is interested in implementing agile workflow in existing projects by having AI automatically analyze project content to generate initial documentation.
- User prefers documentation to be generated according to the @.cursor/rules/workflows/workflow-agile-manual.mdc format.
- User wants to analyze existing projects to automatically generate documentation following the workflow-agile-manual.mdc structure.