# 创建前端网站分析复刻规则


**项目**: cursor-custom-agents-rules-generator

**创建时间**: 2025年04月18日 06:24

**最后交互**: 2025年04月28日 08:53

**对话ID**: eaa3f909-ac92-45e3-8f0a-df89fad39b8c

---

### 对话 1

> **👤 用户**

@.cursor/rules/core-rules/rule-generating-agent.mdc 根据这个规则，为我生成一份用来详细分析前端网站截图的规则，目的是将分析完的开发规范交给 AI 来完成前端网站的完美复刻

> **🤖 Augment**

我将帮您根据`.cursor/rules/core-rules/rule-generating-agent.mdc`文件创建一个用于分析前端网站截图的规则，以便AI能够根据分析结果完美复刻前端网站。首先，让我查看这个规则文件的内容，了解其结构和格式。


现在我了解了规则文件的结构和格式，我将创建一个用于分析前端网站截图的规则文件。这个规则将帮助AI分析网站截图，提取设计规范，并指导AI如何完美复刻前端网站。

让我为您创建这个规则文件：


我已经为您创建了一个用于分析前端网站截图的规则文件。这个规则文件遵循了`.cursor/rules/core-rules/rule-generating-agent.mdc`中定义的格式和要求。

AutoRuleGen Success: .cursor/rules/ui-rules/rule-frontend-screenshot-analysis-agent.mdc
Rule Type: Agent Select Rule
Rule Description: This rule provides comprehensive guidance for analyzing frontend website screenshots to extract design specifications and development standards. It should be applied when: (1) A user provides screenshots of a website and requests analysis for replication, (2) A user needs to extract UI/UX patterns from visual references, (3) A user wants to convert visual designs into implementable code specifications, or (4) When creating detailed frontend development requirements from visual examples. This rule ensures systematic analysis of visual elements, accurate extraction of design systems, and translation of visual patterns into actionable development guidelines that can be used by AI to perfectly replicate the frontend website.

### 规则说明

这个规则文件提供了一个系统化的框架，用于分析前端网站截图并提取详细的设计规范，以便AI能够完美复刻网站。规则包含以下关键部分：

1. **系统化分析方法**：从全局布局到组件结构再到详细元素的自上而下分析方法
2. **精确的设计规范提取**：包括颜色值、间距、排版、响应式行为等
3. **组件目录**：识别所有UI组件及其变体、状态和交互模式
4. **设计系统原则**：提取配色方案、间距比例和排版层次结构
5. **布局机制**：识别网格系统、弹性盒使用和其他布局机制
6. **动画和过渡规范**：包括时间、缓动函数和触发器
7. **资源清单**：包括图标、图像和其他媒体元素
8. **可访问性和兼容性**：分析可访问性功能和跨浏览器兼容性要求
9. **实现建议**：提供具体的HTML结构和CSS属性建议

这个规则将帮助AI系统地分析前端网站截图，提取所有必要的设计和开发规范，从而能够完美复刻前端网站。

您可以通过提供网站截图并引用这个规则，让AI按照这个规则进行分析，然后根据分析结果进行前端开发。


---

### 对话 2

> **👤 用户**

这个文档能指导 AI 完成什么样的工作，预期的输出结果是什么？请你给出详细的案例以及解释

> **🤖 Augment**

我将详细解释这个规则文档能指导AI完成的工作、预期输出结果，并提供具体案例和解释。

## 规则文档指导AI完成的工作

`.cursor/rules/ui-rules/rule-frontend-screenshot-analysis-agent.mdc`这个规则文档主要指导AI完成以下工作：

1. **系统化分析网站截图**：按照从整体到局部的方法分析前端网站截图
2. **提取设计规范**：从视觉元素中提取精确的设计系统规范
3. **转化为开发标准**：将视觉分析转换为可实施的前端开发规范
4. **生成复刻指南**：创建详细的指导文档，使AI能够精确复刻网站

## 预期的输出结果

当使用这个规则分析网站截图时，预期的输出结果是一份全面的前端开发规范文档，包括：

1. **全局布局分析**：包含最大宽度、网格系统、响应式断点等
2. **颜色系统**：精确的颜色代码及其应用场景
3. **排版系统**：字体族、字重、字号、行高等详细规范
4. **组件规范**：每个UI组件的详细尺寸、间距、边框、阴影等属性
5. **实现建议**：优先级排序的具体技术实现方案

## 详细案例与解释

### 案例一：电子商务网站分析

假设用户提供了一个电子商务网站的首页截图，并希望AI分析后复刻这个网站。

**输入**：电子商务网站首页截图

**AI应用规则后的输出**：

```markdown
# 电子商务网站前端开发规范

## 全局布局分析
- 容器宽度：最大1440px，两侧各有5%的边距
- 网格系统：12列网格，列间距24px
- 响应式断点：桌面端(1200px+)，平板端(768px-1199px)，移动端(<768px)

## 颜色系统
- 品牌色：#FF4500（用于主要按钮、强调元素）
- 辅助色：#0066CC（用于链接、次要按钮）
- 中性色：
  - 背景：#FFFFFF（页面背景）, #F7F8FA（卡片背景）
  - 文本：#333333（主要文本）, #666666（次要文本）, #999999（辅助文本）
- 功能色：#28A745（成功状态）, #DC3545（错误状态）, #FFC107（警告状态）

## 排版系统
- 标题字体：'PingFang SC', 'Microsoft YaHei', sans-serif
  - H1: 32px/48px, 字重700
  - H2: 24px/36px, 字重600
  - H3: 20px/30px, 字重600
- 正文字体：'PingFang SC', 'Microsoft YaHei', sans-serif
  - 大号文本：18px/27px, 字重400
  - 正文文本：16px/24px, 字重400
  - 小号文本：14px/21px, 字重400
- 按钮文本：16px/24px, 字重500

## 组件规范

### 1. 导航栏
- 高度：80px（桌面端），60px（移动端）
- 背景色：#FFFFFF
- 阴影：0 2px 8px rgba(0,0,0,0.08)
- Logo：左对齐，高度40px
- 导航项：间距32px，字号16px，字重500
- 搜索框：
  - 尺寸：360px × 40px（桌面端），100%宽（移动端）
  - 圆角：20px
  - 边框：1px solid #EEEEEE
  - 内边距：0 16px

### 2. 产品卡片
- 尺寸：280px × 420px
- 内边距：16px
- 边框：1px solid #EEEEEE
- 圆角：8px
- 阴影：0 4px 12px rgba(0,0,0,0.05)
- 图片容器：280px × 280px
- 产品标题：18px，字重600，最多两行，溢出显示省略号
- 价格：20px，字重700，颜色#FF4500
- 评分：5星系统，使用#FFC107表示填充星

### 3. 轮播图
- 尺寸：100%宽 × 500px（桌面端），100%宽 × 300px（移动端）
- 指示器：
  - 形状：圆形，直径8px
  - 颜色：未激活#CCCCCC，激活#FF4500
  - 位置：底部居中，距底16px
- 切换按钮：
  - 尺寸：40px × 40px
  - 背景：rgba(0,0,0,0.3)
  - 圆角：50%
  - 图标颜色：#FFFFFF

## 交互规范
- 按钮悬停效果：背景色亮度降低10%，过渡时间0.2s
- 卡片悬停效果：阴影增大至0 8px 16px rgba(0,0,0,0.1)，过渡时间0.3s
- 页面滚动动画：平滑滚动，scroll-behavior: smooth
- 加载状态：使用骨架屏，背景色#F0F0F0，动画pulse 1.5s infinite

## 实现建议
1. 使用Flexbox进行主要布局，CSS Grid用于产品列表
2. 实现响应式设计使用媒体查询，移动优先方法
3. 使用CSS变量存储颜色和间距系统
4. 实现懒加载图片以提高性能
5. 使用语义化HTML结构提高可访问性
6. 组件化开发，可考虑使用React或Vue框架
```

### 案例二：SaaS产品控制面板分析

假设用户提供了一个SaaS产品控制面板的截图，希望AI分析并复刻。

**输入**：SaaS产品控制面板截图

**AI应用规则后的输出**：

```markdown
# SaaS产品控制面板前端开发规范

## 全局布局分析
- 布局类型：侧边栏导航 + 顶部栏 + 主内容区
- 侧边栏宽度：240px（展开状态），64px（折叠状态）
- 顶部栏高度：56px
- 内容区内边距：24px
- 响应式行为：<992px时侧边栏自动折叠

## 颜色系统
- 主题色：#6200EE（主要交互元素）
- 辅助色：#03DAC5（次要交互元素）
- 背景色：
  - 侧边栏：#1F1F1F
  - 顶部栏：#FFFFFF
  - 主内容区：#F5F7FA
  - 卡片：#FFFFFF
- 文本色：
  - 深色背景上：#FFFFFF（主要）, rgba(255,255,255,0.7)（次要）
  - 浅色背景上：#202124（主要）, #5F6368（次要）
- 状态色：
  - 成功：#00C853
  - 警告：#FFD600
  - 错误：#F44336
  - 信息：#2196F3

## 排版系统
- 字体：'Roboto', 'Noto Sans SC', sans-serif
- 标题：
  - 页面标题：24px/32px，字重500
  - 卡片标题：18px/24px，字重500
  - 分组标题：16px/24px，字重500
- 正文：
  - 默认：14px/20px，字重400
  - 强调：14px/20px，字重500
  - 小号：12px/16px，字重400

## 组件规范

### 1. 侧边导航
- 菜单项高度：48px
- 图标尺寸：24px × 24px
- 文本与图标间距：16px
- 活动状态：左侧4px宽的主题色条，背景色rgba(98,0,238,0.08)
- 悬停状态：背景色rgba(0,0,0,0.04)

### 2. 数据卡片
- 内边距：16px
- 圆角：8px
- 阴影：0 1px 2px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.05)
- 标题与内容间距：12px
- 卡片间距：24px

### 3. 数据表格
- 表头：
  - 背景色：#F8F9FA
  - 字体：14px，字重500
  - 高度：48px
- 表格行：
  - 高度：52px
  - 边框：底部1px solid #EEEEEE
  - 悬停背景：#F5F7FA
- 分页控件：
  - 位置：右下角
  - 页码按钮：32px × 32px，圆角4px

### 4. 表单控件
- 输入框：
  - 高度：40px
  - 内边距：0 12px
  - 边框：1px solid #DADCE0，圆角4px
  - 聚焦状态：边框2px solid #6200EE
- 按钮：
  - 主要按钮：背景#6200EE，文字#FFFFFF，圆角4px
  - 次要按钮：背景透明，边框1px solid #6200EE，文字#6200EE
  - 高度：36px
  - 内边距：0 16px

## 交互规范
- 侧边栏折叠/展开动画：宽度过渡，持续时间0.3s，缓动函数ease-in-out
- 卡片加载动画：淡入，持续时间0.5s
- 表单验证反馈：即时显示，错误信息颜色#F44336
- 数据刷新指示器：顶部进度条，颜色#6200EE

## 实现建议
1. 使用CSS Grid布局主内容区的卡片
2. 实现主题切换功能（明/暗模式）
3. 使用CSS变量管理颜色系统
4. 组件化开发，建议使用React或Vue
5. 实现数据表格的排序、筛选和分页功能
6. 使用图表库（如Chart.js或Echarts）实现数据可视化
7. 优化移动端体验，确保所有功能在小屏幕上可用
```

## 规则应用解释

在上述案例中，AI应用了规则文档中的关键指导原则：

1. **系统化分析方法**：从全局布局开始，逐步深入到组件细节
2. **精确的设计规范**：提供了精确的颜色代码、尺寸、间距等数值
3. **组件目录**：详细描述了每个UI组件的所有属性和变体
4. **设计系统提取**：识别并记录了颜色系统、排版系统和间距系统
5. **实现建议**：提供了优先级排序的技术实现方案

这种详细的规范文档使AI能够：

1. 理解设计意图和视觉层次结构
2. 掌握设计系统的核心原则
3. 了解组件之间的关系和交互模式
4. 获得精确的技术实现参数
5. 按照优先级实施开发计划

通过这种方式，AI能够根据分析结果精确复刻前端网站，确保视觉一致性、交互体验和技术实现都符合原始设计的要求。

## 总结

这个规则文档为AI提供了一个系统化的框架，用于分析前端网站截图并生成详细的开发规范。通过遵循这个规则，AI能够从视觉参考中提取所有必要的设计和开发信息，从而能够完美复刻前端网站。这对于快速原型开发、设计转代码、竞品分析等场景特别有价值。


---

### 对话 3

> **👤 用户**

为我将每个角色的系统提示词提炼出来，翻译成中文，输出到同级目录单独的文件中，命名：角色.prompt 

> **🤖 Augment**

我将帮您提炼每个角色的系统提示词，翻译成中文，并保存到同级目录的单独文件中。首先，我需要了解当前文件中包含的角色信息。


我现在了解了这个文件中的内容。这个文件是一个前端截图分析规则，主要定义了一个角色：前端截图分析专家。我将提炼这个角色的系统提示词，翻译成中文，并保存到同级目录的单独文件中。

首先，我将创建前端截图分析专家的提示词文件：


现在，我将查看是否有其他相关的规则文件，以便提取更多角色的系统提示词：


从这个文件中，我可以提取出规则生成代理的角色。我将创建这个角色的提示词文件：


让我检查是否还有其他相关的规则文件，以便提取更多角色的系统提示词：


根据检索到的信息，我发现了几个额外的角色，我将为它们创建提示词文件：

1. 项目分析代理
2. Cursor模式更新代理
3. 敏捷工作流中的项目经理(Picard)和架构师(Spock)角色

让我为这些角色创建提示词文件：


我已经为您提取并翻译了以下角色的系统提示词，并将它们保存到了相应的文件中：

1. **前端截图分析专家**
   - 文件路径：`.cursor/rules/ui-rules/前端截图分析专家.prompt`
   - 描述：专门分析前端网站截图，提取设计规范和开发标准，以便AI能够完美复刻网站

2. **规则生成代理**
   - 文件路径：`.cursor/rules/core-rules/规则生成代理.prompt`
   - 描述：负责创建和维护Cursor规则系统中的规则文件，确保规则的一致性、质量和有效性

3. **项目分析代理**
   - 文件路径：`.cursor/rules/core-rules/项目分析代理.prompt`
   - 描述：分析现有项目并生成符合敏捷工作流结构的全面文档

4. **Cursor模式更新代理**
   - 文件路径：`.cursor/rules/core-rules/Cursor模式更新代理.prompt`
   - 描述：负责在.cursor/modes.json文件中添加或更新代理/角色配置

5. **敏捷工作流项目经理 (Picard)**
   - 文件路径：`.cursor/rules/core-rules/敏捷工作流项目经理.prompt`
   - 描述：负责创建和管理产品需求文档(PRD)和用户故事

6. **敏捷工作流架构师 (Spock)**
   - 文件路径：`.cursor/rules/core-rules/敏捷工作流架构师.prompt`
   - 描述：负责创建和管理架构文档，确保技术选择适合项目需求

每个文件都包含了角色描述、工作方法、具体流程和注意事项，以中文形式呈现。这些提示词文件可以帮助您更好地理解和使用这些角色，也可以作为创建新角色的参考。

您可以根据需要修改这些文件，或者使用它们来指导AI在特定角色下工作。如果您需要添加更多角色或修改现有角色的提示词，请随时告诉我。


---

