# n8n节点理解&梳理

**项目**: n8n

**创建时间**: 2025年04月21日 09:45

**最后交互**: 2025年04月21日 09:52

**对话ID**: a9111996-476a-4840-8999-785dd3e6430c

---

### 对话 1

> **👤 用户**

帮我梳理这个项目支持的节点有多少？每个节点的作用是什么？使用场景是什么？如何使用？先为我分析前10个节点

> **🤖 Augment**

我会帮您梳理n8n项目支持的节点，分析前10个节点的作用、使用场景和使用方法。首先，让我搜索项目中的节点信息。


让我继续搜索更多关于n8n节点的信息，特别是查找节点的目录结构和分类。


让我继续查找更多关于n8n节点的信息，特别是查找一些具体节点的文档或描述。


让我继续查找更多关于n8n节点的信息，特别是查找一些常用节点的具体功能和使用方法。


让我查找更多关于n8n节点的分类和类型信息，以便更全面地了解n8n支持的节点类型。


让我查找一些具体的节点文档，以便了解这些节点的具体功能和使用方法。


让我查找一些关于n8n工作流和节点使用的实际示例，以便更好地理解节点的使用方法。


基于我对n8n项目代码的分析，我将为您梳理n8n支持的节点类型及其功能。以下是前10个核心节点的详细分析：

# n8n节点分析

n8n是一个强大的工作流自动化平台，支持大量节点类型，用于构建各种自动化工作流。以下是对前10个核心节点的详细分析：

## 1. HTTP Request 节点

**作用**：发送HTTP请求并返回响应数据

**使用场景**：
- 调用外部API获取数据
- 向第三方服务发送数据
- 与Web服务进行交互
- 爬取网页内容

**如何使用**：
- 设置HTTP方法（GET、POST、PUT、DELETE等）
- 指定URL
- 配置请求头、查询参数、请求体等
- 可以处理各种响应格式（JSON、XML等）

**示例**：
```
{
  "parameters": {
    "url": "https://catfact.ninja/fact",
    "options": {}
  },
  "name": "HTTP Request",
  "type": "n8n-nodes-base.httpRequest",
  "typeVersion": 3
}
```

## 2. Function 节点 (现在推荐使用Code节点)

**作用**：执行自定义JavaScript代码，处理工作流数据

**使用场景**：
- 数据转换和处理
- 复杂逻辑实现
- 自定义计算
- 批量处理多个项目

**如何使用**：
- 编写JavaScript代码
- 可以访问输入数据（items变量）
- 可以使用内置库（如luxon处理日期）
- 返回处理后的数据

**示例**：
```javascript
// 代码示例
// 为每个输入项添加新字段
for (item of items) {
  item.json.myNewField = 1;
}

console.log('Done!');
return items;
```

## 3. Set 节点 (Edit Fields)

**作用**：添加、修改或删除数据项的字段

**使用场景**：
- 数据结构调整
- 添加新字段
- 修改现有字段值
- 删除不需要的字段

**如何使用**：
- 手动模式：逐个添加字段和值
- 原始模式：使用JSON定义字段
- 支持表达式和动态值
- 可以选择保留或删除其他字段

**示例**：
```
{
  "parameters": {
    "assignments": {
      "assignments": [
        {
          "id": "3a40d9f2-0eed-4a92-9287-9d6ec9ce90e8",
          "name": "message",
          "value": "hello there",
          "type": "string"
        }
      ]
    },
    "options": {}
  },
  "type": "n8n-nodes-base.set",
  "typeVersion": 3.4
}
```

## 4. Webhook 节点

**作用**：创建HTTP端点接收外部请求触发工作流

**使用场景**：
- 接收第三方服务的回调
- 创建API端点
- 接收表单提交
- 处理实时事件通知

**如何使用**：
- 设置HTTP方法和路径
- 配置响应方式
- 可以处理查询参数、请求体等
- 支持多种响应格式

**示例**：
```
{
  "parameters": {
    "httpMethod": "POST",
    "path": "benchmark-http-node",
    "responseMode": "responseNode",
    "options": {}
  },
  "type": "n8n-nodes-base.webhook",
  "typeVersion": 2
}
```

## 5. Manual Trigger 节点

**作用**：手动触发工作流执行

**使用场景**：
- 测试工作流
- 按需执行工作流
- 开发和调试

**如何使用**：
- 添加到工作流开始
- 点击"执行工作流"按钮触发

**示例**：
```
{
  "parameters": {},
  "type": "n8n-nodes-base.manualTrigger",
  "typeVersion": 1
}
```

## 6. XML 节点

**作用**：处理XML数据，进行转换和解析

**使用场景**：
- 解析XML响应
- 将数据转换为XML格式
- 处理XML配置文件
- 与使用XML的API交互

**如何使用**：
- 选择操作模式（解析或转换）
- 指定XML数据源
- 配置解析选项
- 处理结果数据

## 7. HTML 节点

**作用**：处理HTML内容，提取数据

**使用场景**：
- 网页抓取
- 提取HTML表格数据
- 解析HTML结构
- 转换HTML内容

**如何使用**：
- 选择操作（提取、转换等）
- 指定HTML源
- 配置选择器或提取规则
- 处理提取的数据

## 8. ItemLists 节点

**作用**：处理数据项列表，进行聚合、排序、去重等操作

**使用场景**：
- 数据去重
- 排序数据
- 限制数据项数量
- 分割数据
- 聚合数据

**如何使用**：
- 选择操作类型（去重、排序等）
- 配置操作参数
- 指定字段或条件
- 处理结果数据

## 9. Interval 节点

**作用**：按指定时间间隔定期触发工作流

**使用场景**：
- 定期数据同步
- 轮询API
- 定时任务
- 监控系统

**如何使用**：
- 设置时间间隔
- 配置执行次数（无限或有限）
- 设置开始和结束时间

## 10. n8n 节点

**作用**：与n8n实例交互，管理工作流和执行

**使用场景**：
- 管理其他工作流
- 触发工作流执行
- 获取执行结果
- 自动化工作流管理

**如何使用**：
- 选择资源（工作流、执行等）
- 选择操作（获取、创建等）
- 配置参数
- 处理结果数据

这些节点是n8n中最常用的核心节点，它们提供了构建复杂自动化工作流的基础功能。n8n还支持大量其他节点，包括与各种第三方服务集成的应用节点，以及更多专用功能节点。

n8n的节点可以分为几个主要类别：
1. **核心节点**：基础功能节点，如HTTP请求、数据处理等
2. **触发器节点**：用于启动工作流的节点，如Webhook、定时器等
3. **应用节点**：与特定第三方服务集成的节点，如GitHub、Slack等
4. **AI节点**：与AI服务集成的节点，如OpenAI等
5. **数据转换节点**：处理数据格式和结构的节点

每个节点都有详细的文档，可以在n8n官方文档网站上查找：https://docs.n8n.io/integrations/


---

### 对话 2

> **👤 用户**

帮我分析 code 节点的运行原理，支持 javascript和python，该节点是如何运行的 ？

> **🤖 Augment**

# Code 节点运行原理分析

Code 节点是 n8n 中最强大的节点之一，它允许用户在工作流中执行自定义代码（JavaScript 或 Python）。让我深入分析这个节点的运行原理。

## 概述

Code 节点是 Function 节点的升级版本，支持两种编程语言：
1. JavaScript
2. Python

它提供了两种执行模式：
- 对所有项目执行一次（Run once for all items）
- 对每个项目单独执行（Run once for each item）

## 技术实现原理

让我通过代码分析来解释 Code 节点的运行机制：


## JavaScript 执行原理

### 1. 架构概述

Code 节点支持两种编程语言（JavaScript 和 Python）和两种执行模式（对所有项目执行一次或对每个项目单独执行）。其核心架构如下：

```
Code.node.ts (主节点类)
├── JavaScriptSandbox.ts (JavaScript 沙箱)
├── PythonSandbox.ts (Python 沙箱)
├── JsTaskRunnerSandbox.ts (JS任务运行器沙箱)
└── Sandbox.ts (基础沙箱类)
```

### 2. JavaScript 执行流程

当用户在 Code 节点中编写 JavaScript 代码时，执行流程如下：

1. **代码获取**：从节点参数中获取用户编写的 JavaScript 代码
   ```typescript
   const code = this.getNodeParameter(codeParameterName, 0) as string;
   ```

2. **沙箱创建**：创建一个 JavaScript 沙箱环境
   ```typescript
   const sandbox = new JavaScriptSandbox(context, code, this.helpers);
   ```

3. **上下文准备**：为代码执行准备上下文环境，包括：
   - 工作流数据
   - 输入项
   - 辅助函数
   - 特殊变量（如 $input, $json 等）

4. **代码包装**：将用户代码包装在异步函数中
   ```typescript
   const script = `module.exports = async function() {${this.jsCode}\n}()`;
   ```

5. **安全执行**：使用 vm2 库在隔离环境中执行代码
   ```typescript
   executionResult = await this.vm.run(script, __dirname);
   ```

6. **结果处理**：验证和标准化执行结果
   ```typescript
   return this.validateRunCodeAllItems(executionResult);
   ```

### 3. 沙箱安全机制

JavaScript 代码在 `vm2` 库提供的沙箱中执行，这提供了几个关键安全特性：

- **隔离执行环境**：代码在隔离的 VM 中运行，无法访问主进程
- **受限的模块访问**：只能访问明确允许的模块
- **控制台重定向**：console.log 输出被捕获并重定向到 UI
- **内存限制**：防止无限循环和内存泄漏

```typescript
this.vm = new NodeVM({
  console: 'redirect',
  sandbox: context,
  require: options?.resolver ?? vmResolver,
  wasm: false,
});
```

## Python 执行原理

### 1. Pyodide 集成

Python 代码执行使用 Pyodide，这是一个将 Python 编译为 WebAssembly 的项目，允许在 JavaScript 环境中运行 Python 代码。

```typescript
const pyodide = await LoadPyodide(packageCacheDir);
```

### 2. Python 执行流程

1. **加载 Pyodide**：初始化 Pyodide 运行时
   ```typescript
   const pyodide = await LoadPyodide(packageCacheDir);
   ```

2. **变量名转换**：由于 Python 不允许变量名以 `$` 开头，将所有 `$` 前缀变量转换为 `_` 前缀
   ```typescript
   this.context = Object.keys(context).reduce((acc, key) => {
     acc[key.startsWith('$') ? key.replace(/^\$/, '_') : key] = context[key];
     return acc;
   }, {} as PythonSandboxContext);
   ```

3. **代码包装**：将用户代码包装在异步函数中
   ```typescript
   const runCode = `
   async def __main():
   ${this.pythonCode
     .split('\n')
     .map((line) => '  ' + line)
     .join('\n')}
   await __main()`;
   ```

4. **上下文传递**：将 JavaScript 上下文传递给 Python 环境
   ```typescript
   const globalsDict: PyDict = dict();
   for (const key of Object.keys(this.context)) {
     if ((key === '_env' && envAccessBlocked) || key === '_node') continue;
     const value = this.context[key];
     globalsDict.set(key, value);
   }
   ```

5. **执行代码**：在 Pyodide 环境中执行 Python 代码
   ```typescript
   executionResult = await pyodide.runPythonAsync(runCode, { globals: globalsDict });
   ```

6. **结果转换**：将 Python 结果转换回 JavaScript 对象
   ```typescript
   return executionResult.toJs({
     dict_converter: Object.fromEntries,
     create_proxies: false,
   }) as T;
   ```

## 执行模式详解

### 1. 对所有项目执行一次 (runOnceForAllItems)

在这种模式下，代码只执行一次，处理所有输入项：

- JavaScript 中通过 `items` 变量访问所有输入项
- Python 中通过 `items` 变量访问所有输入项
- 代码需要返回一个数组，包含处理后的所有项目

```javascript
// JavaScript 示例
// 为每个项目添加一个新字段
for (const item of items) {
  item.json.newField = 'value';
}
return items;
```

```python
# Python 示例
# 为每个项目添加一个新字段
for item in items:
  item["json"]["newField"] = "value"
return items
```

### 2. 对每个项目单独执行 (runOnceForEachItem)

在这种模式下，代码会为每个输入项单独执行一次：

- JavaScript 中通过 `item` 变量访问当前输入项
- Python 中通过 `item` 变量访问当前输入项
- 代码需要返回单个处理后的项目

```javascript
// JavaScript 示例
// 处理当前项目
item.json.newField = 'value';
return item;
```

```python
# Python 示例
# 处理当前项目
item["json"]["newField"] = "value"
return item
```

## 可用的特殊变量和方法

Code 节点提供了多种特殊变量和方法，使用户能够更方便地处理数据：

### JavaScript 中的特殊变量

- `$input` - 访问输入数据的方法集合
- `$json` - 当前项目的 JSON 数据
- `$binary` - 当前项目的二进制数据
- `$itemIndex` - 当前项目的索引
- `$node` - 当前节点信息
- `$workflow` - 工作流信息
- `$today` - 当前日期（Luxon DateTime 对象）
- `$jmespath` - JMESPath 查询工具

### Python 中的特殊变量

- `_input` - 访问输入数据的方法集合（注意 $ 变为 _）
- `_json` - 当前项目的 JSON 数据
- `_binary` - 当前项目的二进制数据
- `_itemIndex` - 当前项目的索引
- `_node` - 当前节点信息
- `_workflow` - 工作流信息
- `_today` - 当前日期
- `_jmespath` - JMESPath 查询工具

## 总结

Code 节点是 n8n 中最强大的节点之一，它允许用户在工作流中执行自定义代码，实现复杂的数据处理逻辑。

### JavaScript 执行

- 使用 vm2 库在沙箱环境中安全执行
- 提供丰富的上下文和特殊变量
- 支持两种执行模式（对所有项目执行一次或对每个项目单独执行）
- 可以访问受限的 Node.js 模块

### Python 执行

- 使用 Pyodide（WebAssembly）执行 Python 代码
- 将 JavaScript 上下文转换为 Python 可用的格式
- 支持与 JavaScript 相同的执行模式
- 提供类似的特殊变量（但前缀从 $ 变为 _）

这种设计使 Code 节点成为 n8n 工作流中处理复杂数据转换和业务逻辑的强大工具，同时通过沙箱机制确保了代码执行的安全性。


---

### 对话 3

> **👤 用户**

一个标准的 code 节点代码模板是怎么样的？ 如何理解？

> **🤖 Augment**

# Code 节点代码模板解析

Code 节点是 n8n 中最灵活的节点之一，它允许您编写自定义代码来处理数据。下面我将详细解析 JavaScript 和 Python 的标准代码模板，帮助您理解其结构和使用方法。

## JavaScript 代码模板

### 对所有项目执行一次模式 (Run Once for All Items)

```javascript
// 代码将只执行一次，无论有多少输入项
// 更多信息和帮助: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/
// 提示: 您可以使用 luxon 处理日期，使用 $jmespath 查询 JSON 结构

// 循环处理所有输入项并为每个项目添加一个名为 'myNewField' 的新字段
for (const item of $input.all()) {
  item.json.myNewField = 1;
}

// 您可以通过 console.log 写入日志，并在浏览器控制台中查看输出
console.log('处理完成!');

// 返回处理后的所有项目
return $input.all();
```

### 对每个项目单独执行模式 (Run Once for Each Item)

```javascript
// 代码将为每个输入项单独执行一次
// 更多信息和帮助: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/
// 提示: 您可以使用 luxon 处理日期，使用 $jmespath 查询 JSON 结构

// 获取当前项目
const item = $input.item;

// 修改当前项目
item.json.myNewField = 1;

// 您可以通过 console.log 写入日志，并在浏览器控制台中查看输出
console.log('处理项目 ' + $input.itemIndex + ' 完成!');

// 返回处理后的当前项目
return item;
```

## Python 代码模板

### 对所有项目执行一次模式 (Run Once for All Items)

```python
# 代码将只执行一次，无论有多少输入项
# 更多信息和帮助: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/
# 提示: 您可以使用内置方法和变量，如 _today 处理日期，_jmespath 查询 JSON 结构

# 循环处理所有输入项并为每个项目添加一个名为 'myNewField' 的新字段
for item in _input.all():
    item["json"]["myNewField"] = 1

# 您可以通过 print 写入日志，并在浏览器控制台中查看输出
print("处理完成!")

# 返回处理后的所有项目
return _input.all()
```

### 对每个项目单独执行模式 (Run Once for Each Item)

```python
# 代码将为每个输入项单独执行一次
# 更多信息和帮助: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/
# 提示: 您可以使用内置方法和变量，如 _today 处理日期，_jmespath 查询 JSON 结构

# 获取当前项目
item = _input.item

# 修改当前项目
item["json"]["myNewField"] = 1

# 您可以通过 print 写入日志，并在浏览器控制台中查看输出
print(f"处理项目 {_input.itemIndex} 完成!")

# 返回处理后的当前项目
return item
```

## 代码模板解析

### 关键组成部分

1. **注释部分**：
   - 提供关于代码执行模式的说明
   - 包含指向文档的链接
   - 提供有用的提示

2. **数据访问**：
   - JavaScript: 使用 `$input.all()` 或 `$input.item` 访问数据
   - Python: 使用 `_input.all()` 或 `_input.item` 访问数据

3. **数据处理**：
   - 循环遍历项目或处理单个项目
   - 修改项目的 JSON 数据

4. **日志输出**：
   - JavaScript: 使用 `console.log()`
   - Python: 使用 `print()`

5. **返回结果**：
   - 必须返回处理后的数据
   - 返回格式必须与执行模式匹配

### 理解数据结构

在 n8n 中，数据以"项目"(items)的形式流动，每个项目包含：

- **json**: 包含主要数据的对象
- **binary**: (可选) 包含二进制数据的对象
- **pairedItem**: 用于跟踪项目来源的元数据

```javascript
// 项目结构示例
{
  json: { 
    name: "John", 
    email: "<EMAIL>" 
  },
  binary: {
    // 二进制数据 (如果有)
  },
  pairedItem: {
    item: 0 // 来源项目的索引
  }
}
```

## 常用模式和技巧

### 1. 数据转换

```javascript
// 转换所有项目的数据格式
return $input.all().map(item => {
  return {
    json: {
      fullName: `${item.json.firstName} ${item.json.lastName}`,
      email: item.json.email.toLowerCase(),
      age: parseInt(item.json.age)
    }
  };
});
```

### 2. 数据过滤

```javascript
// 过滤项目
return $input.all().filter(item => {
  return item.json.age > 18 && item.json.country === 'USA';
});
```

### 3. 数据聚合

```javascript
// 聚合数据
const result = $input.all().reduce((acc, item) => {
  acc.totalAmount += item.json.amount;
  acc.count += 1;
  return acc;
}, { totalAmount: 0, count: 0 });

// 返回聚合结果作为单个项目
return [{
  json: {
    totalAmount: result.totalAmount,
    averageAmount: result.totalAmount / result.count,
    count: result.count
  }
}];
```

### 4. 使用外部库

JavaScript 中可以使用一些内置库：

```javascript
// 使用 luxon 处理日期
const { DateTime } = require('luxon');
const now = DateTime.now();
const tomorrow = now.plus({ days: 1 });

// 使用 lodash 处理数据
const _ = require('lodash');
const grouped = _.groupBy($input.all(), item => item.json.category);
```

### 5. 错误处理

```javascript
// 错误处理
try {
  // 可能会失败的代码
  const data = JSON.parse(item.json.complexData);
  return { json: { parsedData: data } };
} catch (error) {
  // 处理错误
  console.log('解析数据时出错:', error.message);
  return { json: { error: error.message, originalData: item.json.complexData } };
}
```

## 特殊变量和方法

### JavaScript 中的特殊变量

- `$input` - 访问输入数据的方法集合
  - `$input.all()` - 获取所有输入项
  - `$input.first()` - 获取第一个输入项
  - `$input.last()` - 获取最后一个输入项
  - `$input.item` - 当前项目 (仅在"对每个项目单独执行"模式中)
  - `$input.itemIndex` - 当前项目的索引

- `$json` - 当前项目的 JSON 数据 (等同于 `$input.item.json`)
- `$binary` - 当前项目的二进制数据 (等同于 `$input.item.binary`)
- `$today` - 当前日期 (Luxon DateTime 对象)
- `$jmespath` - JMESPath 查询工具

### Python 中的特殊变量

- `_input` - 访问输入数据的方法集合 (注意 $ 变为 _)
- `_json` - 当前项目的 JSON 数据
- `_binary` - 当前项目的二进制数据
- `_today` - 当前日期
- `_jmespath` - JMESPath 查询工具

## 总结

Code 节点的代码模板提供了一个结构化的框架，帮助您处理工作流中的数据。理解这些模板的关键在于：

1. **了解执行模式**：代码可以对所有项目执行一次，或对每个项目单独执行一次
2. **掌握数据访问**：使用 `$input`/`_input` 访问输入数据
3. **正确返回数据**：确保返回格式与执行模式匹配
4. **利用特殊变量**：使用内置变量和方法简化代码

通过掌握这些模板和概念，您可以充分利用 Code 节点的强大功能，实现复杂的数据处理逻辑。


---

