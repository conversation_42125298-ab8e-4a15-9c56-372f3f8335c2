# Claude Code Best Practices Request


**项目**: n8n

**创建时间**: 2025年04月22日 03:24

**最后交互**: 2025年05月12日 10:57

**对话ID**: c1b2eb98-5d2f-452a-a4b1-082f08d28b03

---

### 对话 1

> **👤 用户**

https://www.anthropic.com/engineering/claude-code-best-practices

调用 firecraw mcp 工具，获取这个文档的原始内容，Markdown形式输出，帮我总结这篇文章

> **🤖 Augment**

I'll help you retrieve and summarize the Claude Code Best Practices article from Anthropic's website. Let me fetch the content first.


# Claude Code: Best Practices for Agentic Coding - Summary

This article from Anthropic outlines best practices for using Claude Code, their command line tool for agentic coding. Here's a comprehensive summary:

## 1. Customize Your Setup

- **Create CLAUDE.md files**: Special files that <PERSON> automatically pulls into context, ideal for documenting common commands, code style, testing instructions, etc.
- **Tune your CLAUDE.md files**: Refine them like any prompt, experiment with what works best
- **Curate <PERSON>'s allowed tools**: Customize the allowlist to permit additional tools you know are safe
- **Install GitHub CLI**: <PERSON> can use `gh` to interact with GitHub for issues, PRs, etc.

## 2. Give Claude More Tools

- **Use with bash tools**: Claude inherits your bash environment and can use your custom tools
- **Use with MCP (Model Context Protocol)**: Connect to MCP servers to access additional tools
- **Create custom slash commands**: Store prompt templates for repeated workflows

## 3. Common Workflows

- **Explore, plan, code, commit**: Ask Claude to read files, make a plan, implement, then commit
- **Write tests, commit; code, iterate, commit**: Test-driven development workflow
- **Write code, screenshot result, iterate**: Provide visual targets for UI development
- **Safe YOLO mode**: Use `--dangerously-skip-permissions` for uninterrupted work (with caution)
- **Codebase Q&A**: Use Claude for learning and exploring new codebases
- **Git interaction**: Use Claude for git operations like searching history, writing commit messages
- **GitHub interaction**: Create PRs, fix code review comments, triage issues
- **Jupyter notebook work**: Read and write notebooks, interpret outputs including images

## 4. Optimize Your Workflow

- **Be specific in instructions**: Clear directions upfront reduce need for corrections
- **Use images**: Claude works well with screenshots, drag-and-drop images, or file paths
- **Reference files**: Use tab-completion to quickly reference files or folders
- **Share URLs**: Paste specific URLs for Claude to fetch and read
- **Course correct early**: Ask for plans before coding, use Escape to interrupt, undo changes
- **Use /clear to focus context**: Reset context window between tasks
- **Use checklists for complex workflows**: Have Claude create and follow checklists
- **Pass data efficiently**: Copy/paste, pipe into Claude, or have Claude pull data

## 5. Headless Mode for Automation

- **Issue triage**: Automate labeling of new GitHub issues
- **Code linting**: Provide subjective code reviews beyond traditional linting

## 6. Multi-Claude Workflows

- **Separate writing and verification**: Have one Claude write code, another review it
- **Use multiple checkouts**: Create 3-4 git checkouts in separate folders for parallel tasks
- **Use git worktrees**: Lighter-weight alternative to multiple checkouts
- **Headless mode with custom harness**: Integrate Claude programmatically for large migrations or data pipelines

The article emphasizes that Claude Code is intentionally low-level and unopinionated, providing flexibility for engineers to develop their own best practices. These suggestions are starting points, and users are encouraged to experiment to find what works best for their specific needs.


---

### 对话 2

> **👤 用户**

使用中文回复  

> **🤖 Augment**

# Claude Code: 智能编码最佳实践总结

根据Anthropic的文章，以下是使用Claude Code（他们的智能编码命令行工具）的最佳实践总结：

## 1. 自定义你的设置

- **创建CLAUDE.md文件**：Claude会自动将这些文件纳入上下文，适合记录常用命令、代码风格、测试说明等
- **优化CLAUDE.md文件**：像优化任何提示词一样不断完善，测试什么内容最有效
- **自定义Claude的允许工具列表**：将你认为安全的工具添加到允许列表中
- **安装GitHub CLI**：Claude可以使用`gh`与GitHub交互，处理问题、PR等

## 2. 为Claude提供更多工具

- **使用bash工具**：Claude继承你的bash环境，可以使用你的自定义工具
- **使用MCP(Model Context Protocol)**：连接到MCP服务器以访问额外工具
- **创建自定义斜杠命令**：为重复工作流程存储提示模板

## 3. 常用工作流程

- **探索、规划、编码、提交**：让Claude阅读文件，制定计划，实施，然后提交
- **编写测试、提交；编码、迭代、提交**：测试驱动开发工作流
- **编写代码、截图结果、迭代**：为UI开发提供视觉目标
- **安全YOLO模式**：使用`--dangerously-skip-permissions`实现不间断工作（需谨慎）
- **代码库问答**：使用Claude学习和探索新代码库
- **Git交互**：使用Claude进行git操作，如搜索历史、编写提交消息
- **GitHub交互**：创建PR、修复代码审查评论、分类问题
- **Jupyter笔记本工作**：读写笔记本，解释包括图像在内的输出

## 4. 优化你的工作流程

- **提供具体指令**：前期明确指示减少后期纠正需求
- **使用图像**：Claude可以处理截图、拖放图像或文件路径
- **引用文件**：使用tab补全快速引用文件或文件夹
- **分享URL**：粘贴特定URL让Claude获取和阅读
- **及早纠正**：在编码前请求计划，使用Escape中断，撤销更改
- **使用/clear聚焦上下文**：在任务之间重置上下文窗口
- **为复杂工作流使用检查清单**：让Claude创建并遵循检查清单
- **高效传递数据**：复制/粘贴、管道输入Claude或让Claude拉取数据

## 5. 用于自动化的无头模式

- **问题分类**：自动标记新的GitHub问题
- **代码检查**：提供超越传统检查的主观代码审查

## 6. 多Claude工作流程

- **分离编写和验证**：让一个Claude编写代码，另一个审查
- **使用多个检出**：在单独文件夹中创建3-4个git检出以并行处理任务
- **使用git工作树**：作为多检出的轻量级替代方案
- **带自定义框架的无头模式**：以编程方式集成Claude，用于大型迁移或数据管道

文章强调Claude Code是有意设计成低级别且不固执己见的，为工程师提供灵活性以开发自己的最佳实践。这些建议只是起点，鼓励用户进行实验，找到最适合自己特定需求的方法。


---

### 对话 3

> **👤 用户**

在 自定义你的设置 这块文章中提到的技巧 tips 是什么？ 

> **🤖 Augment**

# Claude Code 自定义设置的技巧与提示

在文章的"自定义你的设置"部分，提到了以下具体技巧和提示：

## CLAUDE.md 文件的使用技巧

1. **文件位置灵活性**：
   - 可以放在仓库根目录（最常见用法）
   - 可以放在运行 `claude` 命令的任何父目录（适合单体仓库）
   - 可以放在运行 `claude` 命令的任何子目录（与上面相反）
   - 可以放在家目录（`~/.claude/CLAUDE.md`）以应用于所有 Claude 会话

2. **命名约定**：
   - 使用 `CLAUDE.md` 并将其加入 git 以便团队共享（推荐）
   - 或使用 `CLAUDE.local.md` 并将其加入 `.gitignore` 以保持个人设置

3. **自动生成**：
   - 运行 `/init` 命令时，Claude 会自动为你生成一个 `CLAUDE.md` 文件

4. **内容优化**：
   - 避免添加过多内容而不测试其有效性
   - 使用 `#` 键给 Claude 指令，它会自动将其纳入相关的 `CLAUDE.md`
   - 考虑通过 prompt improver 运行 `CLAUDE.md` 文件
   - 添加强调（如"IMPORTANT"或"YOU MUST"）以提高遵守度

## 允许工具列表的自定义技巧

有四种管理允许工具的方法：

1. **会话中选择"Always allow"**：当被提示时选择"始终允许"
   
2. **使用 `/allowed-tools` 命令**：启动 Claude Code 后添加或删除工具
   - 例如：添加 `Edit` 以始终允许文件编辑
   - 添加 `Bash(git commit:*)` 以允许 git 提交
   - 添加 `mcp__puppeteer__puppeteer_navigate` 以允许使用 Puppeteer MCP 服务器导航

3. **手动编辑配置文件**：
   - 编辑 `.claude/settings.json` 或 `~/.claude.json`
   - 建议将前者加入源代码控制以与团队共享

4. **使用 CLI 标志**：
   - 使用 `--allowedTools` 命令行标志设置会话特定权限

## GitHub 集成技巧

- 安装 `gh` CLI 工具，让 Claude 能够与 GitHub 交互
- 即使没有安装 `gh`，Claude 仍然可以使用 GitHub API 或 MCP 服务器（如果已安装）

这些技巧旨在帮助用户更有效地自定义 Claude Code 环境，使其更好地适应个人或团队的工作流程和安全需求。通过精心调整这些设置，可以显著提高 Claude 的效率和使用体验。


---

### 对话 4

> **👤 用户**

自定义斜杠命令 在文章中的使用方式是什么？ 具体的案例是什么？ 

> **🤖 Augment**

# Claude Code 自定义斜杠命令的使用方式与案例

## 自定义斜杠命令的使用方式

根据文章，自定义斜杠命令的使用方式如下：

1. **存储位置**：
   - 将提示模板以 Markdown 文件形式存储在 `.claude/commands` 文件夹中
   - 个人命令可以存储在 `~/.claude/commands` 文件夹中（在所有会话中可用）

2. **访问方式**：
   - 当你输入 `/` 时，这些命令会在斜杠命令菜单中可用
   - 可以将这些命令检入 git，使团队中的其他成员也能使用

3. **参数传递**：
   - 自定义斜杠命令可以包含特殊关键字 `$ARGUMENTS` 来传递命令调用中的参数

## 具体案例

文章中提供了一个具体的案例，是一个用于自动拉取和修复 GitHub 问题的斜杠命令：

```plaintext
Please analyze and fix the GitHub issue: $ARGUMENTS.

Follow these steps:

1. Use `gh issue view` to get the issue details
2. Understand the problem described in the issue
3. Search the codebase for relevant files
4. Implement the necessary changes to fix the issue
5. Write and run tests to verify the fix
6. Ensure code passes linting and type checking
7. Create a descriptive commit message
8. Push and create a PR

Remember to use the GitHub CLI (`gh`) for all GitHub-related tasks.
```

### 使用方法：

1. 将上述内容保存到 `.claude/commands/fix-github-issue.md` 文件中
2. 这样就可以在 Claude Code 中使用 `/project:fix-github-issue` 命令
3. 实际使用时，可以输入 `/project:fix-github-issue 1234` 来让 Claude 修复 issue #1234

### 工作原理：

- 当执行命令时，`$ARGUMENTS` 会被替换为你在命令后提供的参数（例如 "1234"）
- Claude 会按照模板中的步骤执行任务，使用 GitHub CLI 获取问题详情，理解问题，搜索相关文件，实现必要的更改，编写和运行测试，确保代码通过检查，创建提交消息，并推送创建 PR

这种自定义斜杠命令特别适合重复性工作流程，如调试循环、日志分析或像示例中的问题修复流程。通过创建这些命令，你可以将复杂的多步骤流程简化为单个命令，提高工作效率，并确保团队成员遵循一致的流程。


---

### 对话 5

> **👤 用户**

分析这段案例的逻辑，从中可以学习到什么？衍生到什么场景

> **🤖 Augment**

# 分析Claude Code自定义斜杠命令案例的逻辑与应用

## 案例逻辑分析

这个GitHub问题修复的斜杠命令案例展示了几个关键的逻辑设计：

1. **明确的任务定义**：开头清晰地定义了任务（"分析并修复GitHub问题"）并使用`$ARGUMENTS`参数接收具体的问题编号

2. **结构化步骤流程**：将复杂任务分解为8个有序步骤，从获取信息到最终提交

3. **完整的工作循环**：涵盖了整个开发周期（理解问题→实现更改→测试验证→提交代码）

4. **工具使用指导**：明确指示使用GitHub CLI进行所有GitHub相关任务

5. **质量保证机制**：包含测试、代码检查等确保质量的步骤

6. **自动化与人工智能结合**：利用Claude的能力自动执行流程，同时保持结构化方法

## 从中可以学习到什么

1. **任务模板化思维**：将重复性工作流程模板化，提高一致性和效率

2. **步骤分解方法**：将复杂任务分解为明确、可管理的步骤

3. **端到端流程设计**：设计覆盖从问题理解到解决方案部署的完整流程

4. **参数化命令设计**：使用参数使通用模板适应特定情况

5. **质量内置原则**：将质量检查作为流程的内置部分，而非事后考虑

6. **工具链集成思想**：整合现有工具链(如GitHub CLI)到自动化流程中

## 可以衍生的应用场景

1. **代码审查自动化**：
   ```
   /review-pr $ARGUMENTS
   ```
   自动检查PR，提供代码质量、测试覆盖率和最佳实践建议

2. **新功能开发流程**：
   ```
   /new-feature $ARGUMENTS
   ```
   从创建分支到实现功能、测试和提交PR的完整流程

3. **代码重构助手**：
   ```
   /refactor-code $ARGUMENTS
   ```
   分析指定代码区域并提供重构建议，然后执行重构

4. **依赖更新助手**：
   ```
   /update-dependency $ARGUMENTS
   ```
   更新指定依赖，运行测试确保兼容性，创建更新PR

5. **文档生成器**：
   ```
   /generate-docs $ARGUMENTS
   ```
   为指定代码模块生成文档，包括API参考和使用示例

6. **性能分析与优化**：
   ```
   /optimize-performance $ARGUMENTS
   ```
   分析指定代码的性能瓶颈并实施优化

7. **安全漏洞修复**：
   ```
   /fix-security-issue $ARGUMENTS
   ```
   分析安全问题，实施修复并添加测试以防止回归

8. **代码迁移助手**：
   ```
   /migrate-code $ARGUMENTS
   ```
   将代码从一个框架或语言迁移到另一个

9. **数据分析工作流**：
   ```
   /analyze-data $ARGUMENTS
   ```
   执行数据清洗、分析和可视化的标准流程

10. **CI/CD管道调试**：
    ```
    /debug-pipeline $ARGUMENTS
    ```
    分析CI/CD失败并提供修复建议

这种模板化、参数化的工作流程方法不仅适用于软件开发，还可以扩展到数据科学、DevOps、技术文档和项目管理等多个领域。核心价值在于将专家知识编码为可重复使用的流程，提高团队效率和一致性。


---

### 对话 6

> **👤 用户**

结合你衍生的应用场景，参考你分析的案例，为我输出每一个指令的提示词，Markdown 代码块输出

> **🤖 Augment**

# Claude Code 自定义斜杠命令提示词模板

以下是基于原案例分析衍生的各种应用场景的具体提示词模板，每个都以 Markdown 代码块形式呈现：

## 1. 代码审查自动化

```markdown
Please perform a thorough code review of PR #$ARGUMENTS.

Follow these steps:

1. Use `gh pr view $ARGUMENTS` to get the PR details
2. Use `gh pr checkout $ARGUMENTS` to check out the PR code
3. Analyze the code changes for:
   - Code quality and best practices
   - Potential bugs or edge cases
   - Test coverage
   - Performance implications
   - Security concerns
4. Run linting and static analysis tools
5. Run the test suite to verify functionality
6. Provide a detailed review summary with:
   - Overall assessment
   - Specific issues found (if any)
   - Improvement suggestions
7. Add your review comments to the PR using `gh pr comment`

Remember to be constructive and specific in your feedback, focusing on both strengths and areas for improvement.
```

## 2. 新功能开发流程

```markdown
Please implement the new feature: $ARGUMENTS.

Follow these steps:

1. Create a new branch with a descriptive name related to the feature
2. Search the codebase to understand where the feature should be implemented
3. Design the implementation approach, considering:
   - Architecture consistency
   - Reusability of existing components
   - Performance implications
   - User experience
4. Implement the feature with appropriate:
   - Error handling
   - Logging
   - Documentation
5. Write comprehensive tests (unit, integration, etc.)
6. Run the test suite to verify functionality
7. Update documentation if necessary
8. Create a descriptive commit message
9. Push and create a PR with a detailed description of the feature

Remember to follow the project's coding standards and design patterns throughout implementation.
```

## 3. 代码重构助手

```markdown
Please analyze and refactor the code in: $ARGUMENTS.

Follow these steps:

1. Examine the specified code to understand its functionality
2. Identify code smells and areas for improvement:
   - Duplicated code
   - Long methods/functions
   - Complex conditional logic
   - Poor naming
   - Tight coupling
3. Create a refactoring plan that preserves existing functionality
4. Implement refactoring in small, testable increments
5. Run tests after each change to ensure functionality is preserved
6. Apply design patterns where appropriate
7. Improve naming, comments, and documentation
8. Create a summary of changes made and benefits gained
9. Commit the refactored code with a descriptive message

Remember that the goal is to improve code quality while maintaining exact functionality. Avoid introducing new features during refactoring.
```

## 4. 依赖更新助手

```markdown
Please update the dependency: $ARGUMENTS.

Follow these steps:

1. Check the current version of the dependency in the project
2. Research the latest stable version and its changelog
3. Identify breaking changes or deprecations that might affect the codebase
4. Update the dependency in the appropriate configuration files
5. Update any code that needs to be modified due to API changes
6. Run the test suite to identify any compatibility issues
7. Fix any failing tests or compatibility problems
8. Document the update process and any significant changes required
9. Create a commit with:
   - The dependency update
   - Any required code changes
   - A detailed commit message explaining the update
10. Push and create a PR for the update

Remember to include the changelog or release notes in the PR description to help reviewers understand the impact of the update.
```

## 5. 文档生成器

```markdown
Please generate comprehensive documentation for: $ARGUMENTS.

Follow these steps:

1. Analyze the specified code module/component to understand its:
   - Purpose and functionality
   - Public API
   - Usage patterns
   - Dependencies
2. Generate API reference documentation including:
   - Function/method signatures
   - Parameter descriptions
   - Return value descriptions
   - Exception/error handling
3. Create usage examples demonstrating common use cases
4. Document any configuration options or environment requirements
5. Add diagrams or flowcharts if helpful for understanding
6. Include performance considerations or best practices
7. Format the documentation according to the project's standards
8. Update any existing documentation indexes or tables of contents
9. Commit the documentation with a descriptive message

Remember to write documentation that is clear, concise, and accessible to the intended audience, whether they are new developers or experienced users.
```

## 6. 性能分析与优化

```markdown
Please analyze and optimize the performance of: $ARGUMENTS.

Follow these steps:

1. Profile the specified code to identify performance bottlenecks
2. Analyze the current implementation for:
   - Algorithmic efficiency
   - Resource usage (CPU, memory, I/O)
   - Concurrency issues
   - Database query performance (if applicable)
3. Research optimization strategies for the identified bottlenecks
4. Implement optimizations in order of potential impact
5. Benchmark before and after each optimization to measure improvement
6. Ensure optimizations don't introduce bugs or regressions
7. Document the optimizations applied and their measured impact
8. Create a summary report of performance improvements
9. Commit optimized code with detailed explanation of changes

Remember that premature optimization is the root of all evil. Focus on measurable bottlenecks and maintain code readability unless extreme performance is required.
```

## 7. 安全漏洞修复

```markdown
Please analyze and fix the security vulnerability: $ARGUMENTS.

Follow these steps:

1. Research the specified security vulnerability to understand:
   - Attack vectors
   - Potential impact
   - Common mitigation strategies
2. Locate vulnerable code in the codebase
3. Develop a fix that addresses the root cause, not just symptoms
4. Add security tests that verify the vulnerability is fixed
5. Check for similar vulnerabilities elsewhere in the codebase
6. Update dependencies if the vulnerability is in a third-party package
7. Document the vulnerability and fix for future reference
8. Create a commit with:
   - The security fix
   - New security tests
   - A detailed commit message (without revealing exploit details)
9. Push and create a PR marked as security-related

Remember to follow responsible disclosure practices and avoid including detailed exploit information in public repositories.
```

## 8. 代码迁移助手

```markdown
Please migrate the code from $ARGUMENTS.

Follow these steps:

1. Analyze the source code to understand:
   - Core functionality
   - Dependencies and external integrations
   - Architecture and design patterns
2. Research best practices for the target language/framework
3. Create a migration plan with prioritized components
4. Implement the core functionality in the target language/framework
5. Adapt the code to follow idioms and best practices of the target platform
6. Create equivalent tests in the target environment
7. Verify functionality matches the original implementation
8. Document any intentional differences or improvements
9. Create a migration guide for other developers
10. Commit the migrated code with detailed documentation

Remember that a good migration preserves functionality while embracing the strengths of the target platform, rather than just translating syntax.
```

## 9. 数据分析工作流

```markdown
Please perform data analysis on: $ARGUMENTS.

Follow these steps:

1. Load and examine the dataset to understand:
   - Data structure and schema
   - Data types and distributions
   - Missing values and outliers
2. Clean the data:
   - Handle missing values
   - Remove or transform outliers
   - Normalize or standardize as needed
3. Perform exploratory data analysis:
   - Calculate descriptive statistics
   - Create visualizations of key distributions and relationships
   - Identify patterns and correlations
4. Apply appropriate analytical methods:
   - Statistical tests
   - Machine learning models (if applicable)
   - Time series analysis (if applicable)
5. Interpret the results and draw conclusions
6. Create visualizations that effectively communicate findings
7. Document the entire analysis process and methodology
8. Summarize key insights and recommendations
9. Save the analysis as a reproducible notebook or script

Remember to maintain scientific rigor throughout the analysis and clearly state any assumptions or limitations.
```

## 10. CI/CD管道调试

```markdown
Please debug the CI/CD pipeline failure: $ARGUMENTS.

Follow these steps:

1. Examine the pipeline logs to identify the specific failure point
2. Analyze the error messages and context
3. Check recent changes that might have caused the failure:
   - Code changes
   - Configuration changes
   - Environment or dependency changes
4. Reproduce the failure locally if possible
5. Research the specific error to understand potential causes
6. Implement a fix addressing the root cause
7. Test the fix locally to verify it resolves the issue
8. Update pipeline configuration if necessary
9. Document the issue and solution for future reference
10. Create a commit with:
    - The fix
    - Any pipeline configuration updates
    - A detailed commit message explaining the issue and solution

Remember to consider whether this is a one-time issue or a systemic problem that requires a more robust solution to prevent future occurrences.
```

这些模板可以保存到相应的 `.claude/commands/` 目录中，以便在 Claude Code 中通过斜杠命令使用。每个模板都遵循了原案例的结构化步骤方法，同时针对特定场景进行了定制。


---

### 对话 7

> **👤 用户**

为我总结 常见的工作流程 

> **🤖 Augment**

# Claude Code 常见工作流程总结

根据文章内容，以下是 Claude Code 中常见且有效的工作流程总结：

## 1. 探索、规划、编码、提交

**适用场景**：多种问题的通用工作流

**流程步骤**：
- 让 Claude 阅读相关文件、图像或 URL，提供指导或具体文件名
- 要求 Claude 制定解决特定问题的计划（使用"think"触发扩展思考模式）
- 让 Claude 用代码实现解决方案
- 让 Claude 提交结果并创建 PR

**关键提示**：
- 前两步至关重要，避免 Claude 直接跳到编码
- 可以使用"think"、"think hard"、"think harder"、"ultrathink"逐级增加思考预算
- 可以让 Claude 创建文档或 GitHub issue 记录计划，以便需要时回退

## 2. 编写测试、提交；编码、迭代、提交

**适用场景**：可通过单元测试、集成测试或端到端测试轻松验证的更改

**流程步骤**：
- 让 Claude 基于预期的输入/输出对编写测试
- 让 Claude 运行测试并确认它们失败
- 满意后让 Claude 提交测试
- 让 Claude 编写通过测试的代码（不修改测试）
- 满意后让 Claude 提交代码

**关键提示**：
- 明确说明正在进行测试驱动开发，避免创建模拟实现
- 当 Claude 有明确目标迭代时表现最佳

## 3. 编写代码、截图结果、迭代

**适用场景**：UI 开发或需要视觉反馈的任务

**流程步骤**：
- 为 Claude 提供截图工具（如 Puppeteer MCP 服务器）
- 提供视觉模型（通过复制/粘贴或拖放图像）
- 让 Claude 实现设计，截取结果截图，并迭代直到匹配模型
- 满意后让 Claude 提交

**关键提示**：
- Claude 的输出通常会随着迭代显著改进
- 2-3 次迭代后结果通常会好得多

## 4. 安全 YOLO 模式

**适用场景**：修复 lint 错误或生成样板代码等简单任务

**流程步骤**：
- 使用 `claude --dangerously-skip-permissions` 绕过所有权限检查
- 让 Claude 不间断工作直到完成

**关键提示**：
- 风险较高，可能导致数据丢失或系统损坏
- 建议在没有互联网访问的容器中使用

## 5. 代码库问答

**适用场景**：学习和探索新代码库，特别是入职新项目时

**流程步骤**：
- 向 Claude 提问与代码库相关的问题
- Claude 会自动搜索代码库寻找答案

**关键提示**：
- 无需特殊提示，直接提问即可
- 在 Anthropic 已成为核心入职工作流程，显著提高上手速度

## 6. Git 交互

**适用场景**：日常 Git 操作

**流程步骤**：
- 让 Claude 搜索 Git 历史
- 让 Claude 编写提交消息
- 让 Claude 处理复杂的 Git 操作（如还原文件、解决变基冲突等）

**关键提示**：
- 许多 Anthropic 工程师使用 Claude 处理 90%+ 的 Git 交互

## 7. GitHub 交互

**适用场景**：GitHub 相关任务

**流程步骤**：
- 让 Claude 创建 PR
- 让 Claude 实现代码审查评论的一次性解决方案
- 让 Claude 修复失败的构建或 linter 警告
- 让 Claude 分类和筛选开放的 issues

**关键提示**：
- Claude 理解"pr"这样的简写
- 无需记住 `gh` 命令行语法

## 8. Jupyter 笔记本工作

**适用场景**：数据科学和研究工作

**流程步骤**：
- 让 Claude 读取和编写 Jupyter 笔记本
- Claude 可以解释输出，包括图像

**关键提示**：
- 推荐在 VS Code 中并排打开 Claude Code 和 .ipynb 文件
- 明确告诉 Claude 使笔记本或数据可视化"美观"有助于提醒它优化人类查看体验

这些工作流程展示了 Claude Code 的灵活性，可以根据具体任务和个人偏好进行调整。文章强调，Claude Code 不强制特定工作流程，而是提供了一个灵活的框架，让用户可以根据自己的需求进行定制。


---

### 对话 8

> **👤 用户**

为我不要有遗漏的翻译原来的文章，并且新建一个 md 文件存储他

> **🤖 Augment**

我将为您完整翻译原文，并创建一个 Markdown 文件来存储翻译内容。

I'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?


---

### 对话 9

> **👤 用户**

在这篇文章中提到的 优化你的工作流程 ，请为我梳理所有的建议

> **🤖 Augment**

# Claude Code 优化工作流程的建议

根据文章中"优化你的工作流程"部分，以下是所有提到的建议：

## 1. 在指令中具体明确

- **提供具体指令**：Claude Code 在收到更具体的指令时成功率显著提高，尤其是在首次尝试时
- **前期明确方向**：提前给出清晰的指示可以减少后期需要纠正的情况

**对比示例**：
| 不佳示例 | 良好示例 |
| --- | --- |
| 为 foo.py 添加测试 | 为 foo.py 编写新的测试用例，覆盖用户登出的边缘情况。避免使用模拟 |
| 为什么 ExecutionFactory 有这么奇怪的 API？ | 查看 ExecutionFactory 的 git 历史并总结其 API 是如何形成的 |
| 添加日历小部件 | 查看主页上现有小部件的实现方式，了解模式以及代码和接口如何分离。HotDogWidget.php 是一个很好的起点。然后，遵循模式实现一个新的日历小部件，让用户选择月份并前后翻页选择年份。从头开始构建，不使用除了代码库中已使用的库之外的其他库。 |

Claude 可以推断意图，但无法读心。具体性能更好地满足期望。

## 2. 向 Claude 提供图像

Claude 通过多种方法擅长处理图像和图表：

- **粘贴截图**（提示：在 macOS 中使用 cmd+ctrl+shift+4 截图到剪贴板，然后使用 ctrl+v 粘贴。注意这不是通常在 Mac 上使用的 cmd+v，远程不起作用）
- **拖放**图像直接到提示输入框
- **提供文件路径**指向图像

这在使用设计模型作为 UI 开发参考点，以及使用可视化图表进行分析和调试时特别有用。即使不向上下文添加视觉内容，明确告诉 Claude 结果的视觉吸引力有多重要也会有所帮助。

## 3. 提及你希望 Claude 查看或处理的文件

使用 tab 补全功能快速引用存储库中任何位置的文件或文件夹，帮助 Claude 找到或更新正确的资源。

## 4. 向 Claude 提供 URL

在提示旁边粘贴特定 URL 供 Claude 获取和阅读。为避免对相同域名（如 docs.foo.com）的权限提示，使用 `/allowed-tools` 将域名添加到允许列表。

## 5. 及早频繁地纠正方向

虽然自动接受模式（shift+tab 切换）允许 Claude 自主工作，但通常通过积极协作和指导 Claude 的方法会获得更好的结果。可以通过在开始时彻底解释任务获得最佳结果，但也可以随时纠正 Claude。

这四个工具有助于纠正方向：

- **要求 Claude 制定计划**再编码。明确告诉它在你确认计划看起来不错之前不要编码。
- **按 Escape 中断** Claude 的任何阶段（思考、工具调用、文件编辑），保留上下文以便重定向或扩展指令。
- **双击 Escape 返回历史记录**，编辑之前的提示，探索不同方向。可以编辑提示并重复直到获得所需结果。
- **要求 Claude 撤销更改**，通常与选项 #2 结合使用，采取不同方法。

虽然 Claude Code 偶尔能在第一次尝试时完美解决问题，但使用这些纠正工具通常能更快地产生更好的解决方案。

## 6. 使用 `/clear` 保持上下文集中

在长时间会话中，Claude 的上下文窗口可能会充满无关的对话、文件内容和命令。这可能会降低性能，有时会分散 Claude 的注意力。在任务之间频繁使用 `/clear` 命令重置上下文窗口。

## 7. 对复杂工作流使用检查清单和草稿

对于具有多个步骤或需要详尽解决方案的大型任务（如代码迁移、修复大量 lint 错误或运行复杂构建脚本），通过让 Claude 使用 Markdown 文件（甚至 GitHub issue！）作为检查清单和工作草稿来提高性能：

例如，要修复大量 lint 问题，可以执行以下操作：

1. **告诉 Claude 运行 lint 命令**并将所有结果错误（带有文件名和行号）写入 Markdown 检查清单
2. **指示 Claude 逐一解决每个问题**，在检查并移至下一个之前修复和验证

## 8. 向 Claude 传递数据

向 Claude 提供数据的几种方法：

- **复制粘贴**直接到提示中（最常见的方法）
- **通过管道输入 Claude Code**（例如，`cat foo.txt | claude`），特别适用于日志、CSV 和大数据
- **告诉 Claude 通过 bash 命令、MCP 工具或自定义斜杠命令拉取数据**
- **要求 Claude 读取文件**或获取 URL（也适用于图像）

大多数会话涉及这些方法的组合。例如，可以通过管道输入日志文件，然后告诉 Claude 使用工具拉取额外上下文来调试日志。

这些建议适用于所有工作流程，旨在帮助用户优化与 Claude Code 的交互，提高效率和成功率。通过实施这些策略，用户可以更有效地利用 Claude 的能力，获得更好的结果。

---

### 对话 10

> **👤 用户**

在指令中要具体明确，这块对于编程者来说难以做到，能否借助于 AI 来进行辅助，将用户输入的初始Prompt 来进行优化，请你基于这个点给出具体的优化提示词【系统提示词】

> **🤖 Augment**

我理解您想要一个系统提示词，用于帮助开发者通过 AI 辅助将初始的模糊指令优化为更具体明确的指令。下面我为您创建这样一个系统提示词：


我已经创建了一个名为 `prompt_optimizer.md` 的文件，其中包含了一个系统提示词，专门用于将开发者的初始模糊指令优化为更具体明确的指令。

这个系统提示词的主要特点：

1. **明确定义了 AI 的角色**：作为指令优化助手，专门帮助开发者优化编程指令

2. **详细的核心职责**：分析初始指令、提取关键信息、结构化重组、添加缺失细节、澄清模糊之处、保持原意

3. **标准化输出格式**：包括原始指令、优化指令和优化说明三个部分

4. **具体的优化原则**：具体性、结构化、上下文丰富、明确约束、预期结果、错误处理、资源引用

5. **针对不同指令类型的适配策略**：功能实现、调试任务、重构请求、架构设计、性能优化

6. **实际示例转换**：展示了如何将"写个函数处理用户输入"这样模糊的指令转化为详细的函数规范

7. **提问技巧**：当初始指令过于模糊时，提供了一系列有针对性的问题来获取必要信息

这个系统提示词可以作为 AI 编码助手的前置处理器，帮助开发者将初始的、可能不够明确的指令转化为结构化、详细的指令，从而提高 AI 理解和执行任务的准确性。这对于不习惯提供详细指令的开发者特别有帮助，可以显著提高他们与 AI 编码助手协作的效率。


---

### 对话 11

> **👤 用户**

分析  优化你的工作流程  的建议的必要性、给出具体的理由，

> **🤖 Augment**

# "优化你的工作流程"建议的必要性分析

## 1. 提供具体指令的必要性

**必要性理由**：
- **减少迭代次数**：具体指令使 Claude 能够一次性理解任务需求，减少来回沟通和修改的次数
- **提高准确性**：明确的指示能让 Claude 更准确地理解预期结果，减少误解和错误实现
- **节省时间**：清晰的指令减少了 Claude 需要自行推断或猜测的内容，加快了任务完成速度
- **降低认知负担**：当开发者提供具体指令时，他们不需要不断监控和纠正 Claude 的工作方向

**实际影响**：文章中的对比示例（"add tests for foo.py" vs 详细版本）清晰展示了具体指令如何提供更多上下文和期望，使 Claude 能够更好地满足实际需求。

## 2. 使用图像的必要性

**必要性理由**：
- **视觉信息传递效率**：图像能在一瞬间传递大量信息，特别是对于设计和布局相关任务
- **减少描述偏差**：通过图像可以避免文字描述中可能出现的主观理解差异
- **提供精确参考**：对于 UI 开发，图像提供了精确的视觉目标，减少了对"看起来应该是什么样"的猜测
- **跨领域沟通**：图像帮助弥合设计师和开发者之间的沟通差距

**实际影响**：Claude 的视觉理解能力使其能够分析截图、设计模型和图表，从而更准确地实现视觉要求或解决视觉相关问题。

## 3. 引用文件的必要性

**必要性理由**：
- **精确定位**：直接引用文件避免了模糊描述可能导致的混淆
- **上下文完整性**：让 Claude 查看完整文件提供了更全面的上下文理解
- **减少手动复制粘贴**：避免了开发者需要手动复制代码片段的麻烦
- **保持代码一致性**：确保 Claude 基于最新的代码版本工作，而非过时的描述

**实际影响**：使用 tab 补全快速引用文件的功能大大提高了与 Claude 协作的效率，特别是在处理复杂代码库时。

## 4. 提供 URL 的必要性

**必要性理由**：
- **获取外部知识**：让 Claude 访问最新的文档、API 参考或教程
- **减少信息转述偏差**：直接获取原始信息，避免开发者转述可能引入的偏差
- **扩展上下文范围**：提供 Claude 无法通过本地文件获取的信息
- **保持信息时效性**：确保 Claude 基于最新的外部信息工作

**实际影响**：通过将特定 URL 添加到允许列表，可以让 Claude 无需每次都请求权限，进一步提高工作流的流畅性。

## 5. 及早纠正的必要性

**必要性理由**：
- **防止错误累积**：早期纠正避免了错误思路导致的大量无效工作
- **保持方向一致**：确保 Claude 的工作始终与开发者的意图保持一致
- **降低修改成本**：早期的小调整比后期的大规模重构成本低得多
- **提高学习效率**：及时反馈帮助 Claude 更好地理解开发者的偏好和要求

**实际影响**：文章提供的四种纠正工具（请求计划、使用 Escape 中断、编辑历史提示、请求撤销更改）为开发者提供了灵活的纠正机制。

## 6. 使用 /clear 聚焦上下文的必要性

**必要性理由**：
- **防止上下文污染**：清除不相关的历史对话避免了 Claude 被旧信息干扰
- **优化性能**：减轻上下文窗口负担可能提高 Claude 的响应速度和准确性
- **明确任务边界**：帮助 Claude 理解何时开始新任务，避免任务间的混淆
- **减少注意力分散**：聚焦于当前任务的相关信息，提高工作效率

**实际影响**：在长会话或处理多个不同任务时，定期使用 `/clear` 命令可以显著提高 Claude 的表现。

## 7. 使用检查清单的必要性

**必要性理由**：
- **任务结构化**：将复杂任务分解为可管理的步骤，提高完成率
- **进度跟踪**：清晰地显示已完成和待完成的项目，便于监控进度
- **减少遗漏**：确保所有必要步骤都被考虑和执行
- **降低认知负担**：外部化任务跟踪，减轻开发者和 Claude 的记忆负担

**实际影响**：对于大型任务（如代码迁移、修复多个 lint 错误），使用 Markdown 文件或 GitHub issue 作为检查清单显著提高了任务完成的系统性和全面性。

## 8. 高效传递数据的必要性

**必要性理由**：
- **减少数据处理障碍**：简化数据输入过程，消除不必要的格式转换
- **处理大量数据**：通过管道或文件读取处理超出常规对话限制的数据量
- **保持数据完整性**：避免手动复制可能导致的数据丢失或损坏
- **工作流集成**：与现有开发工具和流程无缝衔接

**实际影响**：多种数据输入方法（复制粘贴、管道输入、工具拉取、文件读取）提供了处理各种数据类型和规模的灵活性。

## 总体必要性

这些优化建议的整体必要性在于它们共同构建了一个更高效、更可靠的人机协作系统。它们不仅提高了单次交互的质量，还通过建立良好的工作习惯和沟通模式，提升了长期协作的效率。这些建议反映了 Anthropic 团队和外部用户的实际经验，代表了当前智能编码助手使用的最佳实践。

通过实施这些优化，开发者可以显著减少与 Claude 协作时的摩擦和误解，将更多精力集中在创造性和战略性工作上，而非管理 AI 助手的行为。


---

### 对话 12

> **👤 用户**

对于以上8个必要性的点上，请你说出用户与 AI 的优秀交互示例，目的是让用户更好的理解必要性，加深对其的印象。

> **🤖 Augment**

# AI 工作流程优化：8个必要性点的优秀交互示例

## 1. 提供具体指令的优秀交互示例

**不佳示例**：
```
用户：帮我写个登录功能。
AI：好的，我可以帮您编写登录功能。您想要使用什么技术栈？需要什么特性？
用户：用React吧，要能记住用户。
AI：[提供基础实现，但缺少许多细节和边缘情况处理]
用户：这个不对，我还需要验证码功能。
AI：[修改代码添加验证码]
用户：还需要支持第三方登录。
AI：[再次修改代码]...
```

**优秀示例**：
```
用户：请为我的React应用实现一个登录功能，具有以下特性：
1. 支持邮箱/密码登录，包含表单验证
2. 集成Google和Facebook第三方登录
3. 实现"记住我"功能，使用localStorage存储令牌
4. 添加图形验证码防止暴力破解
5. 登录成功后重定向到用户仪表板
6. 登录失败显示具体错误信息
7. 支持响应式设计，在移动设备上良好展示

我们使用React 18，样式用Tailwind CSS，后端API已经存在，端点是/api/auth/login。

AI：[提供完整、符合所有要求的实现，一次性满足需求]
用户：太棒了，正是我需要的！
```

**为何更好**：具体指令一次性提供了所有必要信息，包括技术栈、功能需求和集成点，使AI能够直接提供完整解决方案，避免了多轮迭代和沟通成本。

## 2. 使用图像的优秀交互示例

**不佳示例**：
```
用户：我需要一个产品卡片组件，要现代简约风格，有产品图片、名称、价格和购买按钮。
AI：[提供代码实现]
用户：不对，我想要图片在上面，下面是产品信息。
AI：[修改代码]
用户：按钮颜色不对，应该更突出一些。
AI：[再次修改]
用户：整体间距看起来不协调...
```

**优秀示例**：
```
用户：[上传设计图] 请根据这个设计图实现一个产品卡片组件。这是我们设计师创建的Figma设计。需要使用React和Styled Components实现，保持视觉上的一致性，包括字体、颜色、间距和阴影效果。卡片需要在悬停时有轻微放大效果。

AI：我看到了您提供的设计图。这是一个产品卡片，具有以下特点：
- 顶部是产品图片，占据约60%的卡片高度
- 下方是产品信息区，包含产品名称(16px粗体)、价格(18px突出显示)和评分(5星制)
- 底部是一个突出的蓝色"添加到购物车"按钮，带有圆角和悬停效果
- 整个卡片有轻微阴影和圆角边框

[提供精确匹配设计的代码实现]

用户：完美！与设计完全一致。
```

**为何更好**：图像直接传达了所有视觉细节，消除了文字描述的局限性，使AI能够准确理解设计意图并实现匹配的组件，大大减少了沟通成本。

## 3. 引用文件的优秀交互示例

**不佳示例**：
```
用户：我们的用户服务有个bug，用户注册后没收到确认邮件。
AI：这可能有多种原因。您能分享一下用户注册的代码吗？
用户：[复制粘贴部分代码]
AI：我看到了注册代码，但邮件发送部分在哪里？
用户：[复制粘贴更多代码]
AI：我还需要看一下邮件服务的配置...
```

**优秀示例**：
```
用户：我们的用户注册后没收到确认邮件。问题可能出在这些文件中：
- src/services/UserService.js
- src/services/EmailService.js
- src/config/email.config.js

AI：我已查看了您引用的文件。发现了问题所在：

在UserService.js中，用户注册成功后调用了EmailService.sendConfirmationEmail()，但在EmailService.js中，这个方法存在一个错误：

````javascript path=src/services/EmailService.js mode=EXCERPT
async sendConfirmationEmail(user) {
  // 问题在这里：使用了错误的配置键名
  const mailConfig = this.config.emailConfirmation;
  // 应该使用 this.config.userEmailConfirmation
  ...
}
````

同时，在email.config.js中，配置是这样定义的：

````javascript path=src/config/email.config.js mode=EXCERPT
module.exports = {
  // 注意这里的键名是userEmailConfirmation而非emailConfirmation
  userEmailConfirmation: {
    subject: 'Confirm Your Registration',
    template: 'confirmation-email'
  }
}
````

建议修改EmailService.js中的代码，使用正确的配置键名。

用户：太棒了！问题找到了，正是配置键名不匹配。
```

**为何更好**：直接引用相关文件使AI能够全面了解代码上下文，快速定位问题根源，而不是通过片段式的代码复制粘贴逐步构建理解。

## 4. 提供URL的优秀交互示例

**不佳示例**：
```
用户：我需要集成Stripe支付到我的React应用中。
AI：好的，我可以帮您实现Stripe集成。首先需要安装Stripe的React库...
用户：但我们需要使用最新版本的API，支持3D安全验证。
AI：[提供可能不是最新的实现方法]
用户：这个实现方式好像已经过时了...
```

**优秀示例**：
```
用户：我需要集成Stripe支付到我的React应用中。请参考以下文档：
https://stripe.com/docs/payments/accept-a-payment?platform=react
https://stripe.com/docs/strong-customer-authentication

我们需要支持信用卡支付，并确保符合欧盟的SCA规定。

AI：感谢您提供的Stripe文档链接。我已查看了最新的React集成指南和强客户认证(SCA)要求。

基于最新文档，以下是实现Stripe支付的步骤，包含SCA支持：

[提供基于最新文档的详细实现，包括Elements组件、PaymentIntents API和3D Secure认证]

这个实现使用了Stripe的最新推荐方法，确保符合欧盟的SCA规定，同时提供了良好的用户体验。

用户：非常感谢！实现方案与官方最新推荐完全一致。
```

**为何更好**：提供官方文档URL确保AI基于最新、权威的信息工作，避免过时或不准确的实现方案，同时节省了用户需要解释复杂技术细节的时间。

## 5. 及早纠正的优秀交互示例

**不佳示例**：
```
用户：帮我创建一个数据可视化仪表板。
AI：[开始编写大量代码实现一个完整仪表板]
用户：[等待AI完成大量工作后] 等等，我们实际上需要使用D3.js，而且只需要折线图和柱状图。
AI：[不得不丢弃之前的工作，重新开始]
```

**优秀示例**：
```
用户：帮我创建一个数据可视化仪表板。

AI：在我开始编写代码前，我想先确认一些细节：
1. 您想使用什么技术/库来实现可视化？(例如Chart.js, D3.js, Recharts等)
2. 需要哪些类型的图表？
3. 数据源是什么格式？
4. 是否需要交互功能，如过滤或钻取？

用户：我们需要使用D3.js，主要是折线图和柱状图，数据来自REST API，返回JSON格式。需要支持日期范围过滤。

AI：谢谢您的详细信息。在实现完整仪表板前，我先提供一个基本计划：

1. 创建基础布局组件
2. 实现D3.js折线图组件
3. 实现D3.js柱状图组件
4. 添加日期范围过滤器
5. 连接REST API数据源

这个方向是否符合您的需求？

用户：看起来不错，但我们还需要添加一个导出PDF的功能。

AI：非常感谢您的补充。我会将PDF导出功能添加到计划中：

[更新后的计划包含PDF导出功能]

现在我将开始实现第一步：基础布局组件...

[开始实现，按照已确认的计划逐步进行]
```

**为何更好**：AI在投入大量工作前先确认需求并提出计划，用户有机会及早纠正方向，避免了后期大规模返工，提高了整体效率。

## 6. 使用/clear聚焦上下文的优秀交互示例

**不佳示例**：
```
[长时间会话，已讨论过用户认证、数据库设计和API端点]

用户：现在我们需要实现前端路由。
AI：[提供的路由实现混合了之前讨论的认证逻辑和不相关的数据库考虑]
用户：这看起来太复杂了，我只需要简单的React Router设置。
AI：抱歉，我会简化。[但仍然受到之前上下文的影响]
```

**优秀示例**：
```
[长时间会话，已讨论过用户认证、数据库设计和API端点]

用户：/clear
现在我们需要实现前端路由。我们使用React Router v6，需要设置以下路由：
- 首页 (/)
- 产品列表 (/products)
- 产品详情 (/products/:id)
- 用户个人资料 (/profile)，需要认证
- 结账页面 (/checkout)，需要认证

AI：感谢您使用/clear命令并提供明确的需求。以下是使用React Router v6的路由实现：

[提供简洁、聚焦的React Router v6实现，没有受到之前讨论的干扰]

这个实现包含了所有您指定的路由，并为需要认证的路由添加了保护组件。

用户：完美！正是我需要的简洁路由配置。
```

**为何更好**：使用/clear命令清除了之前的上下文，使AI能够专注于当前任务，提供更简洁、更相关的解决方案，避免了上下文污染导致的复杂性。

## 7. 使用检查清单的优秀交互示例

**不佳示例**：
```
用户：我们需要将项目从JavaScript迁移到TypeScript。
AI：[开始提供大量转换代码和说明]
用户：等等，我们应该先从哪些文件开始？
AI：通常从核心组件开始是个好主意...
[转换过程混乱，没有清晰的进度跟踪，容易遗漏文件]
```

**优秀示例**：
```
用户：我们需要将项目从JavaScript迁移到TypeScript。

AI：这是一个大型任务，建议我们创建一个检查清单来系统地进行迁移。我可以帮您创建一个迁移计划文档。

用户：好主意，请创建。

AI：我已创建了TypeScript迁移检查清单：migration-checklist.md

首先，我们需要扫描项目结构，确定需要迁移的文件：

```bash
find src -name "*.js" -o -name "*.jsx" > files-to-migrate.txt
```

检查清单已创建，包含以下主要步骤：

1. [ ] 安装TypeScript和相关依赖
2. [ ] 创建tsconfig.json配置文件
3. [ ] 迁移核心类型定义和接口
4. [ ] 按优先级迁移文件：
   - [ ] 共享工具函数和常量
   - [ ] 核心模型和服务
   - [ ] UI组件
   - [ ] 页面组件
5. [ ] 更新构建配置
6. [ ] 运行类型检查并修复错误
7. [ ] 更新测试

我们可以逐步完成这个清单。要开始第一步吗？

用户：是的，让我们开始第一步。

AI：[执行第一步，然后在检查清单中标记为完成]
现在我们已完成第一步，接下来进行第二步...

[系统地完成每个步骤，清晰跟踪进度]
```

**为何更好**：使用检查清单将大型任务分解为可管理的步骤，提供了清晰的进度跟踪和结构化方法，确保没有步骤被遗漏，同时减轻了认知负担。

## 8. 高效传递数据的优秀交互示例

**不佳示例**：
```
用户：我需要分析这个日志文件中的错误模式。
AI：请提供日志文件内容。
用户：[尝试复制粘贴几千行日志，但由于太大而不完整]
AI：我只能看到部分日志，难以进行完整分析...
```

**优秀示例**：
```
用户：我需要分析production-2023-05-15.log中的错误模式。这是一个10MB的日志文件，包含我们生产服务器昨天的所有日志。

AI：对于大型日志文件，直接复制粘贴可能不是最佳方式。我建议使用管道将日志传入，或者让我读取文件。您更倾向于哪种方式？

用户：让你直接读取文件吧。

AI：好的，我将读取production-2023-05-15.log文件。

[读取完整日志文件]

我已分析完整个日志文件。发现以下错误模式：

1. 数据库连接超时：占错误的42%，主要发生在高峰时段(14:00-16:00)
2. 认证失败：占错误的28%，集中在特定IP范围
3. API限流：占错误的15%，主要影响移动客户端
4. 内存溢出：占错误的10%，在处理大型报表时发生
5. 其他杂项错误：占5%

建议优先解决数据库连接问题，可能需要增加连接池大小或优化查询。您需要我深入分析某个特定类型的错误吗？

用户：请深入分析数据库连接超时问题。

AI：[提供详细分析，基于完整日志数据]
```

**为何更好**：通过直接读取文件而非复制粘贴，AI能够访问完整数据集，提供全面准确的分析，避免了数据不完整导致的片面结论。

---

这些优秀交互示例展示了工作流程优化的实际价值，不仅提高了效率，还改善了结果质量。通过对比不佳示例和优秀示例，用户可以直观理解这些优化建议的必要性，并将其应用到自己与AI的日常交互中。


---

### 对话 13

> **👤 用户** (2025年04月22日 05:57)

回顾上述 生成的 "Claude Code 自定义斜杠命令提示词模板" 我觉得你生成的质量还不错，请你总结上述模板的共性和差异性，最终再给出系统提示词，用户只需要简单的提问，就会生成上述对应的提示词

> **🤖 Augment** (2025年04月22日 05:57)

# Claude Code 自定义斜杠命令提示词模板分析

## 模板共性分析

分析我之前生成的10个斜杠命令提示词模板，可以发现以下共性：

1. **结构化格式**：
   - 所有模板都以明确的任务描述开头（"Please [动词] [任务]"）
   - 使用 `$ARGUMENTS` 参数接收用户输入
   - 包含"Follow these steps:"引导结构化步骤
   - 以提醒或注意事项结尾

2. **步骤分解方法**：
   - 将复杂任务分解为有序、可执行的步骤（通常5-10个）
   - 步骤按逻辑顺序排列，从分析/准备到实施再到验证/总结
   - 关键步骤包含子步骤，提供更细致的指导

3. **全面性考虑**：
   - 涵盖任务的整个生命周期（从理解问题到最终提交）
   - 包含质量保证环节（测试、验证、检查）
   - 考虑边缘情况和潜在问题

4. **专业性语言**：
   - 使用领域特定术语
   - 保持专业、简洁的表达
   - 避免模糊或主观描述

## 模板差异性分析

各模板的主要差异体现在：

1. **任务特定内容**：
   - 每个模板针对特定任务类型（代码审查、重构、性能优化等）
   - 包含与特定任务相关的专业考虑点
   - 强调不同任务的独特关注点（如安全漏洞修复强调根本原因分析）

2. **分析深度差异**：
   - 有些模板（如性能优化、安全漏洞修复）需要更深入的分析阶段
   - 而其他模板（如文档生成）更注重输出的结构和格式

3. **工具使用差异**：
   - 不同任务推荐使用不同的工具和命令
   - 有些任务需要外部资源（如研究安全漏洞）
   - 有些任务更依赖内部代码分析

4. **输出期望差异**：
   - 每个模板对最终输出有不同期望（代码、文档、分析报告等）
   - 成功标准因任务而异

## 系统提示词

基于以上分析，以下是一个系统提示词，用于生成高质量的Claude Code自定义斜杠命令模板：

```markdown
# 斜杠命令生成助手

你是一个专门为Claude Code创建高质量斜杠命令模板的AI助手。当用户简单描述他们需要的命令类型时，你将生成结构完善、步骤清晰的斜杠命令模板。

## 输出格式

为每个请求生成一个Markdown代码块，包含以下结构：

1. 以"Please [动词] [任务]: $ARGUMENTS."开头
2. 空行后跟"Follow these steps:"
3. 5-10个有序步骤，按逻辑顺序排列
4. 适当的子步骤，使用缩进列表
5. 结尾提醒或最佳实践建议，以"Remember"开头

## 模板设计原则

创建模板时遵循以下原则：

1. **结构化与全面性**：
   - 涵盖任务的完整生命周期
   - 包含分析、实施、验证阶段
   - 添加质量保证步骤

2. **专业性与明确性**：
   - 使用领域特定术语
   - 避免模糊指令
   - 提供具体、可操作的步骤

3. **适应性**：
   - 使用$ARGUMENTS参数接收用户输入
   - 设计通用但可定制的步骤

4. **最佳实践**：
   - 包含该任务类型的行业最佳实践
   - 强调常见陷阱和注意事项

## 命令类型适配

根据不同命令类型调整内容重点：

- **开发类**：强调代码质量、测试和文档
- **分析类**：强调深入研究和数据收集
- **修复类**：强调根本原因分析和验证
- **优化类**：强调基准测试和可测量改进
- **文档类**：强调清晰性、完整性和可访问性

## 示例响应

当用户请求"代码审查命令"时，生成类似以下格式的响应：

```
请为我创建一个代码审查斜杠命令模板。
```

你应该生成：

```markdown
Please perform a thorough code review of PR #$ARGUMENTS.

Follow these steps:

1. Use `gh pr view $ARGUMENTS` to get the PR details
2. Use `gh pr checkout $ARGUMENTS` to check out the PR code
3. Analyze the code changes for:
   - Code quality and best practices
   - Potential bugs or edge cases
   - Test coverage
   - Performance implications
   - Security concerns
4. Run linting and static analysis tools
5. Run the test suite to verify functionality
6. Provide a detailed review summary with:
   - Overall assessment
   - Specific issues found (if any)
   - Improvement suggestions
7. Add your review comments to the PR using `gh pr comment`

Remember to be constructive and specific in your feedback, focusing on both strengths and areas for improvement.
```

记住，好的斜杠命令模板应该是可重用的、结构化的，并且能够指导Claude完成复杂任务。
```
</markdown>
</body>
</html>


---

### 对话 14

> **👤 用户** (2025年04月22日 05:59)

在根目录创建 ClaudeCode 文件夹，将上述内容输出到一个合适的文件中 

> **🤖 Augment** (2025年04月22日 05:59)

我将创建一个 ClaudeCode 文件夹并将斜杠命令生成助手的系统提示词保存到一个合适的文件中。


现在我将创建一个文件来保存斜杠命令生成助手的系统提示词：


现在我将把之前生成的10个斜杠命令模板也保存到一个单独的文件中，作为参考示例：


我还将创建一个简单的使用指南文件，帮助用户理解如何使用这些模板：


我已经在根目录创建了 `ClaudeCode` 文件夹，并在其中创建了以下文件：

1. **slash_command_generator.md** - 包含斜杠命令生成助手的系统提示词，可以用来生成新的斜杠命令模板
2. **slash_command_examples.md** - 包含10个不同场景的斜杠命令模板示例
3. **README.md** - 使用指南，解释如何使用这些资源

这些文件组成了一个完整的工具包，可以帮助用户为 Claude Code 创建高质量的自定义斜杠命令。用户可以：

1. 参考示例模板直接使用
2. 使用生成助手创建新的自定义模板
3. 按照README中的说明将模板集成到Claude Code工作流中

所有文件都使用Markdown格式，便于阅读和编辑。用户可以根据自己的需求修改这些模板，或者使用斜杠命令生成助手创建全新的模板。


---

