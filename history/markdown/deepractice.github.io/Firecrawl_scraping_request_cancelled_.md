# Firecrawl scraping request cancelled


**项目**: deepractice.github.io

**创建时间**: 2025年05月26日 07:33

**最后交互**: 2025年05月26日 08:51

**对话ID**: 8288449d-27e2-41f5-acce-5d3e90948cc6

---

### 对话 1

> **👤 用户** (2025年05月26日 08:51)

使用 firecraw 抓取这个页面的内容，为我整理出所有的 提示词，以及什么时候使用

https://docs.lovable.dev/tips-tricks/prompting-library#ui%2Fux-design

> **🤖 Augment** (2025年05月26日 08:51)

我将使用 Firecrawl 抓取该页面的内容，然后为您整理出所有的提示词以及使用场景。


根据抓取的内容，我为您整理了 Lovable 提示词库中的所有提示词及其使用场景：

## 📋 Lovable 提示词库完整整理

### 1. 🚀 项目启动 (Starting Projects)

**使用场景：** 项目初期，帮助 AI 理解高级需求并构建基础架构

**核心提示词：**
```
我需要一个**任务管理**应用程序，包含：
- **技术栈：** Next.js 前端，Tailwind CSS 样式，Supabase 认证和数据库
- **核心功能：** 项目和任务创建，任务分配给用户，截止日期提醒，仪表板概览

从构建**主仪表板页面**开始，包含：
- 带导航的头部
- 显示状态的项目列表
- 创建新项目的按钮

暂时提供虚拟数据，确保设计简洁且响应式。
```

**其他相关提示词：**
- 创建新组件：`创建一个名为 [ComponentName] 的新组件，具有这些功能：[列出功能]。使其响应式且可访问，具有适当的键盘导航。`
- 代码解释：`用简单的术语解释这个函数如何工作，突出其输入、输出和任何副作用：[粘贴函数]`

### 2. 🎨 UI/UX 设计 (UI/UX Design)

**使用场景：** 优化应用外观和感觉，不改变功能

**核心提示词：**
```
应用程序 UI 需要改进，**不改变任何功能**。

- 保持所有现有逻辑和状态管理不变
- **视觉增强：** 更新仪表板页面样式：为每个项目列表使用现代卡片设计，改善色彩方案以获得更好的对比度，增加内边距以获得更清洁的布局
- 确保这些更改**不会破坏任何功能或数据流**

*目标：* 纯粹的外观改进，使应用看起来更精致，但行为完全相同。
```

**其他设计相关提示词：**
- 增强组件视觉效果：`增强此组件的视觉吸引力：[粘贴组件]。添加动画，改善间距，创建精致外观，同时保持可访问性标准和响应式行为。`
- 设计系统创建：`为我的应用程序创建一个综合设计系统，包含调色板、排版比例、间距系统和组件变体。`
- 响应式仪表板：`设计一个包含 [描述关键指标/小部件] 的响应式仪表板布局。`
- 移动优先设计：`将这个仅限桌面的组件转换为移动优先设计：[粘贴组件]。`
- 添加动画：`为此组件添加微妙、高性能的动画以增强用户体验：[粘贴组件]。`
- 用户流程优化：`分析并优化 [描述任务/目标] 的用户流程。`
- 可访问性审查：`审查这些组件的可访问性问题并建议改进：[粘贴组件]。`

### 3. 📱 响应式设计 (Responsiveness)

**使用场景：** 确保应用在不同屏幕尺寸上正常工作

**核心提示词：**
```
我们的应用需要在移动设备、平板电脑和桌面上**完全响应式**。

- 遵循**移动优先**策略：优先考虑小屏幕布局，然后调整大屏幕
- 使用现代 UI/UX 最佳实践进行响应式设计（对于 Tailwind CSS，使用标准断点 `sm, md, lg, xl`）
- 确保每个页面（特别是仪表板和项目详情页面）在小屏幕上正确重排：元素应该堆叠或根据需要调整大小，文本应保持可读，内容不应溢出屏幕
- **不要改变核心设计或功能**，只需确保它灵活适应不同屏幕尺寸

更改后，请在 iPhone 12 尺寸和典型桌面宽度下仔细检查布局。
```

### 4. 🔧 重构 (Refactoring)

**使用场景：** 清理代码而不改变功能，改善结构、可读性或性能

**核心提示词：**
```
重构 **ProjectList 组件文件**，但**保持其行为和 UI 完全相同**。

目标：
- 改善代码结构和可读性（简化复杂函数，如需要则分解为更小的函数）
- 删除任何未使用的变量或导入
- 确保文件遵循最佳实践并有良好文档

**不要**引入任何新功能或改变组件对用户的工作方式 - 这纯粹是为了可维护性的代码清理。
```

**其他重构相关提示词：**
- 代码审查：`审查此代码并建议可读性、性能和可维护性的改进：[粘贴代码]`
- 文件夹结构：`为具有这些功能的 [类型] 应用程序建议文件夹结构：[列出功能]`
- 错误调试：`我遇到了这个错误：[粘贴错误]。这是相关代码：[粘贴代码]`

### 5. 🏗️ 应用类型 (App Types)

**各种应用类型的启动模板：**

- **电商商店：** `创建一个包含产品列表、搜索、筛选、购物车功能和结账流程的初始电商商店。`
- **内容管理系统：** `构建一个用于管理博客文章或文章的基本 CMS，带有管理仪表板。`
- **项目管理应用：** `创建一个包含任务板、列表和卡片的项目管理应用。`
- **社交媒体动态：** `构建一个包含帖子、评论、点赞和分享功能的社交媒体动态组件。`
- **分析仪表板：** `创建一个包含多种图表类型的分析仪表板。`
- **SaaS 应用：** `构建一个包含用户认证、订阅管理的 SaaS 应用启动器。`

**AI 增强功能：**
- **AI 聊天界面：** `创建一个帮助用户完成 [描述任务/目的] 的 AI 助手聊天界面。`
- **AI 内容生成：** `构建一个使用 AI 根据用户输入生成 [描述内容类型] 的工具。`
- **AI 推荐系统：** `实现一个基于用户行为和偏好推荐 [描述项目] 的推荐组件。`

### 6. ⚛️ React 开发 (React Development)

**React 特定的提示词：**

- **自定义 Hook：** `创建一个名为 use[Name] 的自定义 React hook，处理 [功能]。`
- **Context 重构：** `重构此组件以使用 React Context 而不是属性传递：[粘贴组件]`
- **性能优化：** `优化此 React 组件以防止不必要的重新渲染：[粘贴组件]`
- **表单验证：** `为 [描述表单字段和验证规则] 创建一个带验证的表单。`
- **数据获取：** `使用 React Query 实现 [描述数据] 的数据获取模式。`
- **动画实现：** `为 [描述元素] 创建平滑的过渡动画。`

### 7. 🔒 限制范围 (Locking Files / Limiting Scope)

**使用场景：** 让 AI 专注于特定部分，不触及其他文件

**核心提示词：**
```
请**仅专注于仪表板页面**进行此更改。

- **不要修改** `LoginPage.tsx` 或 `AuthProvider.tsx` 文件（认证工作正常，我们想保持不变）
- 将代码编辑集中在 `Dashboard.tsx` 和相关仪表板组件**仅此而已**

任务：向仪表板添加一个显示"本周到期任务"的新部分。确保从数据库获取相关任务。

*（再次强调，不要更改登录或认证文件 - 这些是禁区。）*
```

### 8. 📋 规划 (Planning)

**使用场景：** 复杂或多步骤实现前的规划

**核心提示词：**
```
在编写任何代码之前，**规划新通知功能的实现**。

- 列出添加任务逾期时发送电子邮件通知所需的每个步骤
- 考虑前端（UI 更改，如果有）和后端（创建计划检查或触发器）方面
- 确保计划保持当前功能稳定 - 我们不能破坏任何现有功能
- 提供有序列表（1、2、3...），简要解释每个步骤

一旦你概述了计划，暂停审查。**暂时不要进行任何代码更改。**
```

### 9. 💳 Stripe 设置 (Stripe Setup)

**使用场景：** 集成 Stripe 支付功能

**核心提示词：**
```
我想**向应用添加 Stripe 支付**。

- 现在使用 **Stripe 测试模式**
- 我们在 Stripe 中有一个产品 ID `prod_12345` 和价格 ID `price_67890`（一次性购买）
- 在**定价页面**实现一个结账按钮，启动该产品的 Stripe 结账
- 支付成功后，重定向用户到 `/payment-success`。如果支付被取消，重定向到 `/payment-cancelled`

重要：
- 假设 API 密钥和 webhook 密钥已安全配置（**不要**硬编码它们）
- **不要**修改与支付无关的其他页面或功能

完成后，提供我需要的任何 webhook 端点设置说明。
```

### 10. 🗄️ Supabase 和后端 (Supabase & Backend)

**后端相关提示词：**

- **数据库架构：** `为具有这些实体关系的 [描述应用程序] 设计数据库架构：[描述关系]`
- **API 服务：** `创建一个从 [API 名称] 获取数据的服务，实现缓存、错误重试逻辑和请求限流。`
- **行级安全：** `为具有这些表的多租户应用程序创建行级安全策略：[列出表]`
- **Edge 函数：** `创建一个 Supabase Edge 函数来处理 [描述功能]`
- **实时同步：** `使用 Supabase 订阅为 [描述功能] 实现实时数据同步。`
- **搜索功能：** `为 [描述内容类型] 实现强大的搜索功能，包含筛选、排序和匹配项高亮。`
- **数据表格：** `为 [描述数据] 创建一个数据表格/网格，包含排序、筛选、分页功能。`
- **数据导入导出：** `构建一个以各种格式导入和导出 [描述数据] 的系统。`
- **交互式图表：** `使用 Recharts 为 [描述数据/指标] 创建一组交互式图表。`
- **离线同步：** `实现一个在恢复连接时将离线数据更改与后端同步的策略。`
- **多步表单：** `创建一个用于收集 [描述数据] 的多步表单向导。`

### 11. 🔄 工作流程 (Workflow)

**工作流程相关提示词：**

- **GitHub 集成：** `将此 Lovable 项目连接到 GitHub 并设置良好的贡献工作流程。`
- **组件重构：** `将这个大组件重构为更小、更易管理的组件：[粘贴组件]`
- **测试策略：** `为 [组件/功能] 建议测试策略，包括测试什么和如何测试。`
- **错误处理：** `为此异步函数实现全面的错误处理：[粘贴函数]`
- **部署管道：** `为此应用程序设置部署管道，包括暂存和生产环境。`
- **用户流程分析：** `分析并优化此用户流程：[描述流程]`

### 12. 💬 聊天模式 vs 默认模式 (Chat Mode vs Default Mode)

**使用指南：**

- **默认模式：** 用于明确定义的功能构建或更改
- **聊天模式：** 用于头脑风暴、调试或在执行前讨论更改

**示例场景：**
- 默认模式：`审查应用并告诉我哪里有过时的代码。`
- 聊天模式：`我看到一些弃用的库警告。代码的哪些部分可能过时了，我们应该如何更新它们？`

### 13. 📚 知识库和 PRD (Knowledge Bases and PRDs)

**使用场景：** 项目开始时和需要给 AI 提供更多上下文时

**核心提示词：**
```
在编写任何代码之前，请**审查知识库**并分享你对我项目的理解。
```

**知识库应包含：**
- 概述和目标
- 用户流程
- 核心功能和范围
- 技术栈
- 设计指南
- 后端详情

---

## 🎯 使用建议

1. **项目初期：** 使用"项目启动"和"知识库"提示词
2. **开发过程中：** 根据需要使用"UI/UX 设计"、"React 开发"、"后端"等提示词
3. **维护阶段：** 使用"重构"、"错误处理"、"测试"等提示词
4. **复杂功能：** 先使用"规划"提示词，再执行具体实现
5. **安全操作：** 使用"限制范围"提示词避免意外更改

这些提示词模板可以根据您的具体项目需求进行调整和组合使用。


---

