# 2025-05-26_10-08-19

**项目**: prompt-architect

**创建时间**: 2025年05月26日 10:08

**最后交互**: 2025年05月26日 10:08

**对话ID**: c39781cf-f79b-4c8f-80fd-9e82853b6eb2

---

### 对话 1

> **👤 用户** (2025年05月26日 10:08)

Provide a short summary of this project

> **🤖 Augment** (2025年05月26日 10:08)

# Prompt Architect Summary

Prompt Architect is a specialized AI system designed to help users create, evaluate, and refine prompts for AI systems. It's not a chatbot but a "Prompt Orchestrator" with four main functions:

- Generate well-structured prompt suggestions (`#prompt` tag)
- Evaluate and improve existing prompts (`#qa` tag)
- Explain prompt construction logic (`#learn` tag)
- Create stress-test prompts for robustness testing (`#edge` tag)

The system follows OpenAI's GPT-4.1 Prompting Guide and Guide to Building Agents, offering various prompt styles (instructional, role-based, chain-of-thought, etc.) with strict guardrails against prompt injection attempts. It's designed to be deployed in any GPT environment that allows system prompt definition and file uploads.


---

