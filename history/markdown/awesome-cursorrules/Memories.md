# awesome-cursorrules 的记忆

# User Preferences
- User prefers content to be translated into Chinese.
- User prefers communication in Chinese language.

# Project: VSCode Plugin
- User is interested in creating a VSCode plugin that analyzes project files (like package.json, requirements.txt) to match appropriate coding rules from a repository.
- User wants to build an index file that serves as context for large language models to better match rules in the project.
- User prefers to directly apply rules to corresponding files rather than just downloading them.
- For rule retrieval implementation, reference the simpler approach used in vscode-cursor-rules plugin.
- When applying rules, .mdc files should be copied to a .cursor/rules subfolder in the project root, while .cursorrules files should be created directly in the project root.
- User wants to implement rule removal functionality.
- User wants to modify ViewDetails to display readme.md content to help users understand how to use the rules.
- User wants to see large language model call logs during debugging.
- User wants to implement real AI model calls instead of using simulated responses in the code.
- User wants to improve rule extraction by parsing rule IDs with '-' as delimiter to determine primary tech stack, and extracting 'globs' and 'description' fields from .mdc files for file type matching and rule injection conditions.
- User prefers to modify rule filtering logic to first filter rules based on projectAnalysis and RuleDetails before using AIMatcher or SimilarityEngine for matching.
- User wants to parse each rule to form RuleDetails and cache it when pulling the rule list from GitHub, instead of parsing it when needed.
- User prefers to implement a dual caching strategy with rules-details.json for complete rule details and keeping rules-list.json for quick access to rule listings.
- When implementing rule matching, the `extractTechStackFromId` method should be referenced, similar to the `extractFrameworkFromName` method in `similarity-engine`, to ensure accurate matching of the primary tech stack.
- User wants to improve rule filtering accuracy by applying the same technical approach used in similarity-engine.ts to the filterRules method in ai-matcher.ts.
- User wants to implement a reset functionality for project analysis and rule recommendation.
- User wants to update readme and changelog documentation for the rule-matcher-extension project.
- User wants to remove the run buttons from rule recommendation and custom rule generation features.

# Project: Custom Rule Generation
- User wants to integrate generate_mdc_files.py functionality into rule-matcher-extension plugin to create custom MDC rules based on project and LLM analysis.
- User wants to add functionality to generate MDC files based on project analysis (tech stack, dependencies, etc.) within the plugin, and this functionality should be isolated from the existing rule recommendation logic.
- User wants to add a custom generation button to the left of the reset button, and the generated rules should be displayed directly below the recommended rules, providing view and apply functionality.
- User wants to implement custom rule generation as a parallel feature alongside existing functionality rather than modifying current features.
- User wants the generated rules to match the project's actual tech stack (Python), avoiding JS/TS rules, and wants to focus on the Prompt construction process and the metadata used.
- User wants to implement a reset button for custom rules similar to existing functionality, and suggests referencing the AI implementation in ai-matcher.ts for fixing custom rule generation issues.
- User found a bug where the reset button for custom rules clears the rules in the logs but not in the UI interface.
- User wants to change the icon for custom rule generation to a triangular run button, with the same interaction logic as project analysis and recommended rules, displaying a loading animation during execution and results upon completion.
- User wants to modify the custom rule generation process to ensure it can only be executed after project analysis is complete.