# 分析大模型调用功能


**项目**: awesome-cursor-rules-mdc

**创建时间**: 2025年04月10日 11:54

**最后交互**: 2025年04月11日 09:50

**对话ID**: dba9d179-e75c-460b-bbf8-99cfcdd3bf37

---

### 对话 1

> **👤 用户**

@src/generate_mdc_files.py 分析这个文件实现的主要功能，主要聚焦在调用大模型上

> **🤖 Augment**

我将为您分析 `src/generate_mdc_files.py` 文件，特别关注其中与大模型调用相关的功能。让我先检索这个文件的内容。


根据对 `src/generate_mdc_files.py` 文件的分析，我将重点关注其中与大模型调用相关的功能：

## `src/generate_mdc_files.py` 文件分析 - 大模型调用功能

### 1. 大模型调用的核心功能

这个文件的主要功能是生成 MDC (Markdown Cursor) 规则文件，它通过调用大模型 API 来生成针对不同编程库的最佳实践规则。主要的大模型调用相关功能包括：

1. **大模型选择与配置**：
   - 使用 `litellm` 库进行大模型调用，支持多种模型（第8行导入）
   - 默认使用 Gemini 2.0 Flash 模型（第61行配置）
   - 支持 OpenAI、Anthropic Claude 等多种模型（第199-206行验证环境变量）
   - 启用了 JSON schema 验证（第22-23行）

2. **大模型调用的核心函数**：
   - `generate_mdc_rules_from_exa` 函数（第292-472行）是调用大模型的主要函数
   - 使用 `@retry`、`@sleep_and_retry` 和 `@limits` 装饰器实现重试和速率限制（第288-291行）

3. **Prompt 构建**：
   - 构建了详细的 prompt 模板（第326-392行）
   - 根据是否有足够的 Exa 搜索结果，使用不同的 prompt 策略（第395-429行）
   - prompt 包含详细的指导，要求大模型生成全面的库最佳实践

4. **大模型 API 调用**：
   - 使用 `completion` 函数调用大模型 API（第432-460行）
   - 指定了 JSON 格式的响应结构（第435-459行）
   - 解析响应并创建 `MDCRule` 对象（第463-468行）

### 2. 大模型调用的辅助功能

1. **Exa 搜索增强**：
   - 使用 Exa 搜索引擎获取库的最佳实践信息（第232-286行的 `get_library_best_practices_exa` 函数）
   - 将 Exa 搜索结果作为上下文提供给大模型，增强生成质量

2. **错误处理与重试机制**：
   - 使用 `tenacity` 库实现重试逻辑（第9行导入）
   - 配置了最大重试次数和等待时间（第62-66行）

3. **速率限制**：
   - 使用 `ratelimit` 库实现 API 调用速率限制（第10行导入）
   - 配置了每分钟最大调用次数（第62-63行）

4. **并行处理**：
   - 使用 `ThreadPoolExecutor` 并行处理多个库（第658-679行）
   - 可配置最大工作线程数（第76行）

### 3. 大模型调用的流程

1. **准备阶段**：
   - 加载配置和环境变量
   - 验证必要的 API 密钥
   - 初始化 Exa 客户端

2. **处理流程**：
   - 对每个库，先使用 Exa 搜索相关最佳实践
   - 将 Exa 搜索结果作为上下文提供给大模型
   - 调用大模型生成详细的最佳实践规则
   - 将生成的规则保存为 MDC 文件

3. **输出处理**：
   - 解析大模型返回的 JSON 响应
   - 格式化为 MDC 文件格式
   - 保存到指定目录

### 4. 大模型调用的特点

1. **结构化输出**：
   - 使用 JSON schema 验证确保输出格式正确
   - 定义了明确的输出结构（名称、glob 模式、描述、内容）

2. **上下文增强**：
   - 结合 Exa 搜索结果增强大模型的上下文
   - 提供详细的指导以生成全面的最佳实践

3. **健壮性设计**：
   - 实现了重试机制和速率限制
   - 跟踪处理进度，支持恢复失败的任务

4. **可配置性**：
   - 支持通过配置文件和命令行参数调整各种参数
   - 可以选择特定的库或标签进行处理

总结：这个文件实现了一个强大的系统，通过结合 Exa 搜索和大模型生成能力，为各种编程库创建详细的最佳实践规则文件。它展示了如何有效地使用大模型 API，包括构建详细的 prompt、处理响应、实现重试和速率限制，以及并行处理多个任务。


---

### 对话 2

> **👤 用户**

exa 预期搜索词是什么？ 搜索的结果是什么？流程是什么？

> **🤖 Augment**

我将详细分析 `src/generate_mdc_files.py` 中 Exa 搜索的预期搜索词、搜索结果和整体流程。

## Exa 搜索分析

### 1. Exa 预期搜索词

Exa 搜索使用的搜索词是根据库名称和标签动态构建的，主要在 `get_library_best_practices_exa` 函数中（第236-286行）：

```python
# 构建搜索查询
if tags and len(tags) > 0:
    # 选择最多3个最相关的标签以保持查询的重点
    relevant_tags = tags[:3] if len(tags) > 3 else tags
    tags_str = " ".join(relevant_tags)
    query = f"{library_name} best practices coding standards for {tags_str} development"
else:
    query = f"{library_name} best practices coding standards"
```

预期搜索词的特点：
1. **基本格式**：`{库名} best practices coding standards`
2. **带标签的扩展格式**：`{库名} best practices coding standards for {标签1 标签2 标签3} development`
3. **标签限制**：最多使用3个标签，以保持查询的聚焦性

例如：
- 对于库 "React" 且没有标签：`"React best practices coding standards"`
- 对于库 "React" 且有标签 ["frontend", "javascript"]：`"React best practices coding standards for frontend javascript development"`

### 2. Exa 搜索的结果

Exa 搜索返回的结果在代码中被处理为以下结构（第255-261行）：

```python
# 将 AnswerResponse 对象转换为字典
result_dict = {
    "answer": result.answer if hasattr(result, 'answer') else "",
    "citations": result.citations if hasattr(result, 'citations') else []
}
```

搜索结果包含两个主要部分：
1. **answer**：Exa 生成的综合回答，总结了搜索到的最佳实践
2. **citations**：引用的来源，包含原始文本和元数据

这些结果会被保存到 JSON 文件中（第264-272行），文件名格式为：`{库名}_exa_result.json`

### 3. Exa 搜索流程

整个 Exa 搜索和处理流程如下：

1. **初始化 Exa 客户端**（第219-230行 `initialize_exa_client` 函数）：
   - 从环境变量获取 API 密钥
   - 创建 Exa 客户端实例

2. **构建搜索查询**（第242-250行）：
   - 根据库名称和标签构建查询字符串
   - 记录日志，表明正在搜索

3. **执行搜索**（第255行）：
   - 调用 `exa_client.answer(query, text=True)` 执行搜索
   - 这个方法不仅搜索，还会生成一个综合回答

4. **处理搜索结果**（第258-261行）：
   - 提取回答和引用
   - 转换为标准字典格式

5. **保存搜索结果**（第264-274行）：
   - 创建安全的文件名
   - 将结果保存为 JSON 文件
   - 记录日志，表明结果已保存

6. **结果验证**（第276-282行）：
   - 检查是否成功获取到回答
   - 如果没有结果，返回空对象

7. **错误处理**（第284-286行）：
   - 捕获并记录任何搜索过程中的错误
   - 在出错时返回空对象

### 4. Exa 结果如何用于大模型生成

Exa 搜索结果直接用于增强大模型的 prompt（第307-320行）：

```python
# 提取 Exa 回答和引用
exa_answer = exa_results.get("answer", "")
citations = exa_results.get("citations", [])

# 从引用中提取文本
citation_texts = []
for citation in citations:
    if isinstance(citation, dict) and citation.get("text"):
        citation_texts.append(citation.get("text", ""))
    elif hasattr(citation, 'text') and citation.text:
        citation_texts.append(citation.text)

# 合并所有引用文本 - Gemini 可以处理更大的输入
all_citation_text = "\n\n".join(citation_texts)
```

然后，根据 Exa 结果的质量，选择不同的 prompt 策略（第395-429行）：

1. **如果 Exa 结果不足**（内容少于100个字符且引用文本少于200个字符）：
   - 使用"从头生成"的 prompt，要求模型从自身知识生成内容

2. **如果 Exa 结果充分**：
   - 使用"基于 Exa 结果增强"的 prompt
   - 将 Exa 的回答和引用文本作为上下文提供给模型
   - 要求模型综合、增强和扩展这些信息

### 5. 整体流程图

```
初始化 → 加载库信息 → 对每个库:
    ↓
    初始化 Exa 客户端
    ↓
    构建搜索查询（库名+标签）
    ↓
    执行 Exa 搜索
    ↓
    处理和保存搜索结果
    ↓
    提取 Exa 回答和引用文本
    ↓
    根据 Exa 结果质量选择 prompt 策略
    ↓
    调用大模型生成详细的最佳实践
    ↓
    解析大模型响应
    ↓
    创建和保存 MDC 文件
```

总结：Exa 搜索在这个系统中扮演着"知识获取"的角色，它通过搜索互联网上关于特定库的最佳实践信息，为大模型提供高质量的上下文，从而使大模型能够生成更加准确、全面和有用的最佳实践规则。这种"搜索增强生成"的方法显著提高了最终生成内容的质量和可靠性。


---

### 对话 3

> **👤 用户**

分析大模型调用提示词动态生成是如何生成的 ，包含了哪些维度？

> **🤖 Augment**

# 大模型调用提示词动态生成分析

我将详细分析 `src/generate_mdc_files.py` 中大模型提示词（prompt）的动态生成机制，包括其中包含的各个维度。

## 提示词动态生成机制

在 `generate_mdc_rules_from_exa` 函数中（第292-472行），提示词的生成是一个复杂的动态过程，涉及多个维度的信息整合。

### 1. 提示词模板结构

首先，代码定义了一个基础提示词模板（第326-392行）：

```python
enhanced_prompt_template = """Create a comprehensive Cursor rule file (.mdc) for the {library_name} library following these guidelines:

{mdc_instructions}

Library Information:
- Name: {library_name}
- Tags: {tags}

{exa_content_section}

Your task is to create an EXTREMELY DETAILED and COMPREHENSIVE guide that covers:

1. Code Organization and Structure:
   - Directory structure best practices for {library_name}
   - File naming conventions specific to {library_name}
   - Module organization best practices for projects using {library_name}
   - Component architecture recommendations for {library_name}
   - Code splitting strategies appropriate for {library_name}

2. Common Patterns and Anti-patterns:
   ...

[其他详细类别]
...

Format your response as a valid JSON object with exactly these keys:
  - name: a short descriptive name for the rule (e.g., "{library_name} Best Practices")
  - glob_pattern: the most appropriate glob pattern for this library based on the file types it typically works with
  - description: a clear 1-2 sentence description of what the rule covers
  - content: the formatted rule content with comprehensive best practices in markdown format
"""
```

这个模板包含了几个动态填充的占位符：`{library_name}`、`{tags}`、`{mdc_instructions}` 和 `{exa_content_section}`。

### 2. 提示词动态生成的维度

分析代码，提示词动态生成包含以下关键维度：

#### 2.1 库信息维度

```python
# 格式化标签
tags_str = ", ".join(library_info.tags)

# 在模板中填充库名和标签
prompt = enhanced_prompt_template.format(
    library_name=library_info.name,
    tags=tags_str,
    ...
)
```

- **库名称**：直接从 `library_info.name` 获取，在模板中多处使用
- **库标签**：从 `library_info.tags` 获取并格式化为逗号分隔的字符串

#### 2.2 指令维度

```python
# 加载MDC指令
mdc_instructions_path = SCRIPT_DIR / CONFIG["paths"]["mdc_instructions"]
if not mdc_instructions_path.exists():
    logger.warning(f"MDC instructions file not found at {mdc_instructions_path}")
    mdc_instructions = "Create rules with clear descriptions and appropriate glob patterns."
else:
    try:
        mdc_instructions = mdc_instructions_path.read_text()
    except Exception as e:
        logger.warning(f"Could not read MDC instructions file: {str(e)}")
        mdc_instructions = "Create rules with clear descriptions and appropriate glob patterns."
```

- **MDC指令**：从外部文件 `mdc_instructions.txt` 加载
- 如果文件不存在或读取失败，使用默认指令
- 这些指令提供了生成规则的具体要求和格式指导

#### 2.3 Exa搜索结果维度

```python
# 提取Exa回答和引用
exa_answer = exa_results.get("answer", "")
citations = exa_results.get("citations", [])

# 从引用中提取文本
citation_texts = []
for citation in citations:
    if isinstance(citation, dict) and citation.get("text"):
        citation_texts.append(citation.get("text", ""))
    elif hasattr(citation, 'text') and citation.text:
        citation_texts.append(citation.text)

# 合并所有引用文本
all_citation_text = "\n\n".join(citation_texts)
```

- **Exa回答**：从Exa搜索结果中提取的综合回答
- **引用文本**：从Exa搜索结果的引用中提取的原始文本
- 这些内容作为上下文提供给大模型，增强生成质量

#### 2.4 内容策略维度

根据Exa搜索结果的质量，代码动态选择不同的内容策略（第395-429行）：

```python
# 判断是否需要从头生成内容
if len(exa_answer.strip()) < 100 and len(all_citation_text.strip()) < 200:
    # Exa内容不足，从头生成
    exa_content_section = f"""I need you to research and generate comprehensive best practices for {library_info.name} from your knowledge.

Please be extremely thorough and detailed, covering all aspects of {library_info.name} development.
Your guidance should be useful for both beginners and experienced developers.
"""
else:
    # 使用现有Exa内容
    chunk_size = CONFIG["processing"]["chunk_size"]
    exa_content_section = f"""Based on the following information about {library_info.name} best practices:

Exa search results:
{exa_answer}

Additional information from citations:
{all_citation_text[:chunk_size]}

Please synthesize, enhance, and expand upon this information to create the most comprehensive guide possible.
Add any important best practices that might be missing from the search results.
"""
```

- **内容充分性判断**：基于Exa回答和引用文本的长度
- **从头生成策略**：当Exa内容不足时，要求模型从自身知识生成
- **内容增强策略**：当Exa内容充分时，提供这些内容作为上下文，要求模型综合和扩展

#### 2.5 输出格式维度

提示词末尾明确指定了输出格式要求（第387-392行）：

```
Format your response as a valid JSON object with exactly these keys:
  - name: a short descriptive name for the rule (e.g., "{library_name} Best Practices")
  - glob_pattern: the most appropriate glob pattern for this library based on the file types it typically works with
  - description: a clear 1-2 sentence description of what the rule covers
  - content: the formatted rule content with comprehensive best practices in markdown format
```

这与API调用中的JSON schema验证相对应（第435-459行）：

```python
response_format={
    "type": "json_object",
    "schema": {
        "type": "object",
        "properties": {
            "name": {
                "type": "string",
                "description": "Short descriptive name for the rule"
            },
            "glob_pattern": {
                "type": "string",
                "description": "Valid glob pattern for target files"
            },
            "description": {
                "type": "string",
                "description": "1-2 sentence description of what the rule does"
            },
            "content": {
                "type": "string",
                "description": "Formatted rule content using markdown"
            }
        },
        "required": ["name", "glob_pattern", "description", "content"]
    }
}
```

#### 2.6 内容结构维度

提示词中详细列出了需要覆盖的内容类别（第336-386行），包括7个主要维度：

1. **代码组织和结构**：
   - 目录结构最佳实践
   - 文件命名约定
   - 模块组织最佳实践
   - 组件架构建议
   - 代码拆分策略

2. **常见模式和反模式**：
   - 特定设计模式
   - 常见任务的推荐方法
   - 需要避免的反模式和代码气味
   - 状态管理最佳实践
   - 错误处理模式

3. **性能考虑**：
   - 特定优化技术
   - 内存管理考虑
   - 渲染优化
   - 包大小优化策略
   - 懒加载策略

4. **安全最佳实践**：
   - 常见漏洞及预防
   - 输入验证最佳实践
   - 认证和授权模式
   - 数据保护策略
   - 安全API通信

5. **测试方法**：
   - 单元测试策略
   - 集成测试方法
   - 端到端测试建议
   - 测试组织最佳实践
   - 模拟和存根技术

6. **常见陷阱和注意事项**：
   - 开发者常犯的错误
   - 需要注意的边缘情况
   - 特定版本的问题
   - 兼容性问题
   - 调试策略

7. **工具和环境**：
   - 推荐的开发工具
   - 构建配置最佳实践
   - 代码检查和格式化建议
   - 部署最佳实践
   - CI/CD集成策略

### 3. 提示词生成的完整流程

1. **加载基础信息**：
   - 获取库名称和标签
   - 加载MDC指令

2. **处理Exa搜索结果**：
   - 提取Exa回答和引用
   - 合并引用文本

3. **判断内容策略**：
   - 评估Exa内容的充分性
   - 选择适当的内容策略

4. **构建exa_content_section**：
   - 根据选定的策略构建内容部分
   - 对于充分的Exa内容，可能会截断过长的引用文本

5. **格式化最终提示词**：
   - 将所有动态内容填充到模板中
   - 生成完整的提示词字符串

6. **发送到大模型**：
   - 使用构建好的提示词调用大模型API
   - 指定JSON格式的响应结构

## 总结：提示词动态生成的维度

综合分析，提示词动态生成包含以下关键维度：

1. **目标库信息维度**：库名称和相关标签
2. **指令维度**：从外部文件加载的MDC指令
3. **知识来源维度**：Exa搜索结果和引用文本
4. **内容策略维度**：基于Exa内容质量的不同生成策略
5. **内容结构维度**：7个主要类别的详细内容要求
6. **输出格式维度**：明确的JSON结构要求
7. **上下文增强维度**：将库特定信息融入到各个内容类别中

这种多维度的动态提示词生成机制使得系统能够为不同的库生成高度定制化、内容丰富且结构清晰的最佳实践规则，同时保持一致的输出格式和质量标准。


---

### 对话 4

> **👤 用户**

@src/plan.md  帮我将这个文件翻译成中文，然后解释这个文件内容

> **🤖 Augment**

我将帮您将 `src/plan.md` 文件翻译成中文，并解释其内容。首先，让我查看这个文件的内容。


# 创建基于库最佳实践的MDC文件计划（中文翻译）

## 项目概述
创建一个脚本，根据各种库和框架的最佳实践生成Cursor规则文件（.mdc）。该脚本将利用：
- Exa API用于搜索和检索库的最佳实践
- 使用Gemini 2.0 Flash的LiteLLM来增强和构建内容
- 适用时使用convert_to_mdc.py中的现有实用函数

## 实施步骤

1. **解析库JSON**
   - 从libraries.json中提取所有库和框架
   - 创建所有技术的扁平列表
   - 按类别组织它们以获得更好的文件结构

2. **设置API集成**
   - 配置Exa API客户端
   - 设置具有适当速率限制的LiteLLM
   - 确保适当的错误处理和重试机制

3. **创建数据收集管道**
   - 对于每个库/框架：
     - 使用Exa搜索最佳实践
     - 解析和清理搜索结果
     - 如果结果不足，使用Gemini 2.0 Flash增强

4. **生成MDC文件**
   - 根据MDC文件结构格式化数据
   - 创建适当的glob模式
   - 组织成逻辑类别

5. **创建文件结构**
   - 使用libraries.json中的类别（前端、后端等）
   - 根据库/框架命名文件（例如，frontend/nextjs.mdc）
   - 将文件保存到适当的目录

6. **实现进度跟踪**
   - 跟踪已处理的库
   - 如果中断，允许恢复处理过程

## 文件结构
```
├── frontend/
│   ├── react.mdc
│   ├── nextjs.mdc
│   ├── vue.mdc
│   └── ...
├── backend/
│   ├── express.mdc
│   ├── django.mdc
│   └── ...
├── database/
│   ├── postgresql.mdc
│   ├── mongodb.mdc
│   └── ...
└── ...
```

## 执行计划
1. 创建协调整个过程的主脚本
2. 实现API交互的实用函数
3. 基于convert_to_mdc.py创建MDC生成函数
4. 使用一小部分库进行测试
5. 运行完整的生成过程
6. 审查和验证生成的MDC文件

## 文件内容解释

这个 `plan.md` 文件是一个项目计划文档，详细描述了如何创建一个自动化系统，用于为各种编程库和框架生成Cursor规则文件（.mdc格式）。以下是对文件内容的详细解释：

### 1. 项目目标
文档首先概述了项目的主要目标：创建一个脚本，能够自动生成基于各种编程库和框架最佳实践的Cursor规则文件。这些规则文件将帮助开发者在使用Cursor编辑器时遵循各种库的最佳实践。

### 2. 技术栈
计划使用的主要技术包括：
- **Exa API**：一个搜索引擎API，用于从互联网上搜索和检索关于各种库的最佳实践信息
- **LiteLLM与Gemini 2.0 Flash**：用于处理和增强从Exa获取的内容，使其更加结构化和全面
- **现有工具**：利用已有的`convert_to_mdc.py`脚本中的功能

### 3. 实施方法
文档详细列出了六个主要实施步骤：

- **解析库JSON**：从配置文件中提取库信息，并按类别组织
- **API集成**：设置必要的API客户端，包括速率限制和错误处理
- **数据收集**：为每个库搜集最佳实践信息，必要时使用大模型增强内容
- **MDC文件生成**：将收集的信息格式化为MDC文件格式
- **文件结构创建**：按类别组织生成的文件
- **进度跟踪**：实现机制以跟踪处理进度，支持中断后恢复

### 4. 预期文件结构
文档提供了一个清晰的文件结构示例，展示了最终生成的MDC文件将如何按类别（前端、后端、数据库等）组织。每个类别下包含相关库的MDC文件，例如前端类别下有react.mdc、nextjs.mdc等。

### 5. 执行计划
最后，文档列出了一个六步执行计划，从创建主脚本开始，到最终审查和验证生成的文件结束。这提供了一个清晰的路线图，指导整个项目的实施过程。

### 总结
这个计划文档是一个全面的项目蓝图，详细描述了如何构建一个自动化系统，利用搜索引擎和大型语言模型为各种编程库生成最佳实践规则文件。它涵盖了从数据收集到文件生成的整个流程，并提供了清晰的实施步骤和预期输出。

从我们之前分析的`src/generate_mdc_files.py`文件来看，这个计划已经被实现了，该Python脚本正是按照这个计划构建的自动化系统，用于生成MDC文件。


---

### 对话 5

> **👤 用户**

分析这个文件的结构，如何借助于 AI 将用户的简单需求能形成这样的执行计划

> **🤖 Augment**

# 分析 `src/plan.md` 文件结构及如何借助 AI 将简单需求转化为执行计划

## 文件结构分析

`src/plan.md` 文件采用了一个结构清晰、层次分明的计划文档格式，主要包含以下几个部分：

1. **项目概述**：简明扼要地描述项目目标和使用的技术栈
2. **实施步骤**：按照逻辑顺序列出的详细步骤，每个步骤都有明确的子任务
3. **文件结构**：使用树形图展示预期的输出文件组织方式
4. **执行计划**：按时间顺序排列的具体执行任务

这种结构非常适合作为软件开发项目的规划文档，它既提供了高层次的概述，又包含了足够的细节来指导实际实施。

## 如何借助 AI 将简单需求转化为执行计划

将用户的简单需求转化为像 `plan.md` 这样详细的执行计划，可以通过以下方法借助 AI 实现：

### 1. 需求提炼与扩展

**用户输入示例**：
```
我想创建一个工具，能自动为不同编程库生成最佳实践指南
```

**AI 辅助流程**：
- **提问澄清**：AI 可以提出一系列问题来获取更多信息
  - "您希望支持哪些编程库？"
  - "输出格式有特定要求吗？"
  - "您计划如何获取最佳实践的信息？"
- **知识补充**：AI 可以基于已有知识补充相关技术信息
  - 推荐适合的搜索 API（如 Exa）
  - 建议使用大语言模型处理和增强内容
  - 提供文件格式建议（如 MDC 格式）

### 2. 结构化规划生成

AI 可以将收集到的信息转化为结构化计划：

1. **项目概述生成**：
   - 综合用户需求和技术可行性
   - 提炼核心目标和技术栈

2. **步骤分解**：
   - 将大任务分解为逻辑上独立的步骤
   - 每个步骤进一步分解为具体可执行的子任务
   - 确保步骤之间有清晰的依赖关系和顺序

3. **资源规划**：
   - 识别所需的 API、库和工具
   - 考虑潜在的技术限制和解决方案

4. **输出结构设计**：
   - 基于行业最佳实践提出文件组织方式
   - 使用可视化方式（如树形图）展示结构

### 3. 实际应用示例

假设用户提出简单需求："我想为不同编程库创建最佳实践指南"，AI 可以通过以下步骤将其转化为详细计划：

1. **需求理解**：
   ```
   用户需要一个自动化工具，能够为各种编程库和框架生成最佳实践指南。
   ```

2. **技术选型建议**：
   ```
   - 数据来源：可以使用 Exa API 搜索互联网上的最佳实践
   - 内容增强：使用 LiteLLM 与 Gemini 2.0 Flash 处理和结构化内容
   - 输出格式：采用 MDC (Markdown Cursor) 格式，便于在编辑器中使用
   ```

3. **流程设计**：
   ```
   1. 数据收集：从配置文件获取库列表 → 搜索最佳实践 → 清理数据
   2. 内容增强：使用 LLM 补充和结构化内容
   3. 文件生成：格式化为 MDC 文件 → 按类别组织 → 保存到指定位置
   ```

4. **执行计划生成**：
   ```
   从原型开发到全面实施的时间顺序步骤，包括测试和验证阶段
   ```

### 4. AI 辅助计划生成的优势

1. **全面性**：AI 可以基于领域知识，确保计划涵盖所有必要方面
2. **结构化**：自动生成层次分明、逻辑清晰的文档结构
3. **技术整合**：推荐合适的技术栈和工具
4. **预见性**：识别潜在挑战和解决方案
5. **一致性**：生成格式统一、风格一致的文档

### 5. 实施建议

要有效地使用 AI 将简单需求转化为执行计划，可以采用以下方法：

1. **提供清晰的初始需求**：即使是简单的需求，也应尽可能明确目标
2. **迭代对话**：通过多轮对话逐步完善需求和计划
3. **领域知识注入**：提供相关的技术背景或参考资料
4. **结构化提示**：要求 AI 生成特定格式的计划文档
5. **计划审查**：对 AI 生成的计划进行审查，确保其可行性和完整性

通过这种方式，即使是非技术用户也能借助 AI 将简单的想法转化为详细、专业的执行计划，就像 `src/plan.md` 这样的文档，为项目实施提供清晰的指导。


---

### 对话 6

> **👤 用户**

能否生成一个系统提示词来实现

> **🤖 Augment**

# 系统提示词：需求转执行计划生成器

以下是一个系统提示词，用于指导 AI 将用户的简单需求转化为详细的执行计划文档：

```
你是一位专业的项目规划顾问，专门帮助用户将简单的需求转化为详细、结构化的执行计划。你的任务是通过提问和分析，扩展用户的初始需求，然后生成一个全面的项目计划文档。

## 工作流程

1. 需求理解与扩展
   - 分析用户提供的初始需求
   - 提出关键澄清问题（目标用户、技术限制、预期输出等）
   - 基于用户回答和你的专业知识，扩展和完善需求

2. 技术栈评估
   - 推荐适合该项目的技术、工具和API
   - 考虑项目规模、复杂性和用户技术背景
   - 解释每项技术选择的理由

3. 结构化计划生成
   - 创建包含以下部分的Markdown格式计划文档：
     * 项目概述：简明扼要地描述项目目标和核心技术
     * 实施步骤：按逻辑顺序列出的详细步骤，每个步骤包含具体子任务
     * 资源需求：所需技术、API和工具列表
     * 文件/输出结构：使用树形图展示预期输出组织
     * 执行计划：按时间顺序排列的具体执行任务
     * 潜在挑战与解决方案：预见可能的困难和应对策略

## 输出要求

1. 格式要求
   - 使用清晰的Markdown格式
   - 使用标题、子标题、列表和代码块增强可读性
   - 保持一致的格式和风格

2. 内容要求
   - 专业性：使用领域相关的专业术语
   - 具体性：提供足够详细的信息指导实施
   - 可行性：确保计划在技术上可行
   - 全面性：涵盖项目的各个方面

3. 结构要求
   - 逻辑清晰：各部分之间有明确的逻辑关系
   - 层次分明：使用适当的标题层级
   - 视觉辅助：适当使用列表、表格或图表

## 互动策略

1. 如果用户需求过于简单或模糊，主动提问获取更多信息
2. 在生成计划前，先确认你对需求的理解是否正确
3. 如果某些技术细节不确定，提供多个可能的选项并解释各自优缺点
4. 在完成计划后，主动询问是否需要对特定部分进行详细说明

记住，你的目标是将用户可能只有一两句话的简单想法，转化为可以直接指导项目实施的专业计划文档。
```

## 使用说明

这个系统提示词设计用于指导 AI 将用户的简单需求转化为类似 `src/plan.md` 的详细执行计划。它具有以下特点：

1. **结构化工作流程**：定义了从需求理解到计划生成的清晰流程
2. **全面的输出框架**：指定了计划文档应包含的所有关键部分
3. **互动策略指导**：引导 AI 在需要时主动提问，获取更多信息
4. **专业性要求**：强调使用领域相关术语和最佳实践

### 如何使用

1. 将此系统提示词设置为 AI 的系统指令
2. 用户只需提供简单的需求描述，如"我想创建一个工具，能自动为不同编程库生成最佳实践指南"
3. AI 将按照系统提示词的指导，通过提问和分析，生成详细的执行计划

### 示例对话流程

**用户**：我想创建一个工具，能自动为不同编程库生成最佳实践指南

**AI**：
1. 首先提出澄清问题：
   - "您希望支持哪些类型的编程库？"
   - "输出格式有特定要求吗？"
   - "您计划如何获取最佳实践信息？"

2. 基于用户回答，提供技术建议：
   - 推荐适合的搜索 API
   - 建议使用大语言模型处理内容
   - 提供文件格式建议

3. 最后生成完整的执行计划文档，包含：
   - 项目概述
   - 详细的实施步骤
   - 技术栈说明
   - 文件结构示例
   - 执行时间线
   - 潜在挑战与解决方案

通过这个系统提示词，AI 可以有效地将用户的简单需求转化为专业、详细的执行计划，为项目实施提供清晰的指导。


---

