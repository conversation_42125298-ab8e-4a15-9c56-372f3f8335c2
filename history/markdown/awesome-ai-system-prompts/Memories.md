# awesome-ai-system-prompts 的记忆

# Augment Code Analysis
- 用户请求我分析Augment Code的系统提示词，我详细解释了工具列表、remember工具和promptTemplates部分的功能和用途。

# Knowledge Management and Sharing
- 用户希望使用AI帮助理解Augment Code的博客内容并更好地分享给他人，需要组织系统提示词来完成知识理解和萃取。
- User wants blog content translated to Chinese and organized in a dedicated folder structure.
- User wants blog content analyzed for technical concepts, knowledge organization, and sharing optimization, with focus on Augment Code's technology.
- User is interested in analyzing Augment's product differentiation strategy, competitive advantages, and how technical authority is established in AI coding tools.