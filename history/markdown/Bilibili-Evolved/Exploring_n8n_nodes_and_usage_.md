# Exploring n8n nodes and usage


**项目**: Bilibili-Evolved

**创建时间**: 2025年05月09日 07:42

**最后交互**: 2025年05月09日 07:49

**对话ID**: 9c002fef-b87f-4fa7-bc4f-51f6815ae610

---

### 对话 1

> **👤 用户** (2025年05月09日 07:43)

使用 context7 ,查看 n8n-docs , 为我梳理支持的节点有哪些？该如何使用？

> **🤖 Augment** (2025年05月09日 07:43)

I'll help you explore the n8n documentation using Context7 to understand what nodes are supported and how to use them. Let me retrieve this information for you.

First, I'll resolve the n8n library ID using Context7.


Now I'll retrieve the documentation for n8n to understand the supported nodes and how to use them:


Based on the documentation I've retrieved, I'll organize the information about n8n's supported nodes and how to use them.

# n8n Nodes and Usage Guide

## What is n8n?

n8n is a fair-code licensed automation tool that allows you to build workflows connecting various services and applications. It provides both a free community edition and enterprise options, with the ability to build AI functionality into your workflows.

## Supported Node Categories

Based on the documentation, n8n supports several types of nodes:

### 1. Core Nodes

These are the fundamental building blocks for workflows:

- **Manual Trigger**: Starts workflows manually with a button click
- **Code**: Runs JavaScript or Python code to transform data
- **HTTP Request**: Makes API calls to external services
- **Execute Command**: Runs shell commands
- **Loop Over Items**: Iterates through items in a workflow
- **If**: Implements conditional logic in workflows
- **Merge**: Combines data from multiple sources
- **Split in Batches**: Processes data in batches
- **Date & Time**: Handles date and time operations
- **Convert to File**: Transforms data into file format

### 2. App Integration Nodes

n8n connects with many third-party services, including:

- **Discord**: Sends messages to Discord channels
- **Gmail**: Interacts with Gmail for sending/receiving emails
- **Airtable**: Connects with Airtable databases
- **RSS Read**: Reads RSS feeds
- **MySQL**: Interacts with MySQL databases
- **Postgres**: Connects with PostgreSQL databases
- **SendGrid/FriendGrid**: Email marketing platform integration

### 3. Utility Nodes

- **Edit Fields**: Modifies data fields
- **Sort**: Orders items based on specified criteria
- **Convert to File**: Transforms data into files

## How to Use n8n

### Installation Options

1. **Using npm (globally)**:
   ```bash
   npm install n8n -g
   ```

2. **Using Docker**:
   ```bash
   docker volume create n8n_data
   docker run -it --rm --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n
   ```

3. **With PostgreSQL**:
   ```bash
   docker run -it --rm \
    --name n8n \
    -p 5678:5678 \
    -e DB_TYPE=postgresdb \
    -e DB_POSTGRESDB_DATABASE=<POSTGRES_DATABASE> \
    -e DB_POSTGRESDB_HOST=<POSTGRES_HOST> \
    -e DB_POSTGRESDB_PORT=<POSTGRES_PORT> \
    -e DB_POSTGRESDB_USER=<POSTGRES_USER> \
    -e DB_POSTGRESDB_SCHEMA=<POSTGRES_SCHEMA> \
    -e DB_POSTGRESDB_PASSWORD=<POSTGRES_PASSWORD> \
    -v n8n_data:/home/<USER>/.n8n \
    docker.n8n.io/n8nio/n8n
   ```

### Creating Workflows

1. **Basic Workflow Structure**:
   - Workflows consist of connected nodes that process data
   - Data flows from one node to another
   - Each node performs a specific operation

2. **Data Structure**:
   n8n uses a specific data structure for passing information between nodes:
   ```json
   [
     {
       "json": {
         // Regular data goes here
         "key": "value"
       },
       "binary": {
         // Binary data like files goes here
         "filename": {
           "data": "base64-encoded-content",
           "mimeType": "image

---

### 对话 2

> **👤 用户** (2025年05月09日 07:44)

使用中文回复

> **🤖 Augment** (2025年05月09日 07:44)

我将使用中文为您查询n8n文档并梳理支持的节点信息。让我先通过Context7工具获取n8n的文档信息。


现在我将获取n8n文档的详细内容，了解其支持的节点和使用方法：


根据获取的n8n文档信息，我将为您梳理n8n支持的节点类型及其使用方法：

# n8n节点概述与使用指南

## 节点类型分类

n8n支持多种类型的节点，主要可以分为以下几类：

### 1. 核心节点（Core Nodes）
这些是n8n的基础功能节点，包括：
- **Code节点**：执行JavaScript或Python代码
- **Merge节点**：合并来自不同节点的数据
- **If节点**：条件判断和分支
- **Switch节点**：多条件分支
- **Remove Duplicates节点**：删除重复数据
- **GraphQL节点**：执行GraphQL查询

### 2. 应用节点（App Nodes）
连接各种第三方服务和API的节点，例如：
- **Google Analytics**：处理分析数据
- **WordPress**：管理文章、页面和用户
- **LinkedIn**：发布内容和管理社交媒体
- **Zoom**：创建和管理会议
- **Gong**：管理通话和用户
- **Hacker News**：获取新闻数据
- **Harvest**：时间跟踪和项目管理
- **Iterable**：营销自动化
- **Tapfiliate**：联盟营销
- **SIGNL4**：警报和通知
- **UptimeRobot**：监控服务

### 3. 触发节点（Trigger Nodes）
用于启动工作流的节点，通常基于事件或定时器。

### 4. 集群节点（Cluster Nodes）
包括：
- **根节点（Root Nodes）**：如LangChain Code、Text Classifier、Information Extractor等
- **子节点（Sub-nodes）**：为根节点提供特定功能的节点

### 5. AI相关节点
专门用于AI和机器学习功能的节点，如：
- **LangChain集成节点**：向量存储、文本分类器等
- **Information Extractor**：从文本中提取结构化信息

## 节点使用方法

### 基本使用流程

1. **添加节点**：从节点库中选择所需节点
2. **配置节点**：设置节点的参数和操作
3. **连接节点**：将节点连接起来形成工作流
4. **执行工作流**：测试和运行工作流

### 节点配置关键参数

大多数节点都有以下关键配置项：

1. **Resource（资源）**：指定要操作的资源类型（如WordPress的Post、Page或User）
2. **Operation（操作）**：指定要执行的操作（如Create、Get、Update等）
3. **认证凭据**：连接第三方服务所需的API密钥或凭据
4. **特定参数**：根据不同节点和操作需要的特定参数

### 数据处理与映射

1. **表达式**：使用`{{ $json.fieldName }}`格式访问输入数据
2. **节点间数据访问**：
   - JavaScript: `$("<node-name>").all()`, `$("<node-name>").first()`, `$("<node-name>").last()`
   - Python: `_("<node-name>").all()`, `_("<node-name>").first()`, `_("<node-name>").last()`

### 代码节点使用

1. **JavaScript示例**：
```javascript
// 返回数据必须是包含json属性的对象数组
return [
  {
    json: {
      name: 'Example',
      value: 123
    }
  }
];
```

2. **Python示例**：
```python
# 访问输入数据
input_data = _input.all()
# 处理数据
result = []
for item in input_data:
    # 转换JsProxy对象为Python字典
    item_dict = item.json.to_py()
    # 处理数据...
    result.append({"json": {"processed": item_dict}})
return result
```

## 常见节点示例

### WordPress节点
支持的操作：
- Post：创建、获取、更新文章
- Pages：创建、获取、更新页面
- User：创建、获取、更新用户

### LinkedIn节点
配置选项：
- Post As：选择以个人或组织身份发布
- Person Name/ID或Organization URN：指定发布者
- Text：发布内容
- Media Category：包含图片或文章URL时使用

### Zoom节点
支持的操作：
- Meeting：创建、删除、获取、更新会议

### Merge节点
用于合并来自不同节点的数据，可以基于共同字段进行匹配。

## 高级功能

### 节点版本控制
n8n支持节点版本控制，可以在节点配置中指定版本：
```javascript
version: [1, 2, 3]
```

### 条件显示
可以根据条件显示或隐藏特定参数：
```javascript
displayOptions: {
  show: {
    '@version': 2,
  },
}
```

### 提示和帮助信息
节点可以提供提示信息，帮助用户正确配置：
```javascript
hints: [
  {
    message: "提示信息",
    type: 'info',
    location: 'outputPane',
    whenToDisplay: 'beforeExecution'
  }
]
```

## 总结

n8n提供了丰富的节点类型，从基础的数据处理到复杂的第三方服务集成，再到AI功能，几乎涵盖了所有自动化工作流需求。使用这些节点时，关键是理解每个节点的资源类型、可用操作以及如何正确配置参数。通过组合不同的节点，可以构建强大的自动化工作流，实现各种业务需求。

要深入了解特定节点的使用方法，建议查阅n8n官方文档中对应节点的详细说明，或者尝试使用n8n提供的模板和示例。


---

### 对话 3

> **👤 用户** (2025年05月09日 07:47)

当前项目是 n8n 工作流模板的文件内容，请你挑选2-3 个仔细阅读他们的共性、差异点，总结出这些模板文件的语法结构

> **🤖 Augment** (2025年05月09日 07:47)

我将为您分析n8n工作流模板文件的内容，找出它们的共性和差异点，并总结其语法结构。让我先查看当前项目中的工作流模板文件。


我需要查找更多与n8n工作流模板相关的文件，特别是JSON格式的工作流定义文件。


我需要继续搜索，看看是否有更具体的n8n工作流模板文件。


我需要尝试更具体的搜索，看看是否能找到n8n工作流模板文件。


让我尝试另一种方式搜索：


我找到了一个与工作流相关的文件，让我查看它的内容：


让我查看另一个工作流文件：


我还需要查找一下是否有其他类型的工作流文件：


我需要继续搜索，看看是否有更多与n8n相关的文件：


看起来在当前项目中没有找到包含n8n工作流模板的文件。这可能是因为当前项目并不是一个n8n项目，而是一个名为"Bilibili-Evolved"的项目，与n8n无关。

让我尝试通过web搜索来获取n8n工作流模板的示例：


---

