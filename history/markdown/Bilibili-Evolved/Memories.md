# Bilibili-Evolved 的记忆

# User Preferences
- User prefers responses in Chinese language.
- 用户希望新脚本的UI界面与原有脚本中的'提取文案按钮'及其弹窗保持一致。
- 用户期望当脚本检测到字幕URL时，提取字幕按钮应显示数字提示'1'，表示已监听到数据，用户可点击按钮获取字幕内容。

# Bilibili API
- Bilibili's API response at /x/player/wbi/v2 contains subtitle information in data.subtitle.subtitles array, with each subtitle having a subtitle_url that can be used to fetch the actual subtitle JSON data.