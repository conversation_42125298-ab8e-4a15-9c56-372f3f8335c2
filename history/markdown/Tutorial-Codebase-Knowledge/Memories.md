# Tutorial-Codebase-Knowledge 的记忆

# PocketFlow and LLM Workflow
- User is interested in understanding the PocketFlow framework and the LLM workflow nodes in the codebase.
- User is interested in understanding the LLM calling process implementation in utils/call_llm.py.
- User prefers using OpenRouter or OpenAI with custom baseURL instead of Google Gemini due to network connectivity issues.

# Tutorial-Codebase-Knowledge Project
- User wants to develop a user-friendly frontend website for the Tutorial-Codebase-Knowledge project to host the functionality.
- User wants to implement the frontend interface with HTML prototypes using Tailwind CSS/Bootstrap and FontAwesome, with responsive design and organized file structure in a new html folder under frontDocs.
- User wants to create a high-fidelity clone of shumerprompt.com using Tailwind CSS/Bootstrap and FontAwesome, with responsive design and organized file structure in a new shumerprompt folder at the project root.
- User wants to create a high-fidelity clone of mcp.so website using HTML, Tailwind CSS/Bootstrap and FontAwesome, with responsive design and organized file structure in a new mcp folder at the project root.
- User provided a comprehensive alternative PRD for ShumerPrompt with detailed market analysis, technical specifications, business model, and implementation roadmap that differs from the current PRD.md.
- User wants to pause HTML page generation to create a comprehensive PRD document for the ShumerPrompt website clone.
- User wants to know whether Markdown content is directly embedded into HTML files or dynamically referenced.
- User is interested in recommendations for implementing dynamic frontend rendering.
- User prefers object storage for storing generated Markdown documents in the Tutorial-Codebase-Knowledge project.
- User prefers using Supabase for metadata storage in the Tutorial-Codebase-Knowledge project.
- User wants to implement the entire workflow on the frontend, directly calling GitHub API and LLM APIs, without relying on the current backend. This involves replicating the existing backend functionality on the frontend.
- User prefers to focus only on GitHub repository integration for the frontend solution, removing local file upload functionality.
- User is considering implementing the project with a frontend directly using Supabase as a BaaS backend solution instead of a custom backend.
- User is experiencing row-level security policy errors when uploading files to Supabase Storage in the Tutorial-Codebase-Knowledge project.
- User wants to directly sync local folders to Supabase without generating documents first.
- User wants to include the Lovable implementation plan in the V2 documentation.
- User prefers simplified content in detail pages while maintaining consistent styling and design.

# Lovable AI
- User wants to use Lovable AI programming software to implement the frontend interface design.
- User wants to know how to interact with Lovable AI, including how to provide existing documents to Lovable.
- User wants to use Lovable.dev to implement the mcp.so website clone.
- User wants to focus on the server detail page when writing prompts for Lovable.dev.

# General Preferences
- User prefers communication in Chinese.
- User is concerned about potential redundant logic in the code and prefers to remove it, while ensuring core functionality remains intact.
- User prefers simplified documentation with less technical details while maintaining the same functionality.
- User prefers that I split my output into multiple parts when a single response is too long.