# Roo-Cline 的记忆

# User Preferences
- User prefers responses in Chinese.
- User prefers structured system prompts for code assistance tools with role definition, coding rules, language-specific rules, quality standards, documentation requirements, and conflict resolution mechanisms following best practices for LLM prompt engineering.

# Prompt Optimization
- 用户对自定义指令的加载顺序和最佳实践感兴趣，希望了解如何优化提示词以提高模型执行效果。