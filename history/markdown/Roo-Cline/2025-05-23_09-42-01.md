# 2025-05-23_09-42-01

**项目**: <PERSON>oo-<PERSON><PERSON>

**创建时间**: 2025年05月23日 09:42

**最后交互**: 2025年05月26日 01:54

**对话ID**: e8adc698-9661-451f-8ba8-e54a76539103

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月23日 09:42)

Please analyze how custom rules are implemented in this project. Specifically:

1. Explain the implementation mechanism of custom rules in the codebase
2. Describe the loading mechanism for custom rules - how are they discovered, loaded, and applied?
3. What problems do these custom rules solve in the project architecture?
4. What are the best practices for creating and using custom rules in this project?

Please provide code examples and reference specific files where relevant to illustrate your explanation.

使用中文回复 

> **🤖 Augment** (2025年05月23日 09:42)

I'll analyze how custom rules are implemented in this project. Let me first search for information about custom rules in the codebase to understand their implementation mechanism.


Let me gather more information about the custom rules implementation by examining specific files related to rules.


Let me look at the implementation of custom rules loading in the codebase:


Let me also check how these custom rules are integrated into the system prompt:


Now let me check how custom modes are managed:


Based on the information gathered, I'll now provide a comprehensive analysis of how custom rules are implemented in this project.

# 自定义规则在项目中的实现分析

## 1. 自定义规则的实现机制

自定义规则在该项目中是通过文件系统实现的，主要有以下几种形式：

1. **目录结构化规则**：
   - `.roo/rules/` 目录：存放通用规则文件
   - `.roo/rules-{mode}/` 目录：存放特定模式的规则文件

2. **单文件规则**（向后兼容）：
   - `.roorules` 和 `.clinerules`：通用规则文件
   - `.roorules-{mode}` 和 `.clinerules-{mode}`：特定模式的规则文件

3. **规则加载优先级**：
   - 模式特定规则优先于通用规则
   - 目录结构化规则优先于单文件规则
   - 项目级规则优先于全局规则

规则文件通常使用Markdown格式编写，这使得它们易于阅读和编辑。规则内容会被添加到AI模型的系统提示中，作为"用户自定义指令"的一部分。

## 2. 自定义规则的加载机制

规则的发现、加载和应用过程如下：

### 发现机制

1. **目录扫描**：
   ```typescript
   // 检查.roo/rules/目录
   const rooRulesDir = path.join(cwd, ".roo", "rules")
   if (await directoryExists(rooRulesDir)) {
     const files = await readTextFilesFromDirectory(rooRulesDir)
     // ...
   }
   ```

2. **文件递归读取**：
   ```typescript
   async function readTextFilesFromDirectory(dirPath: string): Promise<Array<{ filename: string; content: string }>> {
     // 读取目录中的所有条目（包括子目录）
     const entries = await fs.readdir(dirPath, { withFileTypes: true, recursive: true })
     // 处理文件和符号链接
     // ...
   }
   ```

3. **符号链接处理**：系统支持处理符号链接，可以链接到其他规则文件或目录，最大递归深度为5层。

### 加载机制

1. **规则文件加载**：
   ```typescript
   export async function loadRuleFiles(cwd: string): Promise<string> {
     // 首先检查.roo/rules/目录
     // 如果不存在，则回退到.roorules或.clinerules文件
     // ...
   }
   ```

2. **模式特定规则加载**：
   ```typescript
   // 检查.roo/rules-${mode}/目录
   const modeRulesDir = path.join(cwd, ".roo", `rules-${mode}`)
   if (await directoryExists(modeRulesDir)) {
     const files = await readTextFilesFromDirectory(modeRulesDir)
     // ...
   }
   ```

3. **文件内容格式化**：
   ```typescript
   function formatDirectoryContent(dirPath: string, files: Array<{ filename: string; content: string }>): string {
     // 将多个文件内容格式化为带有文件名标题的文本
     // ...
   }
   ```

### 应用机制

1. **添加到系统提示**：
   ```typescript
   export async function addCustomInstructions(
     modeCustomInstructions: string,
     globalCustomInstructions: string,
     cwd: string,
     mode: string,
     options: { language?: string; rooIgnoreInstructions?: string } = {},
   ): Promise<string> {
     // 组合各种规则和指令
     // ...
   }
   ```

2. **规则组合顺序**：
   - 首先添加模式特定规则
   - 然后添加通用规则
   - 最后将所有规则作为"用户自定义指令"添加到系统提示中

3. **集成到系统提示**：
   ```typescript
   const customInstructions = await addCustomInstructions(
     promptComponent?.customInstructions || currentMode.customInstructions || "",
     globalCustomInstructions || "",
     cwd,
     mode,
     { language: language ?? formatLanguage(vscode.env.language), rooIgnoreInstructions },
   )
   ```

## 3. 自定义规则解决的问题

自定义规则在项目架构中解决了以下关键问题：

1. **上下文适应性**：允许AI模型根据不同的工作模式调整其行为，例如翻译模式下有特定的语言处理规则。

2. **代码质量保证**：通过定义代码质量规则（如测试覆盖率要求、lint规则等），确保生成的代码符合项目标准。

3. **工作流程标准化**：为特定任务（如翻译）定义标准工作流程，确保一致性和质量。

4. **知识封装**：将项目特定的知识和最佳实践封装在规则文件中，使AI能够遵循项目特定的约定。

5. **灵活的配置**：支持项目级和全局级配置，允许在不同级别自定义AI行为。

6. **多语言支持**：通过特定的语言规则文件（如`instructions-zh-cn.md`），提供针对不同语言的特定指导。

7. **模块化和可维护性**：通过将规则分散到多个文件中，使规则更易于管理和更新。

## 4. 创建和使用自定义规则的最佳实践

基于代码库分析，以下是创建和使用自定义规则的最佳实践：

### 规则组织

1. **使用目录结构**：优先使用`.roo/rules/`和`.roo/rules-{mode}/`目录结构，而不是单文件规则。

2. **文件命名约定**：
   - 使用有意义的文件名，如`001-general-rules.md`
   - 可以使用数字前缀来控制规则的加载顺序

3. **模块化规则**：将不同类型的规则分散到多个文件中，而不是放在一个大文件中。

### 规则内容

1. **使用Markdown格式**：规则文件应使用Markdown格式，使用标题、列表和代码块等结构化元素。

2. **明确的规则分类**：
   ```markdown
   # 代码质量规则
   
   1. 测试覆盖率:
       - 在尝试完成之前，始终确保任何代码更改都有测试覆盖
       - 确保所有测试在提交更改前通过
   ```

3. **提供具体示例**：
   ```markdown
   - 示例: `<div className="text-md text-vscode-descriptionForeground mb-2" />` 而不是样式对象
   ```

4. **包含技术细节**：
   ```markdown
   - 占位符（如{{variable}}）必须与英文源保持完全相同，以维护代码集成并防止语法错误
   ```

### 模式特定规则

1. **为特定任务创建专用模式**：如翻译模式（`translate`）有其特定的规则集。

2. **语言特定指导**：为不同语言创建特定的指导文件，如`instructions-zh-cn.md`和`instructions-de.md`。

3. **技术术语表**：在语言特定规则中包含技术术语表，确保术语翻译的一致性。
   ```markdown
   | English Term  | Use (zh-TW) | Avoid (Mainland) |
   | ------------- | ----------- | ---------------- |
   | file          | 檔案        | 文件             |
   ```

### 规则应用

1. **项目级与全局级规则**：
   - 项目级规则（`.roomodes`）优先于全局规则
   - 全局规则适用于所有项目，而项目规则仅适用于特定项目

2. **规则监控**：系统会监控规则文件的变化，当规则文件保存时自动重新加载。
   ```typescript
   this.disposables.push(
     vscode.workspace.onDidSaveTextDocument(async (document) => {
       if (arePathsEqual(document.uri.fsPath, roomodesPath)) {
         // 重新加载规则
       }
     })
   )
   ```

3. **规则验证**：对于某些任务（如翻译），提供验证脚本以确保规则被正确应用。
   ```markdown
   - 始终通过运行缺失翻译脚本验证您的翻译工作:
       ```
       node scripts/find-missing-translations.js
       ```
   ```

## 代码示例

### 1. 创建基本规则文件

```markdown
# 代码风格规则

1. 命名约定:
   - 使用驼峰命名法命名变量和函数
   - 使用PascalCase命名类和组件
   - 使用UPPER_SNAKE_CASE命名常量

2. 代码组织:
   - 每个文件不超过300行
   - 每个函数不超过50行
   - 相关功能应分组在同一目录中
```

### 2. 加载规则的核心代码

```typescript
// 加载规则文件
export async function loadRuleFiles(cwd: string): Promise<string> {
  // 检查.roo/rules/目录
  const rooRulesDir = path.join(cwd, ".roo", "rules")
  if (await directoryExists(rooRulesDir)) {
    const files = await readTextFilesFromDirectory(rooRulesDir)
    if (files.length > 0) {
      return formatDirectoryContent(rooRulesDir, files)
    }
  }

  // 回退到现有行为
  const ruleFiles = [".roorules", ".clinerules"]

  for (const file of ruleFiles) {
    const content = await safeReadFile(path.join(cwd, file))
    if (content) {
      return `\n# Rules from ${file}:\n${content}\n`
    }
  }

  return ""
}
```

### 3. 将规则应用到系统提示

```typescript
// 添加自定义指令到系统提示
export async function addCustomInstructions(
  modeCustomInstructions: string,
  globalCustomInstructions: string,
  cwd: string,
  mode: string,
  options: { language?: string; rooIgnoreInstructions?: string } = {},
): Promise<string> {
  // ...

  // 添加规则 - 如果存在，则同时包含模式特定和通用规则
  const rules = []

  // 如果存在，则首先添加模式特定规则
  if (modeRuleContent && modeRuleContent.trim()) {
    if (usedRuleFile.includes(path.join(".roo", `rules-${mode}`))) {
      rules.push(modeRuleContent.trim())
    } else {
      rules.push(`# Rules from ${usedRuleFile}:\n${modeRuleContent}`)
    }
  }

  // 添加通用规则
  const genericRuleContent = await loadRuleFiles(cwd)
  if (genericRuleContent && genericRuleContent.trim()) {
    rules.push(genericRuleContent.trim())
  }

  if (rules.length > 0) {
    sections.push(`Rules:\n\n${rules.join("\n\n")}`)
  }

  // ...
}
```

## 总结

自定义规则系统是该项目的一个核心功能，它允许用户通过简单的文件系统机制定制AI助手的行为。这种设计既灵活又强大，能够适应各种不同的工作流程和项目需求。通过将规则组织到目录结构中，项目实现了规则的模块化和可维护性，同时支持不同级别（全局和项目级）的规则定义。

规则系统特别适合处理特定领域的任务，如代码质量保证和多语言翻译，通过提供明确的指导和约束，确保AI助手能够按照项目的特定要求工作。这种方法不仅提高了AI输出的质量，还增强了其在特定项目环境中的适用性。


---

### 对话 3

> **👤 用户** (2025年05月23日 09:46)

使用流程图的方式为我输出 规则的加载顺序，加载机制等

> **🤖 Augment** (2025年05月23日 09:46)

我将使用流程图的方式为您展示规则的加载顺序和加载机制。


# 规则加载顺序与加载机制流程图

## 1. 自定义模式加载流程

```mermaid
flowchart TD
    A[开始加载自定义模式] --> B{检查是否存在<br>.roomodes文件}
    B -->|是| C[加载项目级模式<br>从.roomodes文件]
    B -->|否| D[项目级模式为空]
    C --> E[加载全局级模式<br>从全局设置文件]
    D --> E
    E --> F{项目级模式中<br>是否存在相同slug}
    F -->|是| G[使用项目级模式<br>忽略全局级模式]
    F -->|否| H[保留全局级模式]
    G --> I[合并模式列表<br>项目级模式优先]
    H --> I
    I --> J[缓存合并后的模式列表]
    J --> K[结束]
```

## 2. 规则文件发现和加载流程

```mermaid
flowchart TD
    A[开始加载规则] --> B{是否指定模式?}
    B -->|是| C[加载模式特定规则]
    B -->|否| G[跳过模式特定规则]
    
    C --> D{检查.roo/rules-模式/<br>目录是否存在}
    D -->|是| E[递归读取目录中所有文件<br>按字母顺序排序]
    D -->|否| F{检查.roorules-模式<br>文件是否存在}
    F -->|是| F1[读取.roorules-模式文件]
    F -->|否| F2{检查.clinerules-模式<br>文件是否存在}
    F2 -->|是| F3[读取.clinerules-模式文件]
    F2 -->|否| F4[模式特定规则为空]
    
    E --> M[格式化模式特定规则内容]
    F1 --> M
    F3 --> M
    F4 --> M
    
    G --> H[加载通用规则]
    M --> H
    
    H --> I{检查.roo/rules/<br>目录是否存在}
    I -->|是| J[递归读取目录中所有文件<br>按字母顺序排序]
    I -->|否| K{检查.roorules<br>文件是否存在}
    K -->|是| K1[读取.roorules文件]
    K -->|否| K2{检查.clinerules<br>文件是否存在}
    K2 -->|是| K3[读取.clinerules文件]
    K2 -->|否| K4[通用规则为空]
    
    J --> N[格式化通用规则内容]
    K1 --> N
    K3 --> N
    K4 --> N
    
    N --> O[合并所有规则<br>模式特定规则优先]
    O --> P[结束]
```

## 3. 目录文件读取流程

```mermaid
flowchart TD
    A[开始读取目录] --> B[获取目录中所有条目<br>包括子目录]
    B --> C[遍历每个条目]
    C --> D{是文件?}
    D -->|是| E[添加到文件路径列表]
    D -->|否| F{是符号链接?}
    F -->|是| G[解析符号链接<br>最大递归深度5层]
    F -->|否| H[忽略]
    G --> I{链接指向文件?}
    I -->|是| J[添加到文件路径列表]
    I -->|否| K{链接指向目录?}
    K -->|是| L[递归读取链接目录]
    K -->|否| M[忽略]
    L --> N[添加所有文件到列表]
    
    E --> O[读取所有文件内容]
    J --> O
    N --> O
    H --> C
    M --> C
    
    O --> P[按文件名排序]
    P --> Q[返回文件内容列表]
    Q --> R[结束]
```

## 4. 规则应用到系统提示流程

```mermaid
flowchart TD
    A[开始构建系统提示] --> B{是否有语言偏好?}
    B -->|是| C[添加语言偏好部分]
    B -->|否| D[跳过语言偏好]
    
    C --> E{是否有全局指令?}
    D --> E
    E -->|是| F[添加全局指令部分]
    E -->|否| G[跳过全局指令]
    
    F --> H{是否有模式特定指令?}
    G --> H
    H -->|是| I[添加模式特定指令部分]
    H -->|否| J[跳过模式特定指令]
    
    I --> K{是否有规则?}
    J --> K
    K -->|是| L[添加规则部分<br>模式特定规则优先]
    K -->|否| M[跳过规则部分]
    
    L --> N[组合所有部分<br>添加到USER'S CUSTOM INSTRUCTIONS]
    M --> N
    
    N --> O[将自定义指令添加到系统提示]
    O --> P[结束]
```

## 5. 规则加载优先级

```mermaid
flowchart TD
    A[规则加载优先级] --> B[1. 模式特定规则]
    B --> C[1.1 .roo/rules-模式/目录<br>按文件名字母顺序]
    C --> D[1.2 .roorules-模式文件]
    D --> E[1.3 .clinerules-模式文件]
    
    A --> F[2. 通用规则]
    F --> G[2.1 .roo/rules/目录<br>按文件名字母顺序]
    G --> H[2.2 .roorules文件]
    H --> I[2.3 .clinerules文件]
    
    A --> J[3. 模式加载优先级]
    J --> K[3.1 项目级模式<br>.roomodes文件]
    K --> L[3.2 全局级模式<br>全局设置文件]
```

## 6. 完整规则处理流程

```mermaid
flowchart TD
    A[开始] --> B[加载自定义模式<br>项目级优先于全局级]
    B --> C[选择当前模式]
    C --> D[加载模式特定规则]
    D --> E[加载通用规则]
    E --> F[合并所有规则<br>模式特定规则优先]
    F --> G[添加语言偏好<br>全局指令<br>模式特定指令]
    G --> H[将所有内容添加到<br>系统提示]
    H --> I[AI使用系统提示<br>处理用户请求]
    I --> J[结束]
```

以上流程图详细展示了规则的加载顺序、加载机制以及如何应用到系统提示中。从图中可以清晰地看到：

1. 模式加载优先级：项目级 > 全局级
2. 规则文件优先级：目录结构 > 单文件
3. 规则类型优先级：模式特定规则 > 通用规则
4. 目录内文件按字母顺序加载，可以通过文件名前缀控制加载顺序
5. 规则最终被添加到系统提示的"USER'S CUSTOM INSTRUCTIONS"部分

这种设计使得规则系统既灵活又强大，能够适应各种不同的工作流程和项目需求。


---

### 对话 4

> **👤 用户** (2025年05月23日 09:50)

为我通过一个示例来说明，包括规则的目录树，每个文件的简短描述，加载的顺序，最终注入的逻辑

> **🤖 Augment** (2025年05月23日 09:50)

# 自定义规则加载示例详解

我将通过一个具体示例来说明规则的加载过程，包括目录结构、文件内容、加载顺序以及最终注入的逻辑。

## 示例项目目录结构

```
项目根目录/
├── .roo/
│   ├── rules/
│   │   ├── 001-code-quality.md     # 代码质量通用规则
│   │   └── 002-documentation.md    # 文档规范通用规则
│   │
│   ├── rules-translate/
│   │   ├── 001-general-rules.md    # 翻译模式通用规则
│   │   ├── 002-zh-cn-rules.md      # 中文翻译特定规则
│   │   └── 003-terminology.md      # 术语表规则
│   │
│   └── rules-code/
│       ├── 001-typescript.md       # TypeScript编码规则
│       └── 002-react.md            # React组件规则
│
├── .roorules                       # 旧版通用规则文件
└── .roorules-translate             # 旧版翻译模式规则文件
```

## 文件内容简介

### 1. 通用规则文件

#### `.roo/rules/001-code-quality.md`
```markdown
# 代码质量规则

1. 测试覆盖率:
   - 所有新代码必须有单元测试
   - 测试覆盖率不得低于80%

2. 代码风格:
   - 使用ESLint规则检查代码
   - 不允许禁用lint规则
```

#### `.roo/rules/002-documentation.md`
```markdown
# 文档规范

1. 函数文档:
   - 所有公共函数必须有JSDoc注释
   - 参数和返回值必须有类型说明

2. 模块文档:
   - 每个模块顶部必须有模块描述
```

#### `.roorules` (旧版通用规则)
```markdown
# 旧版通用规则
- 使用TypeScript而不是JavaScript
- 使用函数式编程风格
```

### 2. 翻译模式规则文件

#### `.roo/rules-translate/001-general-rules.md`
```markdown
# 翻译通用规则

1. 支持的语言:
   - 支持以下语言: zh-CN, zh-TW, en, ja, ko, de, fr, es

2. 翻译风格:
   - 保持非正式语气
   - 保留专业术语不翻译
```

#### `.roo/rules-translate/002-zh-cn-rules.md`
```markdown
# 中文翻译规则

1. 标点符号:
   - 使用中文标点符号
   - 中英文之间加空格

2. 术语翻译:
   - "file" 翻译为 "文件"
   - "token" 保留英文不翻译
```

#### `.roo/rules-translate/003-terminology.md`
```markdown
# 术语表

| 英文术语 | 中文翻译 | 备注 |
|---------|---------|------|
| API     | API     | 保留原文 |
| file    | 文件    | |
| folder  | 文件夹  | |
```

#### `.roorules-translate` (旧版翻译规则)
```markdown
# 旧版翻译规则
- 使用中文标点符号
- 保持专业术语一致性
```

### 3. 代码模式规则文件

#### `.roo/rules-code/001-typescript.md`
```markdown
# TypeScript规则

1. 类型定义:
   - 使用接口而不是类型别名
   - 避免使用any类型

2. 异步处理:
   - 使用async/await而不是Promise链
```

#### `.roo/rules-code/002-react.md`
```markdown
# React规则

1. 组件结构:
   - 使用函数组件而不是类组件
   - 使用React Hooks管理状态

2. 性能优化:
   - 使用React.memo避免不必要的重渲染
   - 使用useCallback和useMemo优化性能
```

## 规则加载过程示例

假设用户当前选择了"translate"模式，并且系统语言设置为中文(zh-CN)，我们来看看规则的加载过程：

### 1. 加载自定义模式

首先，系统会检查是否存在项目级的自定义模式定义（`.roomodes`文件）。如果存在，则加载项目级模式；如果不存在，则加载全局级模式。

在这个例子中，假设没有项目级模式定义，系统使用内置的"translate"模式。

### 2. 加载模式特定规则

系统开始加载"translate"模式的特定规则：

1. 检查 `.roo/rules-translate/` 目录是否存在
   - 目录存在，按字母顺序读取所有文件：
     - `001-general-rules.md`
     - `002-zh-cn-rules.md`
     - `003-terminology.md`

2. 由于找到了目录结构化规则，系统不会检查旧版的 `.roorules-translate` 文件

3. 格式化模式特定规则内容：
```markdown
# Rules from .roo/rules-translate/001-general-rules.md:
# 翻译通用规则

1. 支持的语言:
   - 支持以下语言: zh-CN, zh-TW, en, ja, ko, de, fr, es

2. 翻译风格:
   - 保持非正式语气
   - 保留专业术语不翻译

# Rules from .roo/rules-translate/002-zh-cn-rules.md:
# 中文翻译规则

1. 标点符号:
   - 使用中文标点符号
   - 中英文之间加空格

2. 术语翻译:
   - "file" 翻译为 "文件"
   - "token" 保留英文不翻译

# Rules from .roo/rules-translate/003-terminology.md:
# 术语表

| 英文术语 | 中文翻译 | 备注 |
|---------|---------|------|
| API     | API     | 保留原文 |
| file    | 文件    | |
| folder  | 文件夹  | |
```

### 3. 加载通用规则

接下来，系统加载通用规则：

1. 检查 `.roo/rules/` 目录是否存在
   - 目录存在，按字母顺序读取所有文件：
     - `001-code-quality.md`
     - `002-documentation.md`

2. 由于找到了目录结构化规则，系统不会检查旧版的 `.roorules` 文件

3. 格式化通用规则内容：
```markdown
# Rules from .roo/rules/001-code-quality.md:
# 代码质量规则

1. 测试覆盖率:
   - 所有新代码必须有单元测试
   - 测试覆盖率不得低于80%

2. 代码风格:
   - 使用ESLint规则检查代码
   - 不允许禁用lint规则

# Rules from .roo/rules/002-documentation.md:
# 文档规范

1. 函数文档:
   - 所有公共函数必须有JSDoc注释
   - 参数和返回值必须有类型说明

2. 模块文档:
   - 每个模块顶部必须有模块描述
```

### 4. 合并规则并添加到系统提示

系统将模式特定规则和通用规则合并，模式特定规则优先：

```markdown
Rules:

# Rules from .roo/rules-translate/001-general-rules.md:
# 翻译通用规则

1. 支持的语言:
   - 支持以下语言: zh-CN, zh-TW, en, ja, ko, de, fr, es

2. 翻译风格:
   - 保持非正式语气
   - 保留专业术语不翻译

# Rules from .roo/rules-translate/002-zh-cn-rules.md:
# 中文翻译规则

1. 标点符号:
   - 使用中文标点符号
   - 中英文之间加空格

2. 术语翻译:
   - "file" 翻译为 "文件"
   - "token" 保留英文不翻译

# Rules from .roo/rules-translate/003-terminology.md:
# 术语表

| 英文术语 | 中文翻译 | 备注 |
|---------|---------|------|
| API     | API     | 保留原文 |
| file    | 文件    | |
| folder  | 文件夹  | |

# Rules from .roo/rules/001-code-quality.md:
# 代码质量规则

1. 测试覆盖率:
   - 所有新代码必须有单元测试
   - 测试覆盖率不得低于80%

2. 代码风格:
   - 使用ESLint规则检查代码
   - 不允许禁用lint规则

# Rules from .roo/rules/002-documentation.md:
# 文档规范

1. 函数文档:
   - 所有公共函数必须有JSDoc注释
   - 参数和返回值必须有类型说明

2. 模块文档:
   - 每个模块顶部必须有模块描述
```

### 5. 添加语言偏好和其他指令

由于系统语言设置为中文(zh-CN)，系统会添加语言偏好指令：

```markdown
Language Preference:
You should always speak and think in the "Chinese (Simplified)" (zh-CN) language unless the user gives you instructions below to do otherwise.
```

如果有全局自定义指令和模式特定指令，也会添加到系统提示中：

```markdown
Global Instructions:
[全局自定义指令内容]

Mode-specific Instructions:
[翻译模式特定指令内容]
```

### 6. 最终注入的完整自定义指令

最终，所有内容被组合成完整的自定义指令部分，添加到系统提示中：

```markdown
====

USER'S CUSTOM INSTRUCTIONS

The following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.

Language Preference:
You should always speak and think in the "Chinese (Simplified)" (zh-CN) language unless the user gives you instructions below to do otherwise.

Global Instructions:
[全局自定义指令内容]

Mode-specific Instructions:
[翻译模式特定指令内容]

Rules:

# Rules from .roo/rules-translate/001-general-rules.md:
# 翻译通用规则

1. 支持的语言:
   - 支持以下语言: zh-CN, zh-TW, en, ja, ko, de, fr, es

2. 翻译风格:
   - 保持非正式语气
   - 保留专业术语不翻译

# Rules from .roo/rules-translate/002-zh-cn-rules.md:
# 中文翻译规则

1. 标点符号:
   - 使用中文标点符号
   - 中英文之间加空格

2. 术语翻译:
   - "file" 翻译为 "文件"
   - "token" 保留英文不翻译

# Rules from .roo/rules-translate/003-terminology.md:
# 术语表

| 英文术语 | 中文翻译 | 备注 |
|---------|---------|------|
| API     | API     | 保留原文 |
| file    | 文件    | |
| folder  | 文件夹  | |

# Rules from .roo/rules/001-code-quality.md:
# 代码质量规则

1. 测试覆盖率:
   - 所有新代码必须有单元测试
   - 测试覆盖率不得低于80%

2. 代码风格:
   - 使用ESLint规则检查代码
   - 不允许禁用lint规则

# Rules from .roo/rules/002-documentation.md:
# 文档规范

1. 函数文档:
   - 所有公共函数必须有JSDoc注释
   - 参数和返回值必须有类型说明

2. 模块文档:
   - 每个模块顶部必须有模块描述
```

## 加载顺序总结

在这个示例中，规则的加载顺序如下：

1. **模式特定规则**（按字母顺序）：
   - `.roo/rules-translate/001-general-rules.md`
   - `.roo/rules-translate/002-zh-cn-rules.md`
   - `.roo/rules-translate/003-terminology.md`

2. **通用规则**（按字母顺序）：
   - `.roo/rules/001-code-quality.md`
   - `.roo/rules/002-documentation.md`

3. **旧版规则文件**（由于找到了目录结构化规则，这些文件被忽略）：
   - `.roorules`
   - `.roorules-translate`

## 注入逻辑总结

1. 系统首先确定当前模式（本例中为"translate"）
2. 加载该模式的特定规则（从`.roo/rules-translate/`目录）
3. 加载通用规则（从`.roo/rules/`目录）
4. 添加语言偏好（基于系统语言设置）
5. 添加全局自定义指令和模式特定指令（如果有）
6. 将所有内容组合成完整的自定义指令部分
7. 将自定义指令部分添加到系统提示中
8. AI模型使用包含这些规则的系统提示来处理用户请求

这种分层的规则结构使得规则管理非常灵活：

- 通用规则适用于所有模式
- 模式特定规则只适用于特定模式
- 文件命名前缀控制加载顺序
- 目录结构化规则优先于旧版单文件规则
- 项目级规则优先于全局级规则

通过这种方式，项目可以为不同的工作流程和任务定制AI助手的行为，确保输出符合项目的特定要求和标准。


---

### 对话 5

> **👤 用户** (2025年05月23日 09:55)

那么从大语言模型提示词的角度，这种加载顺序会影响模型对指令的执行度吗？解释为什么？并且给出自定义指令的最佳实践 

> **🤖 Augment** (2025年05月23日 09:55)

# 规则加载顺序对大语言模型执行度的影响分析

## 加载顺序对模型执行度的影响

从大语言模型（LLM）提示词工程的角度来看，规则的加载顺序确实会显著影响模型对指令的执行度。以下是几个关键原因：

### 1. 提示词中的位置效应

**原因**：大语言模型存在"位置效应"（Position Effect），即提示词中靠前和靠后的内容对模型的影响不同。

- **靠前内容**：通常获得更高的注意力权重，对模型行为有更强的引导作用
- **靠后内容**：可能被前面的内容"覆盖"或稀释其影响力
- **最近内容**：靠近用户实际问题的指令可能获得"近因效应"（Recency Effect）的优势

在当前项目的加载顺序中，模式特定规则优先于通用规则，这意味着模式特定规则会获得更高的执行优先级，这符合预期的行为。

### 2. 指令冲突与覆盖

**原因**：当不同规则文件中存在相互冲突的指令时，加载顺序决定了哪条指令会被优先执行。

- 在当前实现中，后加载的通用规则可能会与先加载的模式特定规则产生冲突
- 由于模式特定规则先被加载，它们在提示词中的位置更靠前，因此在冲突情况下通常会优先被执行
- 然而，如果通用规则中有更明确、更具体的指令，模型可能会优先执行这些指令，导致不一致的行为

### 3. 上下文窗口限制

**原因**：所有LLM都有上下文窗口限制，当规则总量接近这一限制时，可能导致部分规则被截断或获得较少注意力。

- 如果规则总量过大，系统提示中靠后的规则可能会被模型"遗忘"或给予较少权重
- 在当前实现中，通用规则位于模式特定规则之后，因此在上下文窗口受限的情况下，通用规则可能获得较少的注意力

### 4. 规则的具体性与明确性

**原因**：不同规则的表述方式（具体vs抽象，明确vs模糊）会影响模型的执行度。

- 更具体、更明确的规则通常会获得更高的执行度，无论其在提示词中的位置如何
- 在当前实现中，如果模式特定规则表述模糊而通用规则表述明确，即使通用规则位于后面，也可能获得更高的执行度

### 5. 规则的分类与组织

**原因**：规则的分类方式和组织结构会影响模型对规则整体的理解和执行。

- 当前实现中，规则按文件名排序并按模式分组，这种组织方式有助于模型理解规则之间的关系
- 然而，如果规则分散在多个文件中且缺乏明确的分类标识，模型可能难以理解规则之间的优先级关系

## 自定义指令的最佳实践

基于以上分析，以下是针对该项目自定义指令的最佳实践建议：

### 1. 规则文件组织与命名

**最佳实践**：

- **使用明确的数字前缀**：继续使用`001-`、`002-`等前缀控制加载顺序，确保最重要的规则最先加载
- **采用分层命名结构**：例如`001-critical-rules.md`、`100-important-rules.md`、`500-optional-rules.md`，预留空间便于后续插入新规则
- **在文件名中反映规则优先级**：高优先级规则使用较小的前缀数字，确保它们先被加载

```
.roo/rules/
├── 001-critical-rules.md     # 最高优先级规则
├── 100-important-rules.md    # 重要但非关键规则
└── 500-optional-rules.md     # 可选规则
```

### 2. 规则内容结构化

**最佳实践**：

- **使用明确的优先级标记**：在规则内容中明确标注优先级，例如`[优先级：高]`、`[优先级：中]`、`[优先级：低]`
- **采用一致的格式**：所有规则文件使用一致的格式和结构，便于模型理解
- **使用分层标题**：使用Markdown标题层级清晰地组织规则，主要类别使用`#`，子类别使用`##`等

```markdown
# 代码质量规则 [优先级：高]

## 1. 测试覆盖率 [强制]
- 所有新代码必须有单元测试
- 测试覆盖率不得低于80%

## 2. 代码风格 [建议]
- 使用ESLint规则检查代码
- 不允许禁用lint规则
```

### 3. 避免规则冲突

**最佳实践**：

- **明确规则适用范围**：每条规则都应明确其适用范围，避免不同规则之间的模糊地带
- **建立规则优先级机制**：当规则可能冲突时，明确指出哪条规则优先
- **集中管理冲突规则**：将可能冲突的规则放在同一个文件中，确保它们被一起考虑

```markdown
# 规则优先级说明

当不同规则之间存在冲突时，请按以下优先级执行：
1. 安全相关规则
2. 性能相关规则
3. 代码风格规则
4. 文档规则
```

### 4. 优化规则表达方式

**最佳实践**：

- **使用指令性语言**：使用明确的指令性语言，如"必须"、"禁止"、"应该"，而不是描述性语言
- **提供具体示例**：对于复杂规则，提供正面和负面示例，帮助模型理解
- **使用结构化格式**：对于复杂规则，使用表格、列表等结构化格式，而不是长段落

```markdown
# TypeScript规则

## 类型定义 [强制]

✅ 正确示例:
```typescript
interface User {
  id: number;
  name: string;
}
```

❌ 错误示例:
```typescript
type User = {
  id: any;
  name: string;
}
```

- 必须使用接口而不是类型别名定义对象结构
- 禁止使用any类型，应使用unknown或具体类型
```

### 5. 控制规则总量与复杂度

**最佳实践**：

- **限制规则总量**：确保所有规则加起来不会接近模型的上下文窗口限制
- **按重要性精简规则**：定期审查规则，移除不必要或很少使用的规则
- **合并相似规则**：将相似或相关的规则合并，减少总体复杂度

### 6. 模式特定规则设计

**最佳实践**：

- **避免通用规则与模式规则重复**：模式特定规则应只包含该模式特有的指令，通用内容应放在通用规则中
- **明确标识模式规则**：在模式规则开头明确说明这是特定模式的规则，例如"以下规则仅适用于翻译模式"
- **处理规则继承关系**：明确说明模式规则是替换还是补充通用规则

```markdown
# 翻译模式特定规则

注意：以下规则仅适用于翻译模式，并且优先于任何通用规则。

## 规则继承说明
- 本文件中的规则将覆盖通用规则中的同名规则
- 未在本文件中提及的规则将继续遵循通用规则
```

### 7. 规则测试与验证

**最佳实践**：

- **定期测试规则执行度**：创建测试用例验证模型是否正确执行各种规则
- **监控规则冲突**：建立机制检测规则之间的潜在冲突
- **收集规则执行反馈**：记录规则未被正确执行的情况，用于改进规则表述

### 8. 完整示例：优化的规则文件

以下是一个优化后的规则文件示例，综合应用了上述最佳实践：

```markdown
# 翻译模式规则 [优先级：最高]

## 适用范围
本文件中的规则适用于所有翻译任务，并且优先于任何通用规则。

## 1. 语言风格 [强制]

- 必须使用非正式语气（如德语中使用"du"而非"Sie"）
- 必须保持原文的语气和风格
- 必须使用目标语言的自然表达方式，而非直译

## 2. 术语处理 [强制]

- 以下术语必须保留英文原文，不得翻译：
  - API
  - Token
  - Prompt
  - Endpoint

- 以下术语必须使用官方翻译：

| 英文术语 | 中文翻译 | 日文翻译 | 德文翻译 |
|---------|---------|---------|---------|
| file    | 文件    | ファイル | Datei   |
| folder  | 文件夹  | フォルダ | Ordner  |
| user    | 用户    | ユーザー | Benutzer|

## 3. 格式保留 [强制]

- 必须保留原文的所有格式标记（Markdown、HTML等）
- 必须保留原文的所有占位符，如`{{variable}}`
- 必须保留原文的所有代码块，代码内注释可以翻译

✅ 正确示例:
```markdown
## 用户设置 (User Settings)

使用`settings.json`文件配置您的{{appName}}。
```

❌ 错误示例:
```markdown
## User Settings

使用settings.json文件配置您的{{程序名称}}。
```

## 4. 文化适应性 [建议]

- 应根据目标语言的文化背景调整表达方式
- 应避免使用在目标语言文化中可能引起误解的比喻或表达

## 规则冲突处理

当本文件中的规则与其他规则冲突时，按以下优先级处理：
1. 本文件中的[强制]规则
2. 通用规则中的[强制]规则
3. 本文件中的[建议]规则
4. 通用规则中的[建议]规则
```

## 结论

规则的加载顺序确实会显著影响大语言模型对指令的执行度。在当前项目实现中，模式特定规则优先于通用规则的加载顺序是合理的，但仍有优化空间。

通过采用上述最佳实践，可以进一步提高模型对规则的理解和执行度，特别是在规则数量较多或存在潜在冲突的情况下。关键是要使规则结构化、明确化，并建立清晰的优先级机制，同时控制规则的总量和复杂度，确保最重要的规则获得最高的执行优先级。

最终，一个设计良好的规则系统应该是：结构清晰、优先级明确、表达具体、易于理解、便于维护，并且能够在不同模式和场景下一致地引导模型行为。


---

### 对话 6

> **👤 用户** (2025年05月26日 01:54)

基于你对自定义规则最佳实践的分析，请为我设计一套完整的系统提示词，用于指导大语言模型在软件开发过程中的行为。这套提示词应该：

1. 适用于 AugmentCode Agent、Cursor 或 Roo-Cline 等代码辅助工具
2. 根据我的项目需求和代码库特点进行定制
3. 遵循你之前分析的最佳实践（包括规则结构化、优先级明确、表达具体等）
4. 包含以下具体部分：
   - 角色定义（模型应该扮演什么角色）
   - 通用编码规则（适用于所有编程任务）
   - 特定语言/框架规则（针对我项目中使用的主要技术栈）
   - 代码质量与测试规则
   - 文档规范
   - 规则冲突处理机制

请提供结构化的、可直接使用的系统提示词，并解释每个部分的设计意图和预期效果。提示词应当遵循你之前分析的位置效应、规则优先级和表达方式等最佳实践。

> **🤖 Augment** (2025年05月26日 01:54)

# 软件开发代码辅助工具系统提示词设计

基于之前的最佳实践分析，我将为您设计一套完整的系统提示词。这套提示词遵循位置效应原理，将最重要的规则放在前面，并采用结构化、明确的表达方式。

## 完整系统提示词

```markdown
# 高级软件开发助手 - 系统提示词

## 角色定义 [优先级：最高]

您是一位资深的软件开发专家和代码架构师，专门为开发者提供高质量的代码辅助服务。您的核心职责包括：

- **代码生成与优化**：编写高质量、可维护、符合最佳实践的代码
- **架构设计指导**：提供合理的代码结构和架构建议
- **问题诊断与解决**：快速识别并解决代码问题
- **知识传授**：通过代码示例和解释帮助开发者学习和成长

### 工作原则 [强制执行]

1. **质量优先**：始终优先考虑代码质量而非开发速度
2. **安全第一**：确保所有代码建议都符合安全最佳实践
3. **可维护性**：编写易于理解、修改和扩展的代码
4. **性能意识**：在保证可读性的前提下优化性能
5. **测试驱动**：所有代码都应考虑可测试性

## 通用编码规则 [优先级：高]

### 1. 代码结构与组织 [强制]

- **单一职责原则**：每个函数/类只负责一个明确的功能
- **函数长度限制**：单个函数不超过50行，复杂逻辑必须拆分
- **文件长度限制**：单个文件不超过300行，超出时必须重构
- **命名规范**：使用描述性名称，避免缩写和模糊表达

✅ 正确示例:
```typescript
function calculateUserSubscriptionPrice(user: User, plan: SubscriptionPlan): number {
  // 清晰的函数名和参数类型
}
```

❌ 错误示例:
```typescript
function calc(u: any, p: any): any {
  // 模糊的命名和类型
}
```

### 2. 错误处理 [强制]

- **显式错误处理**：所有可能失败的操作都必须有错误处理
- **错误类型化**：使用类型化的错误对象，避免字符串错误
- **错误传播**：合理地向上传播错误，不要静默失败
- **用户友好错误**：面向用户的错误消息应该清晰易懂

```typescript
// 推荐的错误处理模式
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

async function fetchUserData(id: string): Promise<Result<User, UserNotFoundError | NetworkError>> {
  try {
    const response = await api.getUser(id);
    return { success: true, data: response };
  } catch (error) {
    if (error instanceof NetworkError) {
      return { success: false, error };
    }
    return { success: false, error: new UserNotFoundError(id) };
  }
}
```

### 3. 性能考虑 [重要]

- **避免过早优化**：先确保代码正确，再考虑性能优化
- **合理使用缓存**：对计算密集型操作实施缓存策略
- **内存管理**：及时清理不再使用的资源，避免内存泄漏
- **异步操作优化**：合理使用并发和批处理

## TypeScript/JavaScript 特定规则 [优先级：高]

### 1. 类型安全 [强制]

- **严格类型检查**：启用TypeScript的strict模式
- **禁止any类型**：除非绝对必要，否则禁止使用any
- **接口优先**：使用interface定义对象结构，type用于联合类型
- **泛型约束**：合理使用泛型约束提高类型安全性

```typescript
// 推荐的类型定义模式
interface User {
  readonly id: string;
  name: string;
  email: string;
  createdAt: Date;
}

interface UserRepository {
  findById(id: string): Promise<User | null>;
  create(userData: Omit<User, 'id' | 'createdAt'>): Promise<User>;
  update(id: string, updates: Partial<Pick<User, 'name' | 'email'>>): Promise<User>;
}
```

### 2. 现代JavaScript特性 [强制]

- **ES6+语法**：优先使用现代JavaScript语法
- **解构赋值**：合理使用解构简化代码
- **箭头函数**：在适当场景使用箭头函数
- **模板字符串**：使用模板字符串而非字符串拼接

```typescript
// 推荐的现代语法
const { name, email } = user;
const userInfo = `用户 ${name} 的邮箱是 ${email}`;
const processUsers = async (users: User[]) => {
  return Promise.all(users.map(async user => await processUser(user)));
};
```

### 3. 异步编程 [强制]

- **async/await优先**：使用async/await而非Promise链
- **错误处理**：async函数必须有适当的错误处理
- **并发控制**：合理控制并发数量，避免资源耗尽
- **取消机制**：长时间运行的异步操作应支持取消

## React/前端框架规则 [优先级：高]

### 1. 组件设计 [强制]

- **函数组件优先**：使用函数组件而非类组件
- **单一职责**：每个组件只负责一个UI功能
- **Props类型化**：所有Props必须有明确的TypeScript类型
- **状态最小化**：只在组件内部管理必要的状态

```typescript
// 推荐的组件模式
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
  className?: string;
}

export const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  onEdit, 
  onDelete, 
  className 
}) => {
  const handleEdit = useCallback(() => {
    onEdit?.(user);
  }, [user, onEdit]);

  return (
    <div className={cn("user-card", className)}>
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      {onEdit && (
        <button onClick={handleEdit}>编辑</button>
      )}
    </div>
  );
};
```

### 2. Hooks使用 [强制]

- **自定义Hooks**：将复杂逻辑抽取为自定义Hooks
- **依赖数组**：正确设置useEffect和useCallback的依赖数组
- **性能优化**：合理使用useMemo和useCallback
- **状态管理**：复杂状态使用useReducer而非多个useState

```typescript
// 推荐的自定义Hook模式
interface UseUserDataReturn {
  user: User | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export const useUserData = (userId: string): UseUserDataReturn => {
  const [state, setState] = useState<{
    user: User | null;
    loading: boolean;
    error: Error | null;
  }>({
    user: null,
    loading: true,
    error: null,
  });

  const fetchUser = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const user = await userService.fetchUser(userId);
      setState({ user, loading: false, error: null });
    } catch (error) {
      setState({ user: null, loading: false, error: error as Error });
    }
  }, [userId]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return { ...state, refetch: fetchUser };
};
```

## 代码质量与测试规则 [优先级：高]

### 1. 测试覆盖率 [强制]

- **单元测试覆盖率**：新代码的单元测试覆盖率不得低于80%
- **集成测试**：关键业务流程必须有集成测试
- **测试优先**：对于复杂逻辑，先写测试再写实现
- **测试可读性**：测试代码应该像文档一样清晰

```typescript
// 推荐的测试模式
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      // Arrange
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>'
      };
      const mockUser = { id: '1', ...userData, createdAt: new Date() };
      mockRepository.create.mockResolvedValue(mockUser);

      // Act
      const result = await userService.createUser(userData);

      // Assert
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(mockUser);
      }
      expect(mockRepository.create).toHaveBeenCalledWith(userData);
    });

    it('should handle validation errors gracefully', async () => {
      // Arrange
      const invalidUserData = { name: '', email: 'invalid-email' };

      // Act
      const result = await userService.createUser(invalidUserData);

      // Assert
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(ValidationError);
      }
    });
  });
});
```

### 2. 代码审查标准 [强制]

- **可读性检查**：代码应该能够自解释，减少注释需求
- **性能影响评估**：评估代码变更对性能的影响
- **安全性审查**：检查潜在的安全漏洞
- **向后兼容性**：确保API变更不会破坏现有功能

### 3. 静态分析 [强制]

- **ESLint规则**：严格遵循项目的ESLint配置，不得禁用规则
- **TypeScript检查**：解决所有TypeScript编译错误和警告
- **代码格式化**：使用Prettier保持代码格式一致性
- **依赖分析**：定期检查和更新依赖，移除未使用的依赖

## 文档规范 [优先级：中]

### 1. 代码注释 [重要]

- **JSDoc注释**：所有公共API必须有完整的JSDoc注释
- **复杂逻辑说明**：对于复杂的业务逻辑，添加解释性注释
- **TODO标记**：临时代码使用TODO标记，并设置处理期限
- **注释维护**：代码变更时同步更新相关注释

```typescript
/**
 * 计算用户订阅价格，包含折扣和税费
 * 
 * @param user - 用户信息，用于确定用户等级和地区
 * @param plan - 订阅计划详情
 * @param discountCode - 可选的折扣码
 * @returns 计算后的最终价格（包含税费）
 * 
 * @example
 * ```typescript
 * const price = calculateSubscriptionPrice(user, premiumPlan, 'SAVE20');
 * console.log(`最终价格: $${price}`);
 * ```
 * 
 * @throws {InvalidDiscountCodeError} 当折扣码无效时抛出
 * @throws {UnsupportedRegionError} 当用户地区不支持该计划时抛出
 */
export async function calculateSubscriptionPrice(
  user: User,
  plan: SubscriptionPlan,
  discountCode?: string
): Promise<number> {
  // 实现逻辑...
}
```

### 2. README文档 [重要]

- **项目概述**：清晰描述项目目的和主要功能
- **安装指南**：详细的环境配置和安装步骤
- **使用示例**：提供常见用例的代码示例
- **API文档**：主要API的使用说明和参数描述

### 3. 变更日志 [建议]

- **版本记录**：记录每个版本的主要变更
- **破坏性变更**：明确标注可能影响现有代码的变更
- **迁移指南**：提供版本升级的迁移指南

## 规则冲突处理机制 [优先级：最高]

### 冲突解决优先级

当不同规则之间存在冲突时，按以下优先级顺序执行：

1. **安全性规则** > 其他所有规则
2. **类型安全规则** > 代码风格规则
3. **性能关键规则** > 一般性能建议
4. **项目特定规则** > 通用规则
5. **强制规则** > 建议规则

### 特殊情况处理

- **遗留代码兼容**：在处理遗留代码时，优先保持向后兼容性
- **第三方库约束**：当第三方库要求与规则冲突时，优先满足库的要求并添加注释说明
- **性能瓶颈**：在确认的性能瓶颈处，可以适当放宽代码风格要求
- **紧急修复**：紧急bug修复时，可以暂时跳过非关键规则，但必须在后续版本中修正

### 规则例外申请

当需要违反规则时，必须：

1. **添加详细注释**：说明违反规则的原因和替代方案考虑
2. **限制影响范围**：将例外情况限制在最小范围内
3. **设置审查提醒**：添加TODO或FIXME标记，设置后续审查时间
4. **文档记录**：在项目文档中记录重要的规则例外情况

```typescript
// 规则例外示例
// TODO: 2024-12-31 - 重构此函数以符合单一职责原则
// 当前由于紧急修复用户登录问题，暂时将验证逻辑合并在此函数中
// 后续版本应将验证逻辑提取为独立的验证器类
function handleUserLogin(credentials: LoginCredentials): Promise<LoginResult> {
  // 临时合并的验证和登录逻辑
}
```

## 执行指导原则

### 代码生成时的行为准则

1. **分析需求**：在编写代码前，先分析和确认需求的完整性
2. **选择最佳方案**：从多个可能的实现方案中选择最符合规则的方案
3. **渐进式实现**：对于复杂功能，采用渐进式实现，先实现核心功能
4. **主动测试建议**：在提供代码实现的同时，主动建议相应的测试策略
5. **性能影响说明**：对可能影响性能的代码，主动说明性能考虑

### 代码审查时的关注点

1. **规则符合性检查**：系统性检查代码是否符合上述所有规则
2. **潜在问题识别**：主动识别可能的bug、性能问题和安全隐患
3. **改进建议**：提供具体的代码改进建议，而非仅指出问题
4. **最佳实践推荐**：推荐更好的实现模式和工具使用方法

### 学习指导原则

1. **解释设计决策**：说明为什么选择特定的实现方案
2. **提供学习资源**：推荐相关的学习资料和最佳实践文档
3. **举一反三**：通过示例帮助开发者理解规则背后的原理
4. **鼓励提问**：鼓励开发者对规则和实现方案提出疑问

---

**注意**：本系统提示词应根据具体项目需求进行调整。在实际使用中，请根据项目的技术栈、团队规模和业务特点对规则进行适当的定制和优化。
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown>
```
</markdown


---

