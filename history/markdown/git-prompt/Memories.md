# git-prompt 的记忆

# 用户偏好
- 用户偏好使用中文进行交流。
- 用户偏好在UI中使用英文术语表达统计数据，如Star、Forks、Prompts。
- 用户偏好提示词卡片中的标签应该放置在横线的上面。
- 用户偏好在提示词卡片中显示的类别使用 src/constants/categories.ts 中定义的 label 属性值。
- 用户偏好在表单页面使用小图标而非文字'提示'来表示提示信息。
- 用户偏好在提示词详情页面默认高亮显示变量而非隐藏，仅在用户填写时动态替换变量内容。
- 用户偏好新功能的界面设计与现有项目保持一致，并希望先用HTML原型确认设计再进行实际代码改动。
- 用户偏好在项目中集成大模型调用能力时，界面设计要与现有项目保持一致，不影响现有功能逻辑，模型调用要足够通用化，对应的Prompt可以常量化并支持变量。
- 用户偏好在PromptDetail.tsx页面中'预览'和'使用AI'两个选项卡的展示效果保持一致。

# 用户需求
- 用户希望fork的内容默认为私有，公开分享只能从个人主页进行。
- 用户希望移除'公开分享'选项，表单元素应该不可编辑。
- 用户希望在个人主页(profile)页面调整统计信息，显示用户获得的Star数量、提示词被Fork次数和拥有的提示词数量，并将现有统计数据移至'全部提示词'后面。
- 用户希望在Profile页面中将提示词总数、公开提示词数量等统计数据直接显示在标签名称后面，而不是单独的卡片。
- 用户希望卡片设计移除实际Prompt内容但保留其他元素，并结合参考图片的元素进行合理排版。
- 用户希望个人主页和首页的提示词卡片中的description严格只显示一行，溢出内容省略。（当前的line-clamp-1实现可能在某些情况下仍显示两行）
- 用户希望将分类数据统一到src/constants/categories.ts中，要求添加icon字段(根据分类语义补齐)，并且count数据应来自Prompts表的分组统计。
- 首页的“Create Prompt”按钮应重定向到提示词提交页面。
- 用户不希望使用 prompt_stars 表来实现 Star 功能。
- 用户希望在创建提示词、编辑提示词和Fork提示词的表单按钮上添加交互效果。
- 用户希望移除首页提示词卡片的Fork按钮，只在详情页面保留Fork功能，且Fork计数只在Fork页面提交后才增加。
- 用户希望在个人主页的提示词卡片中，只有私有的提示词才显示编辑按钮。
- 个人主页的私有提示词支持逻辑删除而非物理删除，需要在prompts表中添加state字段(默认0，删除为1)，并规划Prompt的生命周期状态为常量值。
- 项目中不再使用shared_prompts表，应从文档中移除相关内容。
- 用户希望在提示词中支持{{variable_name}}语法定义可自定义变量，并在提示词详情页面动态解析这些变量供用户填写。
- 用户希望在项目中集成大模型调用能力，在用户头像下添加模型设置功能，支持OpenAI、Claude、OpenRouter等提供商配置，保存在localStorage中，为后续大模型调用提供基础。
- 用户希望模型设置支持'OpenAI Compatible'选项，类似于OpenAI的模式，不做实际的过滤。
- 用户对OpenAI官方API SDK中的默认max_tokens值感兴趣。