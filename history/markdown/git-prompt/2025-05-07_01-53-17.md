# 2025-05-07_01-53-17

**项目**: git-prompt

**创建时间**: 2025年05月07日 01:53

**最后交互**: 2025年05月07日 02:03

**对话ID**: 1faafd5e-ca8e-47ad-9397-471a6e9fc5ff

---

### 对话 1

> **👤 用户** (2025年05月07日 01:53)

Provide a short summary of this project

> **🤖 Augment** (2025年05月07日 01:53)

# GitPrompt/Prompt Hub Summary

This project is an AI prompt sharing platform with two names (GitPrompt in Chinese documentation, Prompt Hub in English) that allows users to create, manage, share, and discover high-quality AI prompts.

## Key Features
- Create, edit, and organize AI prompts
- Share prompts with the community or specific users
- Browse prompts by categories and tags
- Social features: star, fork, and comment on prompts
- User profiles with activity tracking
- Logical deletion of prompts (using state field)

## Tech Stack
- Frontend: React, TypeScript, Tailwind CSS, shadcn/ui
- Build Tool: Vite
- Backend: Supabase (Authentication, Database, Storage)
- State Management: TanStack Query
- Routing: React Router

The platform is designed to be a central repository for effective AI prompts, helping users improve their interactions with AI tools like ChatGPT and Claude while building a collaborative community around prompt engineering.


---

### 对话 2

> **👤 用户** (2025年05月07日 02:03)

现在你能读取 supabase 中有多少张表吗？

---

