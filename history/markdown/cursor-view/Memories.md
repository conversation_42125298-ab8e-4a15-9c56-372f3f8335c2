# cursor-view 的记忆

# General Preferences
- User prefers responses in Chinese language.
- 用户偏好数据流向图有强对比度的背景和文字。
- 用户偏好在项目根目录创建augment文件夹来实现新功能。
- 用户偏好将Augment Code历史记录数据按项目名称分别存储到不同文件中，同时保留生成汇总文件的选项。
- 用户偏好使用JSON格式的结构（带有role和content字段）来区分用户和助手的消息。
- 用户偏好将聊天历史数据以Markdown格式展示，需要提供适当的模板组织内容。
- 用户对Augment聊天历史导出为Markdown的组织逻辑表示疑问，认为当前格式可能缺少逻辑性。
- 用户偏好时间显示格式为'2025年5月22日 12:54'，用户和Augment前面各有一个图标，连续的AI响应不需要重复显示'Augment AI'和时间部分信息。
- 用户询问了Markdown文件命名规则，表明对文档命名约定有兴趣。
- 用户偏好将导出文件存储在项目根目录下的history文件夹中，并根据format参数在history下创建markdown或json子文件夹。
- 用户希望在导出 md 格式时，如果工作区中存在非空的 Augment-Memories 文件，则将其内容输出到项目目录下的 Memories.md 文件中。
- 用户偏好在导出Markdown格式时生成一个index.md索引文件，以表格形式列出所有聊天文件。
- 用户偏好在数据库表设计中不使用外键约束。
- 用户偏好简化数据库表结构，只使用3张表（projects, conversations, messages），将workspace_files信息存储在messages表的JSON字段中而不是单独的表。
- User prefers adding database export modes to data extraction scripts, specifically wanting a 'db' mode that imports data directly into MySQL tables using the three-table structure (projects, conversations, messages).
- 用户希望了解Markdown导出和数据库导出在处理Assistant消息时的差异，并考虑在数据库导出中也实现Assistant消息的整合功能。

# Cursor Application on macOS
- User's Cursor application on macOS doesn't have a cursor.cursor directory under /Users/<USER>/Library/Application Support/Cursor/User/globalStorage.
- Cursor on macOS stores chat data in /Users/<USER>/Library/Application Support/Cursor/User/globalStorage/state.vscdb which contains bubbleId entries in the cursorDiskKV table.
- User's Cursor application stores chat history in /Users/<USER>/Library/Application Support/Cursor/User/globalStorage/state.vscdb database which contains bubbleId entries in the cursorDiskKV table.
- Cursor应用中的project信息是从工作区映射(workspace mapping)获取的，即使消息存储在全局数据库中，也会关联到特定工作区。

# Cursor's state.vscdb database
- state.vscdb is a SQLite database file used by Cursor to store application state including chat history.
- Cursor's state.vscdb database contains two tables (cursorDiskKV and ItemTable) that store different types of application data including chat history. The tables have identical schemas: TEXT keys that are UNIQUE ON CONFLICT REPLACE and BLOB values.

# VS Code Extension Storage
- VS Code extension login information is typically stored in the workspaceStorage directory under extension-specific folders.

# Refactoring extract_cursor_chat.py
- User wants to refactor extract_cursor_chat.py to create a modular system supporting multiple AI coding assistants including Augment Code, Cline, Roocode, and Windsurf.
- User prefers a phased approach to refactoring with detailed documentation (refactoring_plan.md) that should be created and shared before actual code changes begin.
- User prefers to create a new 'backend' code directory during refactoring instead of directly modifying existing code.
- The /api/chats and /api/chat/<session_id> API endpoints need to maintain the same response structure and data format as the original system. The new system's API return data must be completely identical to the original system, with no duplicate data. Any issues should be resolved by comparing the return data of the two systems.

# Augment Code Extension Data Extraction
- 用户希望通过Python脚本提取Augment Code扩展的历史记录数据，包括项目名称、路径、workspace_id和会话信息。
- 用户偏好将Augment Code历史记录数据按项目名称分别存储到不同文件中，同时保留生成汇总文件的选项。
- 在Augment聊天数据处理中，只需判断request_message不为空来识别新请求，这也意味着上一次请求结束。