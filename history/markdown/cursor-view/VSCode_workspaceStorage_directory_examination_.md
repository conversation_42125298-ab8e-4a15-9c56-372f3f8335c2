# VSCode workspaceStorage directory examination


**项目**: cursor-view

**创建时间**: 2025年05月18日 11:14

**最后交互**: 2025年05月18日 12:28

**对话ID**: 83093172-3761-4f3b-b77e-24d26a9a482c

---

### 对话 1

> **👤 用户** (2025年05月18日 11:15)

/Users/<USER>/Library/Application Support/Code/User/workspaceStorage

查看这个路径下面的 文件，为我罗列出路径的各个文件是干什么的 ？

> **🤖 Augment** (2025年05月18日 11:15)

I'll help you examine the files in the specified directory and explain their purpose. Let me check what's in that directory first.


Now let me check what's inside one of these directories to understand their structure:


Let me check the content of the workspace.json file to understand what it contains:


Let me also check what's inside one of the extension directories:


Let me also check the structure of the state.vscdb file to understand what it contains:


Based on my examination, here's an explanation of the files and directories in the `/Users/<USER>/Library/Application Support/Code/User/workspaceStorage` path:

## 文件和目录的用途解释

1. **各个哈希值命名的目录** (如 `95fafbee5d3c1c767137ae58947a5dd9`):
   - 这些是VS Code为每个工作区创建的唯一存储目录
   - 每个目录对应一个你曾经打开过的工作区或项目
   - 哈希值是基于工作区路径生成的唯一标识符

2. **workspace.json**:
   - 包含工作区的基本信息，主要是工作区的文件路径
   - 例如: `{"folder": "file:///Users/<USER>/workspace/codeworkspace/openai-cookbook"}`
   - 这个文件告诉VS Code这个存储目录对应哪个工作区

3. **state.vscdb**:
   - SQLite数据库文件，存储工作区的状态信息
   - 包含编辑器状态、打开的文件、光标位置、折叠状态等信息
   - VS Code使用这个数据库来记住你上次离开工作区时的状态

4. **state.vscdb.backup**:
   - state.vscdb文件的备份，用于在数据库损坏时恢复

5. **扩展特定目录** (如 `Augment.vscode-augment`):
   - 每个安装的VS Code扩展可以在工作区存储中保存自己的数据
   - 目录名通常是扩展的ID
   - 包含扩展在特定工作区的设置和状态

6. **扩展数据子目录** (如 `augment-global-state`, `augment-user-assets`):
   - 扩展用来存储不同类型数据的子目录
   - 例如，Augment扩展存储全局状态、用户资产等

7. **ext-dev**:
   - 用于扩展开发的特殊目录
   - 当你在开发VS Code扩展时使用

这个目录结构的主要目的是让VS Code能够:
1. 为每个工作区保存独立的状态
2. 在你重新打开项目时恢复之前的编辑器状态
3. 允许扩展为每个工作区存储特定数据
4. 在不同设备间同步工作区状态(如果启用了设置同步)

每个哈希命名的目录都是独立的，对应你曾经打开过的一个项目或工作区，这样VS Code可以为每个项目维护单独的状态和设置。


---

### 对话 2

> **👤 用户** (2025年05月18日 11:41)

Vscode 插件的登录信息一般保存在哪？

> **🤖 Augment** (2025年05月18日 11:41)

VS Code插件的登录信息通常保存在几个不同的位置，具体取决于插件的设计方式。让我为您详细介绍这些位置：

## VS Code插件登录信息的存储位置

1. **全局存储目录**:
   - 路径: `/Users/<USER>/Library/Application Support/Code/User/globalStorage/[扩展ID]`
   - 用途: 存储与工作区无关的全局扩展数据，包括登录令牌、认证信息等
   - 这是大多数需要登录的插件存储凭证的首选位置

2. **密钥链/Keychain**:
   - macOS上，许多插件使用系统的Keychain服务来安全存储敏感凭证
   - 这些凭证不会直接存储在文件系统中，而是通过系统的安全API访问
   - 可以在"钥匙串访问"应用程序中查看这些凭证

3. **工作区存储**:
   - 路径: `/Users/<USER>/Library/Application Support/Code/User/workspaceStorage/[工作区ID]/[扩展ID]`
   - 用途: 如果插件需要为特定工作区存储登录信息，会使用这个位置
   - 我们之前看到的`Augment.vscode-augment`目录就是一个例子

4. **扩展配置**:
   - 路径: `/Users/<USER>/Library/Application Support/Code/User/settings.json`
   - 一些插件会在VS Code的用户设置中存储加密或编码后的令牌
   - 这些通常以插件ID为前缀，如`"github.token": "xxx"`

5. **自定义存储位置**:
   - 一些插件可能会在用户目录下创建自己的配置文件
   - 例如`~/.config/[插件名]`或`~/.[插件名]`

## 查看特定插件的登录信息

如果您想查看特定插件的登录信息，可以尝试以下方法：

1. 检查全局存储目录:

```bash
ls -la "/Users/<USER>/Library/Application Support/Code/User/globalStorage"
```

2. 在macOS的钥匙串中查找:
   - 打开"钥匙串访问"应用程序
   - 在搜索框中输入插件名称或相关服务名称

3. 检查VS Code的设置:
   - 在VS Code中打开设置(Cmd+,)
   - 搜索插件ID或相关关键词

如果您想了解特定插件的登录信息存储位置，可以告诉我具体是哪个插件，我可以提供更具针对性的指导。


---

### 对话 3

> **👤 用户** (2025年05月18日 12:25)

workspace.json 是项目的路径，请你帮我列举出所有的 项目文件夹以及映射 

> **🤖 Augment** (2025年05月18日 12:25)

我将帮您列举出所有的项目文件夹及其映射关系。我需要查看多个 workspace.json 文件来获取这些信息。

## 详细计划
1. 创建一个脚本来遍历 workspaceStorage 目录下的所有子目录
2. 读取每个子目录中的 workspace.json 文件
3. 提取并整理项目路径与哈希值的映射关系

让我执行这个计划：


现在让我运行这个脚本来获取所有工作区映射：


现在我们已经获取了所有的工作区映射关系。让我为您整理一下这些信息，按照项目类型进行分类：

## VS Code 工作区映射关系

### 按项目类型分类

#### AI 相关项目
- **AI 工作区** (`/Users/<USER>/workspace/aiworkspace/`):
  - AI_Automated_Research_Workflow, ChatDev, DeepClaude, FunClip, LLMs-from-scratch
  - Long_article_generator, MediaCrawler, MetaGPT, MoneyPrinterTurbo, Thinking-Claude
  - Topic_Writer_Flow, ZSXQ-Spider, aide, bolt.new, claude-dev, claude-engineer
  - cline, cofounder, gpt-crawler, lobe-chat, mcp-client-langgraph, poe2gpt
  - refly, twitter-web-exporter, xhs, youku-chatbot

#### 代码开发项目
- **代码工作区** (`/Users/<USER>/workspace/codeworkspace/`):
  - DevDocs, MoneyPrinterTurbo, Roo-Cline, Tutorial-Codebase-Knowledge
  - ai-assisted-development-guide, alps-writer, augment-swebench-agent
  - awesome-ai-system-prompts, awesome-cursor-rules-mdc, awesome-cursorrules
  - claude-task-master, cline, cofounder, cursor-custom-agents-rules-generator
  - cursor-memory-bank, cursor-view, n8n, neurapress, onefilellm
  - openai-cookbook, qiaomu-blog3, repomix, rule-matcher-extension
  - rulebook-ai, rules_template, system-prompts-and-models-of-ai-tools

#### 前端项目
- **前端工作区** (`/Users/<USER>/workspace/frontworkspace/`):
  - dynamic-card-generator, markdown-to-image, pagen-ai-landing-page-template
  - sorafm, story-generate, story-generate-v1, url-to-ui-dashbord

#### 插件开发
- **插件工作区** (`/Users/<USER>/workspace/plugin/`, `/Users/<USER>/workspace/vscodeplugin/`):
  - RepoScan, article2card, claude-sync, douyin_plugin, github_repo_scan
  - redbook-post-auto, my-first-extension

#### Python 项目
- **Python 工作区** (`/Users/<USER>/workspace/pythonworkspace/`):
  - MediaCrawler, MediaCrawlerV2, Python-100-Days, dify_docs_scraper
  - douyin-scraper-extension

#### 其他专业项目
- **302 工作区** (`/Users/<USER>/workspace/302workspace/`):
  - 302_e_commerce_copywriting_assistant, 302_prompt_generator
  - 302_video_creation_hub, 302_video_summary

- **推荐系统工作区** (`/Users/<USER>/workspace/recworkspace/`):
  - tpp_dag_config, ykrec-biz

- **搜索工作区** (`/Users/<USER>/workspace/searchworkspace/`):
  - LangChainPro

- **视频工作区** (`/Users/<USER>/workspace/videoworkspace/`):
  - story-flicks

### 特别注意

1. **重复项目**:
   - 有些项目在多个工作区中出现，如 `MoneyPrinterTurbo` 同时存在于 aiworkspace 和 codeworkspace
   - `lobe-chat`, `poe2gpt` 等项目有多个哈希值映射到相同路径

2. **Cursor 相关项目**:
   - cursor-view (当前打开的项目)
   - cursor-memory-bank
   - cursor-custom-agents-rules-generator
   - CursorPlus

这些映射关系展示了您的工作区组织结构，每个哈希值目录对应一个特定的项目路径。VS Code 使用这些映射来记住每个项目的状态和设置。


---

### 对话 4

> **👤 用户** (2025年05月18日 12:27)

只列举包含这个文件夹  Augment.vscode-augment  的

> **🤖 Augment** (2025年05月18日 12:27)

我将帮您查找所有包含 `Augment.vscode-augment` 文件夹的工作区目录。让我创建一个脚本来专门检查这个条件：


现在让我运行这个脚本来查找所有包含 `Augment.vscode-augment` 目录的工作区：


让我们进一步检查 Augment.vscode-augment 目录中的 Augment-Memories 文件，看看它包含什么内容：


现在让我们检查一下 augment-global-state 目录的内容：


让我们看看 augment-user-assets 目录的内容：


最后，让我们看看哈希命名的目录内容：


## Augment.vscode-augment 目录详细分析

根据我们的查找结果，以下是包含 Augment.vscode-augment 目录的工作区映射及其内容分析：

### 总体情况
- 共找到 **39 个工作区** 包含 Augment.vscode-augment 目录
- 这些工作区主要集中在 codeworkspace 目录下（25个）
- 其他工作区分布在 aiworkspace、aliyunworkspace、cursorworkspace、lovableworkspace、mcpworkspace 和 promptworkspace 等目录

### Augment.vscode-augment 目录结构
每个工作区的 Augment.vscode-augment 目录通常包含以下内容：

1. **Augment-Memories 文件**
   - 包含与工作区相关的记忆信息
   - 例如在 openai-cookbook 工作区中记录了"用户环境存在权限问题，可能需要 sudo 进行项目创建和文件操作"

2. **augment-global-state 目录**
   - 包含全局状态信息
   - 主要文件：
     - fuzzyFsFilesIndex.json：文件索引
     - fuzzyFsFoldersIndex.json：文件夹索引
     - recentlyOpenedFiles.json：最近打开的文件记录

3. **augment-user-assets 目录**
   - 包含用户资产信息
   - 子目录：
     - agent-edits：代理编辑记录
     - checkpoint-documents：检查点文档

4. **哈希命名的目录**（如 e7debbcc7bf4ac00ada353e0324600c8b41e1445f716fca57056d6c8f7ddf37d）
   - 包含特定于项目的缓存和状态信息
   - 主要文件：
     - mtime-cache.json：文件修改时间缓存

### 特别注意的工作区

1. **cursor-view**（当前打开的项目）
   - 哈希值：7c4fd3a316386f0b04d40a82d1a9a718
   - 路径：/Users/<USER>/workspace/codeworkspace/cursor-view
   - 包含完整的 Augment 扩展数据结构

2. **openai-cookbook**
   - 哈希值：95fafbee5d3c1c767137ae58947a5dd9
   - 路径：/Users/<USER>/workspace/codeworkspace/openai-cookbook
   - 我们详细检查了这个工作区的 Augment 扩展数据

3. **多个 Cursor 相关项目**
   - cursor-custom-agents-rules-generator
   - cursor-memory-bank
   - CursorPlus
   - vscode-cursor-rules

### 登录信息存储

Augment 扩展的登录信息可能存储在以下位置：

1. **哈希命名的目录**中的配置文件
   - 这些目录可能包含特定于用户的认证信息

2. **augment-global-state** 目录
   - 可能在全局状态中保存登录令牌或会话信息

这些目录结构表明 Augment 扩展为每个工作区维护独立的状态、缓存和用户数据，同时也保存了一些全局配置信息。登录凭证很可能存储在这些目录中的某个配置文件中，或者通过系统的 Keychain 服务进行管理。


---

