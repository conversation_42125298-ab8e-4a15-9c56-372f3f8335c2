# 2025-04-11_09-56-32

**项目**: rule-matcher-extension

**创建时间**: 2025年04月11日 09:56

**最后交互**: 2025年04月11日 09:56

**对话ID**: adb18d30-616a-45d3-b76c-1ec5d17a8b4a

---

### 对话 1

> **👤 用户**

Provide a short summary of this project

> **🤖 Augment**

# Rule Matcher Extension Summary

This is a VSCode extension that analyzes projects and recommends appropriate coding rules. Key features include:

- Project analysis: Automatically detects dependencies, code structure, and tech stack
- Rule matching: Recommends coding rules based on project characteristics
- AI enhancement: Uses AI models (OpenAI, Anthropic, or compatible APIs) for smarter rule matching
- GitHub integration: Supports loading projects and rules from repositories
- Rule management: Apply, remove, and view details of rules
- Custom rule generation: Creates project-specific rules using AI

The extension works by analyzing project files, identifying technologies used, matching with appropriate rules from a repository, and allowing users to apply these rules to improve code quality and development efficiency.


---

