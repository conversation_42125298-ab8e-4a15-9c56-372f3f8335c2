# AI-Product-Development-Toolkit 的记忆

# User Preferences
- User prefers responses in Chinese language.
- 用户偏好使用Next.js和Tailwind CSS作为技术栈，并希望在根目录创建docs文件夹存放文档。
- 用户希望基于项目模板创建UI设计规范和v0.dev提示词，并将文档保存到docs目录中。
- 用户偏好在前端界面中实现引导式问答流程，清晰传达AI生成内容是草稿的概念，使用结构化格式展示内容，并通过界面设计强调模板的实用性。
- 用户偏好将用户交互体验优化方案融入到docs目录下的文档中，包括将引导式问答流程、AI生成内容草稿性质传达、结构化格式UI设计等内容分别添加到ui-design-specification.md和v0-design-prompt.md的相关部分。
- 用户偏好使用HTML和Tailwind CSS创建UI/UX原型，要求使用Tailwind CSS CDN、Unsplash图片和Lucide图标，将HTML文件存放在项目根目录的html文件夹中，并优先实现仪表板、项目工作区和文档编辑器等功能模块。
- 用户希望创建完整的操作指南文档，使普通用户能够轻松掌握项目的使用流程。
- 用户偏好从提示词角度提供使用指南，而非关注HTML实现细节。
- 用户偏好使用命令管道形式快速输出内容到文件。

# OpenAI Cookbook
- 用户对OpenAI Cookbook感兴趣，希望了解其定义和用途。
- 用户对OpenAI Cookbook网站如何从GitHub源文件渲染内容感兴趣，并希望使用firecrawl工具进行分析。