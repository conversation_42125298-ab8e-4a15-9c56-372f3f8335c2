# 2025-05-15_08-29-20

**项目**: AI-Product-Development-Toolkit

**创建时间**: 2025年05月15日 08:29

**最后交互**: 2025年05月15日 10:23

**对话ID**: cfe7d944-d4b2-4137-927e-3c9bdd15727a

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月15日 08:29)

基于对当前项目的理解，给出我一个完整的操作指南 , 目的是让普通的用户按照该指南就可以完全掌握该项目的 使用流程

> **🤖 Augment** (2025年05月15日 08:29)

我需要先了解当前项目的结构和功能，以便为您创建一个完整的操作指南。让我先检索项目的相关信息。


让我继续获取更多关于项目的信息，特别是关于用户界面和使用流程的部分。


让我继续获取更多关于项目的信息，特别是关于项目的使用流程和HTML原型。


现在我已经对项目有了更全面的了解，让我为您创建一个完整的操作指南。


# AI产品开发工具包完整操作指南

## 目录

1. [项目介绍](#1-项目介绍)
2. [开始使用](#2-开始使用)
3. [创建新项目](#3-创建新项目)
4. [产品开发工作流程](#4-产品开发工作流程)
5. [详细阶段指南](#5-详细阶段指南)
6. [文档编辑器使用](#6-文档编辑器使用)
7. [项目管理](#7-项目管理)
8. [高级功能](#8-高级功能)
9. [常见问题解答](#9-常见问题解答)
10. [资源和支持](#10-资源和支持)

## 1. 项目介绍

### 什么是AI产品开发工具包？

AI产品开发工具包是一个综合性平台，旨在帮助用户从初始想法到最终MVP（最小可行产品）的整个产品开发过程。该工具包提供了一系列结构化模板和AI辅助功能，指导用户完成产品需求文档(PRD)、用户体验(UX)规范、MVP概念定义、开发计划和测试计划等各个阶段。

### 核心价值和优势

- **结构化方法论**：提供从想法到MVP的完整开发流程
- **AI辅助创作**：利用AI技术帮助用户生成和完善各类文档
- **引导式问答**：通过分步骤问答引导用户思考产品的各个方面
- **可视化设计集成**：与v0.dev等工具集成，快速生成前端界面代码
- **模板库**：提供多种产品类型的专业模板，加速开发过程

### 适用场景和用户群体

- 产品经理：规划和定义新产品
- 创业者：将创业想法转化为具体产品
- 设计师：创建用户体验规范和界面设计
- 开发团队：规划MVP开发和测试流程
- 学生和爱好者：学习产品开发的专业流程

## 2. 开始使用

### 访问平台

1. 打开浏览器，访问AI产品开发工具包网站
2. 在首页点击"开始使用"或"注册"按钮

### 创建账户/登录

1. 填写注册表单（用户名、邮箱、密码）
2. 验证邮箱（点击发送到您邮箱的验证链接）
3. 使用注册的邮箱和密码登录系统

### 仪表板界面概览

登录后，您将进入仪表板界面，该界面包含以下主要部分：

- **顶部导航栏**：包含品牌标识、用户菜单和全局导航选项
- **欢迎区域**：显示个性化欢迎信息和快速操作按钮
- **项目网格**：以卡片形式展示您的所有项目，包括进度和状态
- **创建项目卡片**：用于创建新项目的特殊卡片
- **活动时间线**：显示最近的项目活动和更新
- **快速访问模板**：常用模板的快捷入口

## 3. 创建新项目

### 从头创建项目

1. 在仪表板页面，点击"创建新项目"按钮或创建项目卡片
2. 在弹出的表单中填写项目基本信息：
   - 项目名称（必填）
   - 项目描述（简要说明项目目标和范围）
   - 项目类型（选择最接近的类别）
3. 点击"创建项目"按钮确认

### 使用模板创建项目

1. 在仪表板页面，点击"模板"导航选项
2. 浏览可用的项目模板（如智能客服助手、数据可视化平台等）
3. 点击感兴趣的模板卡片查看详情
4. 点击"使用此模板"按钮
5. 填写项目基本信息，可根据需要修改模板预填的内容
6. 点击"创建项目"按钮确认

### 项目基本信息设置

创建项目后，您可以进一步完善项目设置：

1. 进入项目工作区
2. 点击项目信息区域的"设置"图标
3. 在设置面板中，您可以：
   - 更新项目名称和描述
   - 设置项目图标和颜色
   - 调整项目可见性和访问权限（如适用）
   - 配置项目相关的API设置（如适用）
4. 完成设置后点击"保存"按钮

## 4. 产品开发工作流程

### 工作流程概览

AI产品开发工具包提供了一个结构化的产品开发工作流程，包含以下主要阶段：

1. **PRD创建**：定义产品需求文档，明确产品愿景、目标用户和功能需求
2. **UX规范定义**：创建用户体验规范，包括信息架构、用户流程和界面设计
3. **MVP概念定义**：确定最小可行产品的范围、假设和核心功能
4. **MVP开发计划**：制定详细的开发计划，包括技术栈、时间线和资源需求
5. **测试计划**：创建测试策略和计划，确保产品质量
6. **v0.dev设计**：生成前端界面代码，快速实现视觉设计

### 各阶段之间的关系和顺序

- 工作流程设计为顺序进行，每个阶段的输出作为下一阶段的输入
- 在项目工作区的工作流可视化图中，已完成的阶段显示为绿色，当前阶段显示为蓝色，未开始的阶段显示为灰色
- 您可以在不同阶段之间自由切换，但建议按照推荐顺序完成各个阶段
- 每个阶段完成后，系统会自动将相关信息传递到下一阶段，减少重复工作

### 导航工作流程

1. 在项目工作区页面，查看顶部的工作流可视化图
2. 点击任意阶段卡片进入该阶段
3. 使用阶段导航栏在各阶段之间切换
4. 通过面包屑导航返回上一级页面

## 5. 详细阶段指南

### PRD创建阶段

PRD（产品需求文档）是产品开发的基础，定义了产品的目标、用户和功能需求。

**操作步骤**：

1. 在工作流可视化图中点击"PRD阶段"卡片
2. 选择"开始引导式PRD创建"按钮
3. 系统将引导您通过问答方式完成PRD：
   - 回答关于产品概述的问题
   - 定义目标用户和用户需求
   - 描述核心功能和特性
   - 确定产品范围和限制
   - 设置成功指标和验收标准
4. 在问答过程中，您可以：
   - 点击"下一步"继续
   - 点击"上一步"返回修改
   - 查看右侧的文档预览，了解当前进度
   - 点击"保存并退出"暂时保存进度
5. 完成所有问题后，系统会生成完整的PRD文档
6. 在预览页面检查文档，可以点击"编辑"进行修改
7. 确认无误后点击"完成并进入下一阶段"

**引导式问答使用技巧**：

- 每个问题都聚焦于PRD的特定部分，请尽可能详细回答
- 系统会在顶部显示已完成步骤的简要总结，帮助您保持上下文
- 进度指示器显示当前完成度，帮助您了解剩余工作量
- 如果不确定如何回答，可以点击问题旁的"提示"图标获取建议
- AI生成的内容会有明显的视觉标记（浅色背景、虚线边框），表明这是草稿内容

### UX规范定义阶段

UX规范将PRD转化为具体的用户体验设计，包括信息架构、用户流程和界面设计。

**操作步骤**：

1. 在工作流可视化图中点击"UX阶段"卡片
2. 系统会自动加载PRD中的相关信息
3. 选择"开始UX规范定义"按钮
4. 通过引导式问答完成以下部分：
   - 信息架构（页面层次、内容组织、导航结构）
   - 核心用户流程（主要用户旅程、决策点、错误状态）
   - 视图规范（关键屏幕布局、组件层次、状态转换）
   - 交互模式（输入行为、反馈机制、过渡动画）
   - 设计系统集成（组件使用、布局网格、间距原则）
5. 完成问答后，系统生成完整的UX规范文档
6. 检查并编辑文档，确认无误后进入下一阶段

**UX规范定义技巧**：

- 使用文本布局描述功能可视化界面结构
- 利用组件层次描述功能定义UI组件的嵌套关系
- 参考系统提供的设计模式库，选择适合的交互模式
- 注意响应式设计考虑，确保在不同设备上的良好体验

### MVP概念定义阶段

MVP概念定义阶段帮助您确定最小可行产品的范围、假设和核心功能。

**操作步骤**：

1. 在工作流可视化图中点击"MVP概念阶段"卡片
2. 系统会自动加载PRD和UX规范中的相关信息
3. 选择"开始MVP概念定义"按钮
4. 通过引导式问答完成以下部分：
   - MVP目标和假设
   - 核心功能和特性
   - 成功指标和验证方法
   - 范围限制和优先级
5. 完成问答后，系统生成MVP概念文档
6. 检查并编辑文档，确认无误后进入下一阶段

### MVP开发计划阶段

MVP开发计划阶段帮助您制定详细的开发计划，包括技术栈、时间线和资源需求。

**操作步骤**：

1. 在工作流可视化图中点击"MVP计划阶段"卡片
2. 系统会自动加载MVP概念中的相关信息
3. 选择"开始MVP开发计划"按钮
4. 通过引导式问答完成以下部分：
   - 技术栈选择
   - 开发阶段和里程碑
   - 资源需求和分配
   - 风险评估和缓解策略
   - 时间线和交付计划
5. 完成问答后，系统生成MVP开发计划文档
6. 检查并编辑文档，确认无误后进入下一阶段

### 测试计划阶段

测试计划阶段帮助您创建测试策略和计划，确保产品质量。

**操作步骤**：

1. 在工作流可视化图中点击"测试计划阶段"卡片
2. 系统会自动加载MVP开发计划中的相关信息
3. 选择"开始测试计划制定"按钮
4. 通过引导式问答完成以下部分：
   - 测试目标和范围
   - 测试类型和方法
   - 测试环境和工具
   - 测试用例和场景
   - 缺陷管理和报告
5. 完成问答后，系统生成测试计划文档
6. 检查并编辑文档，确认无误后进入下一阶段

### v0.dev设计阶段

v0.dev设计阶段帮助您生成前端界面代码，快速实现视觉设计。

**操作步骤**：

1. 在工作流可视化图中点击"v0-Design阶段"卡片
2. 系统会自动加载UX规范和MVP范围中的相关信息
3. 选择"开始v0.dev提示生成"按钮
4. 通过引导式问答完成以下部分：
   - 设计风格和主题
   - 布局和组件需求
   - 响应式设计考虑
   - 交互和动画需求
5. 系统生成结构化的v0.dev提示词
6. 在编辑器中查看和编辑提示词
7. 点击"复制提示词"按钮，或直接使用"打开v0.dev"按钮
8. 在v0.dev中使用提示词生成前端代码
9. 将生成的代码集成到您的项目中

## 6. 文档编辑器使用

### 编辑器界面布局

文档编辑器界面分为以下几个主要部分：

- **顶部工具栏**：包含文档标题、保存按钮和其他操作选项
- **左侧编辑区域**：用于编辑文档内容
- **右侧预览区域**：实时显示编辑内容的预览效果
- **底部AI辅助面板**：提供智能建议和辅助功能

### 基本编辑功能

1. **文本编辑**：
   - 支持Markdown语法
   - 提供格式化工具栏（标题、列表、链接等）
   - 支持快捷键操作

2. **内容组织**：
   - 使用标题层级（#、##、###）组织内容
   - 使用列表（有序和无序）创建结构化内容
   - 插入表格、代码块和引用

3. **文件操作**：
   - 自动保存功能（每30秒自动保存）
   - 手动保存（点击工具栏的保存按钮）
   - 导出功能（支持Markdown、PDF等格式）

### AI辅助功能

1. **内容生成**：
   - 点击AI辅助面板中的"生成内容"按钮
   - 输入提示或选择预设模板
   - AI会根据当前文档上下文生成相关内容

2. **内容改进**：
   - 选中文本，点击"改进"按钮
   - 选择改进类型（简化、扩展、专业化等）
   - AI会根据选择优化所选内容

3. **智能建议**：
   - 编辑过程中，AI会自动提供相关建议
   - 建议会显示在AI辅助面板中
   - 点击建议可将其应用到文档中

### AI生成内容编辑

1. **识别AI生成内容**：
   - AI生成的内容会有明显的视觉标记（浅色背景、虚线边框）
   - 内容顶部会显示"AI生成草稿"水印

2. **编辑模式切换**：
   - 点击内容区域右上角的"编辑"按钮进入编辑模式
   - 点击"查看"按钮返回查看模式

3. **内联编辑**：
   - 直接点击AI生成内容的任何部分进行编辑
   - 修改后的内容会自动标记为"已编辑"

4. **版本比较**：
   - 点击"查看原始版本"按钮比较AI生成版本与当前版本
   - 使用"接受更改"或"恢复原始"按钮管理修改

5. **批量操作**：
   - 使用"全部接受"按钮接受所有AI生成内容
   - 使用"全部拒绝"按钮拒绝所有AI生成内容
   - 使用"选择性接受"进行部分接受

## 7. 项目管理

### 查看和管理项目

1. **项目列表**：
   - 在仪表板页面查看所有项目
   - 使用筛选器按状态、类型或日期筛选项目
   - 使用搜索框查找特定项目

2. **项目详情**：
   - 点击项目卡片进入项目工作区
   - 查看项目基本信息、进度和状态
   - 访问项目各个阶段的文档

3. **项目操作**：
   - 重命名项目：点击项目名称旁的编辑图标
   - 归档项目：在项目设置中选择"归档"
   - 删除项目：在项目设置中选择"删除"（谨慎操作）

### 跟踪项目进度

1. **进度指示器**：
   - 项目卡片上显示整体完成度
   - 工作流可视化图显示各阶段状态
   - 每个阶段文档显示该阶段完成度

2. **活动时间线**：
   - 在仪表板查看最近的项目活动
   - 在项目工作区查看特定项目的活动历史
   - 活动包括创建、编辑、完成等操作

3. **状态标签**：
   - 未开始：灰色标签
   - 进行中：蓝色标签
   - 已完成：绿色标签
   - 需要注意：黄色标签

### 协作功能（如果适用）

1. **共享项目**：
   - 点击项目工作区中的"共享"按钮
   - 输入协作者的邮箱地址
   - 设置权限级别（查看、编辑、管理）

2. **评论和反馈**：
   - 在文档中选择文本，点击"添加评论"
   - 查看和回复他人的评论
   - 解决评论后点击"标记为已解决"

3. **版本历史**：
   - 查看文档的编辑历史
   - 比较不同版本的差异
   - 恢复到之前的版本

## 8. 高级功能

### 自定义模板

1. **创建自定义模板**：
   - 在完成项目后，点击"另存为模板"
   - 填写模板名称、描述和分类
   - 选择要包含的阶段和内容

2. **使用自定义模板**：
   - 创建新项目时选择"我的模板"标签
   - 选择之前创建的自定义模板
   - 根据需要修改预填内容

3. **管理模板**：
   - 在设置页面的"模板"选项卡中查看所有自定义模板
   - 编辑、删除或分享模板

### 集成外部工具

1. **v0.dev集成**：
   - 在v0-Design阶段生成提示词
   - 使用"打开v0.dev"按钮直接跳转
   - 将生成的代码导入回项目

2. **其他集成**（如适用）：
   - 版本控制系统（如GitHub）
   - 项目管理工具（如Jira、Trello）
   - 设计工具（如Figma）

### API设置（如果适用）

1. **配置API密钥**：
   - 在设置页面的"API"选项卡中添加API密钥
   - 支持多种AI服务提供商
   - 设置默认服务提供商

2. **自定义AI参数**：
   - 调整温度、最大令牌数等参数
   - 创建和保存参数预设
   - 为不同项目类型设置不同参数

## 9. 常见问题解答

### 技术问题

**问：系统支持哪些浏览器？**
答：系统支持最新版本的Chrome、Firefox、Safari和Edge浏览器。为获得最佳体验，建议使用Chrome浏览器。

**问：如何处理大型文档的性能问题？**
答：对于大型文档，可以：
- 使用分段编辑功能，只加载当前正在编辑的部分
- 减少同时打开的预览窗口数量
- 定期保存文档，避免数据丢失

**问：如何解决AI生成内容的超时问题？**
答：如果AI生成内容超时：
- 尝试减小请求的内容量
- 检查网络连接
- 稍后重试或使用"重新生成"按钮

### 使用技巧

**问：如何最有效地使用引导式问答？**
答：
- 提前准备好相关信息，避免中途查找
- 尽可能详细回答每个问题，提供具体例子
- 利用AI建议，但始终根据自己的项目需求进行调整
- 使用"保存并退出"功能，分多次完成复杂阶段

**问：如何提高AI生成内容的质量？**
答：
- 提供清晰、具体的指令
- 使用专业术语和行业词汇
- 指定目标受众和内容风格
- 对生成结果进行编辑和完善

**问：如何在不同设备间同步项目？**
答：
- 确保在所有设备上使用相同的账户登录
- 离开设备前保存所有更改
- 使用"同步"按钮手动触发同步（如适用）

### 故障排除

**问：登录失败怎么办？**
答：
- 检查用户名和密码是否正确
- 清除浏览器缓存和Cookie
- 使用"忘记密码"功能重置密码
- 联系客服支持

**问：文档无法保存怎么办？**
答：
- 检查网络连接
- 尝试使用"另存为"功能保存到新文件
- 复制文档内容到本地文本编辑器作为备份
- 刷新页面后重试

**问：AI功能无响应怎么办？**
答：
- 检查API设置是否正确
- 确认API密钥有效且未超出使用限制
- 尝试切换到备用AI服务提供商
- 暂时使用手动模式继续工作

## 10. 资源和支持

### 学习资源

- **教程视频**：访问"帮助"菜单中的"视频教程"部分
- **文档库**：查阅详细的功能说明和最佳实践
- **示例项目**：浏览示例项目，了解完整工作流程
- **模板库**：探索各种预设模板，学习专业结构

### 获取帮助

- **在线客服**：点击界面右下角的"帮助"图标
- **知识库**：搜索常见问题和解决方案
- **邮件支持**：发送问题到支持邮箱
- **社区论坛**：与其他用户交流经验和解决方案

### 社区和反馈

- **用户社区**：加入用户讨论组，分享经验和技巧
- **功能请求**：提交新功能建议和改进意见
- **Bug报告**：报告发现的问题和错误
- **用户调查**：参与定期用户调查，帮助改进产品

---

通过本操作指南，您应该能够全面掌握AI产品开发工具包的使用流程，从创建项目到最终完成MVP的整个过程。如果您有任何疑问或需要进一步帮助，请参考上述资源和支持渠道。祝您的产品开发之旅顺利！


---

### 对话 3

> **👤 用户** (2025年05月15日 08:54)

忽略 @html/  文件的内容，重新给出指导，就单纯的从提示词的角度来给出使用指南 

> **🤖 Augment** (2025年05月15日 08:54)

我理解您的需求，您希望我忽略HTML文件的内容，专注于从提示词的角度来提供AI产品开发工具包的使用指南。我将重新整理一份指南，专注于如何使用各种提示词模板来完成产品开发流程。


# AI产品开发工具包提示词使用指南

## 目录

1. [项目介绍](#1-项目介绍)
2. [提示词使用的基本原则](#2-提示词使用的基本原则)
3. [完整提示词工作流程](#3-完整提示词工作流程)
4. [详细提示词使用指南](#4-详细提示词使用指南)
5. [提示词定制和优化](#5-提示词定制和优化)
6. [常见问题和解决方案](#6-常见问题和解决方案)

## 1. 项目介绍

### 什么是AI产品开发工具包

AI产品开发工具包是一个精心设计的提示词模板集合，旨在指导您与大型语言模型（如Google Gemini、GPT-4、Claude 3）进行交互，完成从产品构思到MVP开发的整个过程。这些提示词模板采用结构化的对话方式，引导AI模型向您提出关键问题，帮助您系统性地思考和定义产品的各个方面。

### 提示词模板的价值和优势

- **结构化思考**：提供专业的产品开发框架，确保您不会遗漏关键环节
- **引导式对话**：通过问答形式引导您深入思考产品的各个方面
- **知识增强**：融合了产品管理、UX设计和开发规划的最佳实践
- **灵活适应**：可根据不同项目需求进行调整和定制
- **完整流程**：覆盖从初始构思到可视化设计和开发规划的全过程

### 适用的AI模型和平台

这些提示词模板设计用于与具有大上下文窗口的高级AI模型交互，包括但不限于：
- Google Gemini Advanced
- OpenAI GPT-4
- Anthropic Claude 3
- 其他类似能力的大型语言模型

您可以在各种AI聊天平台上使用这些提示词，如Google Gemini、ChatGPT、Claude等。为获得最佳效果，建议使用支持长对话和大文本输入的平台。

## 2. 提示词使用的基本原则

### 如何有效地与AI模型交互

1. **准备充分**：在使用提示词前，先明确您的产品想法和目标
2. **完整复制**：完整复制提示词模板，包括角色设定和指令部分
3. **替换占位符**：仔细替换所有`[示例]`或`[ <<< PASTE ... HERE >>> ]`等占位符
4. **详细回答**：对AI提出的问题给予详细、具体的回答
5. **保持对话**：将整个过程视为对话，而非一次性输入
6. **验证理解**：定期确认AI对您需求的理解是否准确
7. **迭代改进**：根据AI的回应调整您的输入，直到满意为止

### 提示词修改和调整的技巧

1. **保留核心结构**：修改时保留提示词的基本结构和指令
2. **调整专业程度**：根据您的需求调整语言的专业程度
3. **添加行业背景**：在适当位置添加您的行业特定信息
4. **扩展或简化**：根据项目复杂度扩展或简化某些部分
5. **自定义约束**：添加特定的设计约束或技术要求
6. **调整输出格式**：指定您偏好的输出格式和结构

### 处理AI回复的最佳实践

1. **批判性评估**：不要盲目接受所有AI生成的内容
2. **提取核心价值**：关注有价值的见解和结构，而非每个细节
3. **请求澄清**：当AI回复不清晰时，直接要求澄清
4. **引导修正**：指出不准确或不适合的部分，要求修正
5. **保存中间成果**：定期保存重要的对话内容和文档
6. **温度调节**：对于最终文档生成，考虑使用较低的温度设置(0.2-0.5)以获得更一致的输出

## 3. 完整提示词工作流程

### 从想法到MVP的提示词顺序

AI产品开发工具包设计为按以下顺序使用：

1. **PRD创建**：使用`PRD/Guided-PRD-Creation.md`提示词，将初始想法转化为结构化的产品需求文档
2. **UX规范定义**：使用`UX-User-Flow/Guided-UX-User-Flow.md`提示词，基于PRD创建详细的用户体验规范
3. **MVP概念定义**：使用`MVP-Concept/Guided-MVP-Concept-Definition.md`提示词，定义聚焦的MVP概念
4. **MVP开发计划**：使用`MVP/Guided-MVP.md`或`Ultra-Lean-MVP/Guided-Ultra-Lean-MVP.md`提示词，创建MVP开发计划
5. **测试计划**：使用`Testing/Guided-Test-Plan.md`提示词，基于MVP功能创建测试计划
6. **v0.dev设计提示**：使用`v0-Design/v0.dev-visual-generation-prompt-filler.md`提示词，生成用于v0.dev的视觉设计提示

### 各阶段提示词之间的关系

- 每个阶段的提示词设计为使用前一阶段的输出作为输入
- PRD是基础文档，为后续所有阶段提供核心信息
- UX规范和MVP概念共同为v0.dev设计提示提供输入
- 各阶段可以独立使用，但按顺序使用可获得最佳效果
- 您可以根据项目需求跳过某些阶段或调整顺序

### 如何在各阶段之间传递信息

1. **保存每个阶段的输出**：将AI生成的最终文档保存为单独文件
2. **复制关键部分**：在使用下一阶段提示词时，复制上一阶段的关键输出
3. **粘贴到指定位置**：将内容粘贴到下一阶段提示词中的指定占位符位置
4. **参考而非重复**：如果某些内容太长，可以提供摘要并参考完整文档
5. **保持一致性**：确保各阶段使用一致的术语和概念

## 4. 详细提示词使用指南

### PRD创建提示词

**文件位置**：`PRD/Guided-PRD-Creation.md`

**目的**：将初始产品想法转化为全面的产品需求文档(PRD)

**使用步骤**：

1. 复制`Guided-PRD-Creation.md`中的完整提示词
2. 在提示词的"TONE & CONSTRAINTS"部分，更新任何特定约束或要求
3. 将提示词粘贴到AI聊天界面
4. AI会开始一个交互式过程，询问关于您产品的问题
5. 详细回答每个问题，提供具体例子和细节
6. 在对话过程中，AI会定期检查理解并请求确认
7. 完成所有问题后，AI会生成完整的PRD文档
8. 审查文档，要求AI进行任何必要的修改
9. 保存最终PRD文档，用于下一阶段

**使用技巧**：
- 提前准备产品的核心概念和目标用户信息
- 对于复杂问题，可以要求AI提供例子或进一步解释
- 如果AI提出的问题不适用于您的产品，说明原因并请求跳过
- 定期要求AI总结已收集的信息，确保方向正确
- 对于重要部分，可以要求AI提供多个选项或方案

### UX-User-Flow提示词

**文件位置**：`UX-User-Flow/Guided-UX-User-Flow.md`

**目的**：将PRD转化为详细的用户体验规范和用户流程文档

**使用步骤**：

1. 复制`Guided-UX-User-Flow.md`中的完整提示词
2. 在`--- PRD START ---`和`--- PRD END ---`标记之间粘贴您的PRD文档
3. 更新"TONE & CONSTRAINTS"部分的任何特定设计约束
4. 将修改后的提示词粘贴到AI聊天界面
5. AI会分析PRD并开始询问关于用户体验的问题
6. 详细回答关于信息架构、用户流程、界面设计等问题
7. AI会使用文本描述帮助可视化界面布局和组件层次
8. 完成所有问题后，AI会生成UX规范文档
9. 审查文档，要求AI进行任何必要的修改
10. 保存最终UX规范文档，用于后续阶段

**使用技巧**：
- 参考成功产品的UX模式，在回答中提及这些参考
- 使用AI的文本布局描述功能来可视化界面结构
- 要求AI提供多种导航模式或交互模式的选项
- 确保考虑不同用户类型和使用场景
- 特别关注错误状态和边缘情况的处理

### MVP概念定义提示词

**文件位置**：`MVP-Concept/Guided-MVP-Concept-Definition.md`

**目的**：定义聚焦的最小可行产品(MVP)概念，包括范围、假设和核心功能

**使用步骤**：

1. 复制`Guided-MVP-Concept-Definition.md`中的完整提示词
2. 在指定位置粘贴您的PRD文档
3. 将修改后的提示词粘贴到AI聊天界面
4. AI会分析PRD并开始询问关于MVP的问题
5. 回答关于MVP目标、假设、核心功能和成功指标的问题
6. AI会帮助您确定功能优先级和范围限制
7. 完成所有问题后，AI会生成MVP概念文档
8. 审查文档，要求AI进行任何必要的修改
9. 保存最终MVP概念文档，用于后续阶段

**使用技巧**：
- 保持MVP范围精简，专注于验证核心假设的功能
- 明确区分"必须有"和"可以有"的功能
- 为每个核心功能定义明确的成功指标
- 考虑时间和资源约束，设定现实的范围
- 要求AI帮助识别潜在风险和缓解策略

### MVP/Ultra-Lean-MVP提示词

**文件位置**：`MVP/Guided-MVP.md`或`Ultra-Lean-MVP/Guided-Ultra-Lean-MVP.md`

**目的**：创建详细的MVP开发计划或精简的MVP构建规范

**选择指南**：
- 使用`Guided-MVP.md`获取更全面的开发计划，包括技术栈、时间线和资源需求
- 使用`Guided-Ultra-Lean-MVP.md`获取更精简的构建规范，专注于快速实现

**使用步骤**：

1. 根据需求选择并复制相应的提示词模板
2. 在指定位置粘贴您的PRD和MVP概念文档
3. 将修改后的提示词粘贴到AI聊天界面
4. AI会分析输入文档并开始询问关于开发计划的问题
5. 回答关于技术选择、开发阶段、资源需求等问题
6. 完成所有问题后，AI会生成MVP开发计划或构建规范
7. 审查文档，要求AI进行任何必要的修改
8. 保存最终文档，用于实际开发和后续阶段

**使用技巧**：
- 提供关于技术偏好和约束的具体信息
- 考虑现有资源和技能，设定现实的时间线
- 要求AI提供多种技术方案的优缺点比较
- 对于关键功能，要求更详细的技术实现建议
- 确保计划包含明确的里程碑和交付物

### 测试计划提示词

**文件位置**：`Testing/Guided-Test-Plan.md`

**目的**：创建全面的测试计划，确保MVP质量

**使用步骤**：

1. 复制`Guided-Test-Plan.md`中的完整提示词
2. 在指定位置粘贴您的MVP功能列表（来自MVP概念或开发计划）
3. 将修改后的提示词粘贴到AI聊天界面
4. AI会分析功能列表并开始询问关于测试的问题
5. 回答关于测试目标、范围、方法和环境的问题
6. AI会帮助您定义测试用例和验收标准
7. 完成所有问题后，AI会生成测试计划文档
8. 审查文档，要求AI进行任何必要的修改
9. 保存最终测试计划文档，用于质量保证

**使用技巧**：
- 确保测试计划覆盖所有核心功能和关键用户流程
- 考虑不同类型的测试（功能测试、性能测试、用户测试等）
- 要求AI提供针对特定功能的详细测试用例
- 定义明确的验收标准和通过/失败条件
- 考虑边缘情况和错误处理的测试

### v0.dev设计提示词

**文件位置**：`v0-Design/v0.dev-visual-generation-prompt-filler.md`

**目的**：生成用于v0.dev工具的视觉设计提示，将UX规范转化为前端代码

**使用步骤**：

1. 复制`v0.dev-visual-generation-prompt-filler.md`中的完整提示词
2. 在指定位置粘贴您的UX规范和MVP范围文档
3. 将修改后的提示词粘贴到AI聊天界面
4. AI会分析输入文档并开始询问关于视觉设计的问题
5. 回答关于设计风格、布局、组件和交互的问题
6. AI会帮助您定义v0.dev提示的各个部分
7. 完成所有问题后，AI会生成结构化的v0.dev提示
8. 审查提示，要求AI进行任何必要的修改
9. 复制最终提示，用于v0.dev工具生成前端代码

**使用技巧**：
- 提供明确的设计偏好和参考示例
- 指定颜色方案、字体和组件风格
- 确保提示包含所有必要的页面和组件
- 要求AI优化提示以获得最佳v0.dev结果
- 考虑分解复杂界面为多个v0.dev提示

## 5. 提示词定制和优化

### 如何根据项目需求调整提示词

1. **分析项目特点**：确定项目的独特需求和约束
2. **识别关键部分**：找出提示词中需要调整的特定部分
3. **保留核心结构**：修改时保持提示词的基本框架和指令
4. **添加行业上下文**：在开头部分添加行业背景和特定术语
5. **调整问题深度**：根据项目复杂度增加或减少问题深度
6. **更新约束条件**：在约束部分添加项目特定的限制和要求

**示例调整**：
```
原始提示词部分：
"考虑一般用户如何与产品交互..."

调整后：
"考虑医疗专业人员如何在紧急情况下与产品交互，需要考虑医院环境的特殊约束和医疗法规要求..."
```

### 提示词模板的扩展和修改

1. **添加新部分**：根据项目需要添加新的问题或部分
2. **整合多个模板**：将不同模板的元素组合为自定义工作流
3. **简化复杂部分**：对于简单项目，删除过于详细的部分
4. **扩展关键领域**：对项目关键方面添加更深入的问题
5. **调整输出格式**：修改AI应生成的文档结构和格式
6. **添加示例**：在关键问题后添加示例回答，引导AI理解期望

**扩展示例**：
```
在MVP概念提示词中添加：

"## 市场验证策略
请提出3-5种方法来在开发前验证这个MVP概念的市场需求：
1. [示例：用户访谈方法]
2. [示例：竞品分析方法]
3. [示例：最小可行测试方法]"
```

### 创建自定义提示词模板

1. **分析现有模板**：理解现有模板的结构和逻辑
2. **确定核心元素**：识别所有提示词共有的关键元素
3. **设计新结构**：为特定需求创建新的问题流程
4. **保持对话性**：维持引导式问答的交互模式
5. **添加检查点**：包含理解确认和方向验证的环节
6. **测试和迭代**：使用AI测试新模板并根据结果改进

**自定义模板框架**：
```
## ROLE:
[定义AI应扮演的专家角色]

## GOAL:
[明确这个提示词的目标和预期输出]

## PROCESS & KEY RULES:
[列出AI应遵循的关键规则和流程]

## INPUT DOCUMENTS:
[指定需要粘贴的前序文档]

## QUESTIONS FRAMEWORK:
[设计结构化的问题流程]

## DESIRED OUTPUT FORMAT:
[定义期望的输出格式和结构]
```

## 6. 常见问题和解决方案

### 提示词效果不佳的解决方法

**问题**：AI生成的内容质量不高或不相关

**解决方案**：
- 确保完整复制提示词，包括所有指令和角色设定
- 提供更详细、具体的回答，避免模糊或过于简短的回答
- 尝试使用更高级的AI模型（如GPT-4而非GPT-3.5）
- 降低AI温度设置（如设为0.2-0.5）以获得更聚焦的回答
- 明确指出不满意的部分，要求AI重新生成或改进

**问题**：AI回答过于通用，缺乏针对性

**解决方案**：
- 在提示词开头添加更多项目和行业背景
- 提供具体例子说明您的期望
- 要求AI使用您提供的信息而非通用知识
- 在回答中引用特定术语和概念，引导AI使用相同语言
- 明确指出需要更具体内容的部分，提供方向性指导

### 处理复杂项目需求的技巧

**问题**：项目太复杂，单个提示词无法涵盖所有方面

**解决方案**：
- 将项目分解为多个子项目或模块，分别使用提示词
- 先使用提示词处理核心功能，再单独处理特殊功能
- 在提示词中明确优先级，让AI专注于最重要的方面
- 使用多轮对话，逐步深入复杂话题
- 创建自定义提示词，专门针对项目的复杂部分

**问题**：需要整合多个利益相关者的需求

**解决方案**：
- 在使用提示词前，先整合所有利益相关者的输入
- 在提示词中明确说明不同利益相关者的优先级
- 要求AI在生成内容时考虑多方需求并平衡
- 使用提示词生成初稿后，收集反馈并要求AI整合
- 针对有分歧的部分，要求AI提供多个选项供选择

### 提高AI输出质量的方法

**问题**：如何获得更专业、更有深度的AI输出

**解决方案**：
- 使用专业术语和行业词汇，提升AI回答的专业性
- 要求AI从特定专业角度（如产品经理、UX设计师）回答
- 明确指出需要深入探讨的方面，要求更详细的分析
- 提供高质量的参考资料或案例供AI参考


---

