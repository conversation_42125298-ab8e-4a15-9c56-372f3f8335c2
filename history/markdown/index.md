# Augment Code 聊天历史索引

生成时间: 2025年05月27日 15:27:12

## 统计信息

- 项目数量: 41
- 会话数量: 167
- 消息数量: 5830

## 项目列表

| 项目名称 | 会话数量 | 最后交互时间 | 记忆文件 |
|---------|----------|--------------|----------|
| [openai-cookbook](openai-cookbook/) | 3 | 2025年05月16日 03:46 | ✅ |
| [Roo-Cline](Roo-Cline/) | 5 | 2025年05月26日 01:54 | ✅ |
| [CursorPlus](CursorPlus/) | 4 | 2025年04月16日 23:15 | ✅ |
| [n8n](n8n/) | 6 | 2025年05月12日 10:57 | ✅ |
| [cursor-custom-agents-rules-generator](cursor-custom-agents-rules-generator/) | 4 | 2025年04月29日 01:43 | ✅ |
| [awesome-ai-system-prompts](awesome-ai-system-prompts/) | 6 | 2025年04月17日 05:14 | ✅ |
| [claude-task-master](claude-task-master/) | 9 | 2025年05月15日 03:09 | ✅ |
| [repomix](repomix/) | 8 | 2025年04月27日 08:43 | ✅ |
| [ai-assisted-development-guide](ai-assisted-development-guide/) | 2 | 2025年04月15日 11:17 | ✅ |
| [onefilellm](onefilellm/) | 5 | 2025年05月08日 10:50 | ✅ |
| [Tampermonkey_Plugin_Script](Tampermonkey_Plugin_Script/) | 2 | 2025年05月16日 01:54 | ❌ |
| [git-doc](git-doc/) | 2 | 2025年04月27日 10:00 | ❌ |
| [cursor-memory-bank](cursor-memory-bank/) | 1 | 2025年05月23日 09:41 | ✅ |
| [cline](cline/) | 3 | 2025年04月15日 10:00 | ❌ |
| [Bilibili-Evolved](Bilibili-Evolved/) | 4 | 2025年05月12日 11:03 | ✅ |
| [augment-swebench-agent](augment-swebench-agent/) | 2 | 2025年04月17日 08:19 | ✅ |
| [cursor-view](cursor-view/) | 15 | 2025年05月27日 07:24 | ✅ |
| [rules_template](rules_template/) | 6 | 2025年05月23日 09:41 | ✅ |
| [git-prompt](git-prompt/) | 8 | 2025年04月26日 02:56 | ✅ |
| [twitter-web-exporter](twitter-web-exporter/) | 3 | 2025年04月28日 08:12 | ✅ |
| [alps-writer](alps-writer/) | 1 | 2025年05月08日 10:41 | ✅ |
| [cline](cline/) | 2 | 2025年05月19日 08:27 | ❌ |
| [prompt-architect](prompt-architect/) | 3 | 2025年05月26日 11:20 | ❌ |
| [302_prompt_generator](302_prompt_generator/) | 2 | 2025年05月26日 09:44 | ❌ |
| [Tutorial-Codebase-Knowledge](Tutorial-Codebase-Knowledge/) | 8 | 2025年04月28日 03:26 | ✅ |
| [qiaomu-blog3](qiaomu-blog3/) | 5 | 2025年04月27日 11:32 | ❌ |
| [deepractice.github.io](deepractice.github.io/) | 5 | 2025年05月26日 08:51 | ✅ |
| [rulebook-ai](rulebook-ai/) | 4 | 2025年05月23日 09:41 | ✅ |
| [rule-matcher-extension](rule-matcher-extension/) | 2 | 2025年04月11日 09:56 | ❌ |
| [awesome-cursor-rules-mdc](awesome-cursor-rules-mdc/) | 2 | 2025年04月11日 09:50 | ✅ |
| [vscode-cursor-rules](vscode-cursor-rules/) | 1 | 2025年04月11日 08:32 | ❌ |
| [awesome-n8n-templates](awesome-n8n-templates/) | 3 | 2025年05月12日 10:57 | ✅ |
| [OpenAIAgents](OpenAIAgents/) | 3 | 2025年04月05日 02:06 | ❌ |
| [system-prompts-and-models-of-ai-tools](system-prompts-and-models-of-ai-tools/) | 3 | 2025年04月14日 06:41 | ✅ |
| [AI-Product-Development-Toolkit](AI-Product-Development-Toolkit/) | 5 | 2025年05月16日 02:46 | ✅ |
| [cards-go](cards-go/) | 2 | 2025年04月17日 11:41 | ❌ |
| [git-prompt](git-prompt/) | 3 | 2025年05月07日 02:04 | ❌ |
| [cofounder](cofounder/) | 2 | 2025年04月21日 01:47 | ✅ |
| [directories](directories/) | 2 | 2025年04月10日 01:46 | ❌ |
| [awesome-cursorrules](awesome-cursorrules/) | 8 | 2025年04月11日 09:22 | ✅ |
| [AI-test](AI-test/) | 3 | 2025年04月10日 05:55 | ✅ |

## 详细会话列表

### openai-cookbook

[📝 记忆文件](openai-cookbook/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-16_02-53-23](openai-cookbook/2025-05-16_02-53-23.md) | 2025年05月16日 02:53 | 2025年05月16日 02:53 | 0 |
| [2025-05-16_02-53-23](openai-cookbook/2025-05-16_02-53-23.md) | 2025年05月16日 02:53 | 2025年05月16日 02:54 | 1 |
| [2025-05-16_02-54-34](openai-cookbook/2025-05-16_02-54-34.md) | 2025年05月16日 02:54 | 2025年05月16日 03:46 | 46 |

### Roo-Cline

[📝 记忆文件](Roo-Cline/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [RooCode历史记录存储查询解析](Roo-Cline/RooCode历史记录存储查询解析.md) | 2025年05月22日 11:05 | 2025年05月23日 02:03 | 14 |
| [VSCode插件登录逻辑探索](Roo-Cline/VSCode插件登录逻辑探索.md) | 2025年05月23日 05:52 | 2025年05月23日 08:36 | 18 |
| [Orchestrator模式解读](Roo-Cline/Orchestrator模式解读.md) | 2025年05月23日 08:41 | 2025年05月23日 09:25 | 19 |
| [分析五月版本更新历史
](Roo-Cline/分析五月版本更新历史_.md) | 2025年05月23日 09:28 | 2025年05月23日 09:32 | 5 |
| [2025-05-23_09-42-01](Roo-Cline/2025-05-23_09-42-01.md) | 2025年05月23日 09:42 | 2025年05月26日 01:54 | 21 |

### CursorPlus

[📝 记忆文件](CursorPlus/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-16_02-24-57](CursorPlus/2025-04-16_02-24-57.md) | 2025年04月16日 02:24 | 2025年04月16日 03:16 | 44 |
| [生成网站LLMs.txt文件请求
](CursorPlus/生成网站LLMs.txt文件请求_.md) | 2025年04月16日 07:15 | 2025年04月16日 07:31 | 27 |
| [翻译提示词指南为中文
](CursorPlus/翻译提示词指南为中文_.md) | 2025年04月16日 07:51 | 2025年04月16日 07:55 | 4 |
| [网站复刻与PRD文档编写需求
](CursorPlus/网站复刻与PRD文档编写需求_.md) | 2025年04月16日 09:03 | 2025年04月16日 23:15 | 13 |

### n8n

[📝 记忆文件](n8n/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-21_09-27-05](n8n/2025-04-21_09-27-05.md) | 2025年04月21日 09:27 | 2025年05月12日 03:41 | 29 |
| [2025-04-21_09-41-16](n8n/2025-04-21_09-41-16.md) | 2025年04月21日 09:41 | 2025年04月21日 09:41 | 1 |
| [n8n节点理解&梳理](n8n/n8n节点理解_梳理.md) | 2025年04月21日 09:45 | 2025年04月21日 09:52 | 15 |
| [Claude Code Best Practices Request
](n8n/Claude_Code_Best_Practices_Request_.md) | 2025年04月22日 03:24 | 2025年05月12日 10:57 | 36 |
| [2025-05-12_03-42-09](n8n/2025-05-12_03-42-09.md) | 2025年05月12日 03:42 | 2025年05月12日 04:46 | 17 |
| [n8n导入导出JSON结构分析
](n8n/n8n导入导出JSON结构分析_.md) | 2025年05月12日 04:48 | 2025年05月12日 04:56 | 13 |

### cursor-custom-agents-rules-generator

[📝 记忆文件](cursor-custom-agents-rules-generator/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-14_06-44-50](cursor-custom-agents-rules-generator/2025-04-14_06-44-50.md) | 2025年04月14日 06:44 | 2025年04月18日 06:22 | 64 |
| [2025-04-18_02-17-11](cursor-custom-agents-rules-generator/2025-04-18_02-17-11.md) | 2025年04月18日 02:17 | 2025年04月18日 05:38 | 29 |
| [创建前端网站分析复刻规则
](cursor-custom-agents-rules-generator/创建前端网站分析复刻规则_.md) | 2025年04月18日 06:24 | 2025年04月28日 08:53 | 18 |
| [2025-04-28_08-53-49](cursor-custom-agents-rules-generator/2025-04-28_08-53-49.md) | 2025年04月28日 08:53 | 2025年04月29日 01:43 | 81 |

### awesome-ai-system-prompts

[📝 记忆文件](awesome-ai-system-prompts/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-14_08-13-18](awesome-ai-system-prompts/2025-04-14_08-13-18.md) | 2025年04月14日 08:13 | 2025年04月14日 08:13 | 1 |
| [2025-04-14_08-14-39](awesome-ai-system-prompts/2025-04-14_08-14-39.md) | 2025年04月14日 08:14 | 2025年04月17日 02:05 | 44 |
| [2025-04-17_01-41-09](awesome-ai-system-prompts/2025-04-17_01-41-09.md) | 2025年04月17日 01:41 | 2025年04月17日 02:13 | 25 |
| [Scraping Augment Code blog links
](awesome-ai-system-prompts/Scraping_Augment_Code_blog_links_.md) | 2025年04月17日 02:14 | 2025年04月17日 03:26 | 139 |
| [2025-04-17_03-21-41](awesome-ai-system-prompts/2025-04-17_03-21-41.md) | 2025年04月17日 03:21 | 2025年04月17日 05:14 | 74 |
| [2025-04-17_03-26-15](awesome-ai-system-prompts/2025-04-17_03-26-15.md) | 2025年04月17日 03:26 | 2025年04月17日 03:26 | 4 |

### claude-task-master

[📝 记忆文件](claude-task-master/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-14_11-18-12](claude-task-master/2025-04-14_11-18-12.md) | 2025年04月14日 11:18 | 2025年05月13日 02:51 | 61 |
| [2025-05-13_02-58-44](claude-task-master/2025-05-13_02-58-44.md) | 2025年05月13日 02:58 | 2025年05月13日 03:07 | 14 |
| [OpenRouter model configuration analysis
](claude-task-master/OpenRouter_model_configuration_analysis_.md) | 2025年05月13日 07:11 | 2025年05月13日 08:30 | 47 |
| [Examples folder analysis request
](claude-task-master/Examples_folder_analysis_request_.md) | 2025年05月13日 08:32 | 2025年05月14日 02:35 | 48 |
| [任务管理器命令功能分析请求
](claude-task-master/任务管理器命令功能分析请求_.md) | 2025年05月14日 02:43 | 2025年05月14日 03:16 | 28 |
| [2025-05-14_03-16-49](claude-task-master/2025-05-14_03-16-49.md) | 2025年05月14日 03:16 | 2025年05月14日 05:04 | 5 |
| [2025-05-14_05-04-03](claude-task-master/2025-05-14_05-04-03.md) | 2025年05月14日 05:04 | 2025年05月14日 06:09 | 19 |
| [Analyzing parse_prd MCP tool workflow
](claude-task-master/Analyzing_parse_prd_MCP_tool_workflow_.md) | 2025年05月14日 09:11 | 2025年05月15日 02:56 | 4 |
| [2025-05-15_02-56-43](claude-task-master/2025-05-15_02-56-43.md) | 2025年05月15日 02:56 | 2025年05月15日 03:09 | 18 |

### repomix

[📝 记忆文件](repomix/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-27_01-50-02](repomix/2025-04-27_01-50-02.md) | 2025年04月27日 01:50 | 2025年04月27日 02:07 | 21 |
| [项目功能分析请求
](repomix/项目功能分析请求_.md) | 2025年04月27日 02:09 | 2025年04月27日 03:48 | 84 |
| [GitHub教程生成器开发启动
](repomix/GitHub教程生成器开发启动_.md) | 2025年04月27日 03:48 | 2025年04月27日 05:43 | 336 |
| [分析仓库获取逻辑错误
](repomix/分析仓库获取逻辑错误_.md) | 2025年04月27日 05:59 | 2025年04月27日 06:06 | 39 |
| [2025-04-27_06-10-27](repomix/2025-04-27_06-10-27.md) | 2025年04月27日 06:10 | 2025年04月27日 06:38 | 53 |
| [2025-04-27_06-38-44](repomix/2025-04-27_06-38-44.md) | 2025年04月27日 06:38 | 2025年04月27日 07:35 | 148 |
| [2025-04-27_07-36-00](repomix/2025-04-27_07-36-00.md) | 2025年04月27日 07:36 | 2025年04月27日 08:37 | 168 |
| [优化教程列表界面展示
](repomix/优化教程列表界面展示_.md) | 2025年04月27日 08:38 | 2025年04月27日 08:43 | 20 |

### ai-assisted-development-guide

[📝 记忆文件](ai-assisted-development-guide/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-15_05-06-00](ai-assisted-development-guide/2025-04-15_05-06-00.md) | 2025年04月15日 05:06 | 2025年04月15日 05:14 | 12 |
| [Obsidian Web Clipper LLMs.txt Generation
](ai-assisted-development-guide/Obsidian_Web_Clipper_LLMs.txt_Generation_.md) | 2025年04月15日 05:16 | 2025年04月15日 11:17 | 23 |

### onefilellm

[📝 记忆文件](onefilellm/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-07_02-55-37](onefilellm/2025-05-07_02-55-37.md) | 2025年05月07日 02:55 | 2025年05月07日 08:09 | 20 |
| [2025-05-07_08-21-16](onefilellm/2025-05-07_08-21-16.md) | 2025年05月07日 08:21 | 2025年05月07日 10:27 | 1 |
| [2025-05-07_10-27-50](onefilellm/2025-05-07_10-27-50.md) | 2025年05月07日 10:27 | 2025年05月08日 07:01 | 72 |
| [2025-05-08_07-01-40](onefilellm/2025-05-08_07-01-40.md) | 2025年05月08日 07:01 | 2025年05月08日 10:50 | 13 |
| [中文网页设计任务规划
](onefilellm/中文网页设计任务规划_.md) | 2025年05月08日 07:29 | 2025年05月08日 10:50 | 19 |

### Tampermonkey_Plugin_Script

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-16_01-54-07](Tampermonkey_Plugin_Script/2025-05-16_01-54-07.md) | 2025年05月16日 01:54 | 2025年05月16日 01:54 | 0 |
| [2025-05-16_01-54-07](Tampermonkey_Plugin_Script/2025-05-16_01-54-07.md) | 2025年05月16日 01:54 | 2025年05月16日 01:54 | 1 |

### git-doc

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-27_10-00-09](git-doc/2025-04-27_10-00-09.md) | 2025年04月27日 10:00 | 2025年04月27日 10:00 | 0 |
| [2025-04-27_10-00-09](git-doc/2025-04-27_10-00-09.md) | 2025年04月27日 10:00 | 2025年04月27日 10:00 | 1 |

### cursor-memory-bank

[📝 记忆文件](cursor-memory-bank/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-16_09-34-49](cursor-memory-bank/2025-05-16_09-34-49.md) | 2025年05月16日 09:34 | 2025年05月23日 09:41 | 34 |

### cline

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-15_08-30-26](cline/2025-04-15_08-30-26.md) | 2025年04月15日 08:30 | 2025年04月15日 08:30 | 0 |
| [2025-04-15_08-31-08](cline/2025-04-15_08-31-08.md) | 2025年04月15日 08:31 | 2025年04月15日 09:09 | 64 |
| [获取OpenAI页面并翻译成中文
](cline/获取OpenAI页面并翻译成中文_.md) | 2025年04月15日 09:52 | 2025年04月15日 10:00 | 4 |

### Bilibili-Evolved

[📝 记忆文件](Bilibili-Evolved/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-09_06-55-57](Bilibili-Evolved/2025-05-09_06-55-57.md) | 2025年05月09日 06:55 | 2025年05月12日 10:51 | 62 |
| [Exploring n8n nodes and usage
](Bilibili-Evolved/Exploring_n8n_nodes_and_usage_.md) | 2025年05月09日 07:42 | 2025年05月09日 07:49 | 22 |
| [2025-05-12_10-52-39](Bilibili-Evolved/2025-05-12_10-52-39.md) | 2025年05月12日 10:52 | 2025年05月12日 11:03 | 18 |
| [2025-05-12_10-56-20](Bilibili-Evolved/2025-05-12_10-56-20.md) | 2025年05月12日 10:56 | 2025年05月12日 10:57 | 3 |

### augment-swebench-agent

[📝 记忆文件](augment-swebench-agent/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-17_06-13-28](augment-swebench-agent/2025-04-17_06-13-28.md) | 2025年04月17日 06:13 | 2025年04月17日 08:19 | 62 |
| [2025-04-17_07-11-53](augment-swebench-agent/2025-04-17_07-11-53.md) | 2025年04月17日 07:11 | 2025年04月17日 07:11 | 0 |

### cursor-view

[📝 记忆文件](cursor-view/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-18_10-01-15](cursor-view/2025-05-18_10-01-15.md) | 2025年05月18日 10:01 | 2025年05月22日 05:32 | 43 |
| [VSCode workspaceStorage directory examination
](cursor-view/VSCode_workspaceStorage_directory_examination_.md) | 2025年05月18日 11:14 | 2025年05月18日 12:28 | 22 |
| [2025-05-18_12-34-39](cursor-view/2025-05-18_12-34-39.md) | 2025年05月18日 12:34 | 2025年05月19日 01:53 | 23 |
| [2025-05-19_02-03-59](cursor-view/2025-05-19_02-03-59.md) | 2025年05月19日 02:03 | 2025年05月19日 05:23 | 93 |
| [2025-05-19_05-06-37](cursor-view/2025-05-19_05-06-37.md) | 2025年05月19日 05:06 | 2025年05月19日 05:20 | 36 |
| [2025-05-19_06-24-37](cursor-view/2025-05-19_06-24-37.md) | 2025年05月19日 06:24 | 2025年05月19日 08:23 | 16 |
| [Refactoring Cursor Chat Extraction System
](cursor-view/Refactoring_Cursor_Chat_Extraction_System_.md) | 2025年05月19日 08:23 | 2025年05月20日 02:41 | 35 |
| [Analyzing Cursor chat extraction logs
](cursor-view/Analyzing_Cursor_chat_extraction_logs_.md) | 2025年05月20日 03:04 | 2025年05月20日 03:42 | 43 |
| [分析Augment Code历史记录数据
](cursor-view/分析Augment_Code历史记录数据_.md) | 2025年05月20日 06:15 | 2025年05月22日 05:33 | 81 |
| [优化AugmentCode导出Markdown格式](cursor-view/优化AugmentCode导出Markdown格式.md) | 2025年05月22日 04:54 | 2025年05月22日 07:38 | 58 |
| [2025-05-22_07-21-25](cursor-view/2025-05-22_07-21-25.md) | 2025年05月22日 07:21 | 2025年05月26日 02:08 | 55 |
| [2025-05-26_04-33-27](cursor-view/2025-05-26_04-33-27.md) | 2025年05月26日 04:33 | 2025年05月27日 03:04 | 67 |
| [重构代码](cursor-view/重构代码.md) | 2025年05月27日 03:10 | 2025年05月27日 05:54 | 49 |
| [查询整合](cursor-view/查询整合.md) | 2025年05月27日 05:57 | 2025年05月27日 06:08 | 22 |
| [元提示词-for-生成提示词分析与探讨](cursor-view/元提示词-for-生成提示词分析与探讨.md) | 2025年05月27日 06:56 | 2025年05月27日 07:24 | 27 |

### rules_template

[📝 记忆文件](rules_template/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-17_08-39-19](rules_template/2025-04-17_08-39-19.md) | 2025年04月17日 08:39 | 2025年04月17日 09:51 | 5 |
| [2025-04-17_09-51-49](rules_template/2025-04-17_09-51-49.md) | 2025年04月17日 09:51 | 2025年04月17日 09:51 | 0 |
| [2025-04-17_09-52-35](rules_template/2025-04-17_09-52-35.md) | 2025年04月17日 09:52 | 2025年04月17日 11:18 | 28 |
| [2025-04-17_10-12-08](rules_template/2025-04-17_10-12-08.md) | 2025年04月17日 10:12 | 2025年04月17日 10:18 | 35 |
| [2025-04-17_11-23-57](rules_template/2025-04-17_11-23-57.md) | 2025年04月17日 11:23 | 2025年05月16日 08:51 | 23 |
| [2025-05-16_09-04-23](rules_template/2025-05-16_09-04-23.md) | 2025年05月16日 09:04 | 2025年05月23日 09:41 | 20 |

### git-prompt

[📝 记忆文件](git-prompt/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-24_09-29-51](git-prompt/2025-04-24_09-29-51.md) | 2025年04月24日 09:29 | 2025年04月24日 11:18 | 187 |
| [2025-04-24_11-24-08](git-prompt/2025-04-24_11-24-08.md) | 2025年04月24日 11:24 | 2025年04月25日 02:23 | 77 |
| [2025-04-25_02-24-36](git-prompt/2025-04-25_02-24-36.md) | 2025年04月25日 02:24 | 2025年04月25日 02:26 | 7 |
| [提示词卡片宽度差异分析
](git-prompt/提示词卡片宽度差异分析_.md) | 2025年04月25日 02:28 | 2025年04月25日 03:40 | 151 |
| [2025-04-25_03-42-02](git-prompt/2025-04-25_03-42-02.md) | 2025年04月25日 03:42 | 2025年04月25日 05:40 | 73 |
| [2025-04-25_05-48-15](git-prompt/2025-04-25_05-48-15.md) | 2025年04月25日 05:48 | 2025年04月25日 08:04 | 147 |
| [2025-04-25_08-07-41](git-prompt/2025-04-25_08-07-41.md) | 2025年04月25日 08:07 | 2025年04月25日 10:43 | 59 |
| [2025-04-26_02-46-46](git-prompt/2025-04-26_02-46-46.md) | 2025年04月26日 02:46 | 2025年04月26日 02:56 | 1 |

### twitter-web-exporter

[📝 记忆文件](twitter-web-exporter/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-28_07-54-50](twitter-web-exporter/2025-04-28_07-54-50.md) | 2025年04月28日 07:54 | 2025年04月28日 07:54 | 0 |
| [2025-04-28_07-54-50](twitter-web-exporter/2025-04-28_07-54-50.md) | 2025年04月28日 07:54 | 2025年04月28日 07:55 | 1 |
| [2025-04-28_07-55-20](twitter-web-exporter/2025-04-28_07-55-20.md) | 2025年04月28日 07:55 | 2025年04月28日 08:12 | 28 |

### alps-writer

[📝 记忆文件](alps-writer/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-08_07-50-59](alps-writer/2025-05-08_07-50-59.md) | 2025年05月08日 07:50 | 2025年05月08日 10:41 | 85 |

### cline

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-19_08-27-12](cline/2025-05-19_08-27-12.md) | 2025年05月19日 08:27 | 2025年05月19日 08:27 | 0 |
| [2025-05-19_08-27-12](cline/2025-05-19_08-27-12.md) | 2025年05月19日 08:27 | 2025年05月19日 08:27 | 0 |

### prompt-architect

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-26_10-08-19](prompt-architect/2025-05-26_10-08-19.md) | 2025年05月26日 10:08 | 2025年05月26日 10:08 | 0 |
| [2025-05-26_10-08-19](prompt-architect/2025-05-26_10-08-19.md) | 2025年05月26日 10:08 | 2025年05月26日 10:08 | 1 |
| [2025-05-26_10-10-07](prompt-architect/2025-05-26_10-10-07.md) | 2025年05月26日 10:10 | 2025年05月26日 11:20 | 32 |

### 302_prompt_generator

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-26_09-21-17](302_prompt_generator/2025-05-26_09-21-17.md) | 2025年05月26日 09:21 | 2025年05月26日 09:26 | 8 |
| [2025-05-26_09-27-07](302_prompt_generator/2025-05-26_09-27-07.md) | 2025年05月26日 09:27 | 2025年05月26日 09:44 | 10 |

### Tutorial-Codebase-Knowledge

[📝 记忆文件](Tutorial-Codebase-Knowledge/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-23_02-06-41](Tutorial-Codebase-Knowledge/2025-04-23_02-06-41.md) | 2025年04月23日 02:06 | 2025年04月28日 02:50 | 85 |
| [LLM调用流程分析请求
](Tutorial-Codebase-Knowledge/LLM调用流程分析请求_.md) | 2025年04月23日 07:38 | 2025年04月24日 01:48 | 8 |
| [Frontend Document Storage Solution Recommendations
](Tutorial-Codebase-Knowledge/Frontend_Document_Storage_Solution_Recommendations_.md) | 2025年04月23日 08:01 | 2025年04月24日 01:48 | 78 |
| [Supabase upload permission error analysis
](Tutorial-Codebase-Knowledge/Supabase_upload_permission_error_analysis_.md) | 2025年04月23日 09:11 | 2025年04月23日 09:55 | 40 |
| [Supabase sync file analysis request
](Tutorial-Codebase-Knowledge/Supabase_sync_file_analysis_request_.md) | 2025年04月23日 10:01 | 2025年04月23日 10:20 | 38 |
| [2025-04-23_10-46-57](Tutorial-Codebase-Knowledge/2025-04-23_10-46-57.md) | 2025年04月23日 10:46 | 2025年04月26日 15:02 | 25 |
| [克隆ShumerpPrompt网站HTML原型开发
](Tutorial-Codebase-Knowledge/克隆ShumerpPrompt网站HTML原型开发_.md) | 2025年04月24日 01:46 | 2025年04月25日 16:28 | 57 |
| [2025-04-28_02-50-29](Tutorial-Codebase-Knowledge/2025-04-28_02-50-29.md) | 2025年04月28日 02:50 | 2025年04月28日 03:26 | 74 |

### qiaomu-blog3

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-27_10-53-03](qiaomu-blog3/2025-04-27_10-53-03.md) | 2025年04月27日 10:53 | 2025年04月27日 10:53 | 0 |
| [2025-04-27_10-53-03](qiaomu-blog3/2025-04-27_10-53-03.md) | 2025年04月27日 10:53 | 2025年04月27日 10:53 | 1 |
| [2025-04-27_10-53-26](qiaomu-blog3/2025-04-27_10-53-26.md) | 2025年04月27日 10:53 | 2025年04月27日 11:29 | 47 |
| [2025-04-27_11-07-19](qiaomu-blog3/2025-04-27_11-07-19.md) | 2025年04月27日 11:07 | 2025年04月27日 11:29 | 4 |
| [Database SQL syntax errors analysis
](qiaomu-blog3/Database_SQL_syntax_errors_analysis_.md) | 2025年04月27日 11:12 | 2025年04月27日 11:32 | 46 |

### deepractice.github.io

[📝 记忆文件](deepractice.github.io/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-26_06-07-19](deepractice.github.io/2025-05-26_06-07-19.md) | 2025年05月26日 06:07 | 2025年05月26日 06:07 | 0 |
| [2025-05-26_06-07-19](deepractice.github.io/2025-05-26_06-07-19.md) | 2025年05月26日 06:07 | 2025年05月26日 06:07 | 1 |
| [2025-05-26_06-07-46](deepractice.github.io/2025-05-26_06-07-46.md) | 2025年05月26日 06:07 | 2025年05月26日 06:20 | 36 |
| [Firecrawl analysis of OpenAI cookbook
](deepractice.github.io/Firecrawl_analysis_of_OpenAI_cookbook_.md) | 2025年05月26日 06:24 | 2025年05月26日 06:43 | 12 |
| [Firecrawl scraping request cancelled
](deepractice.github.io/Firecrawl_scraping_request_cancelled_.md) | 2025年05月26日 07:33 | 2025年05月26日 08:51 | 2 |

### rulebook-ai

[📝 记忆文件](rulebook-ai/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-16_04-27-35](rulebook-ai/2025-05-16_04-27-35.md) | 2025年05月16日 04:27 | 2025年05月18日 09:43 | 27 |
| [分析规则管理文件功能与流程
](rulebook-ai/分析规则管理文件功能与流程_.md) | 2025年05月16日 06:34 | 2025年05月16日 09:20 | 42 |
| [规则平台差异分析请求
](rulebook-ai/规则平台差异分析请求_.md) | 2025年05月16日 07:27 | 2025年05月16日 09:20 | 7 |
| [Augment Code历史记录存储查询
](rulebook-ai/Augment_Code历史记录存储查询_.md) | 2025年05月18日 09:45 | 2025年05月23日 09:41 | 21 |

### rule-matcher-extension

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-11_09-56-32](rule-matcher-extension/2025-04-11_09-56-32.md) | 2025年04月11日 09:56 | 2025年04月11日 09:56 | 0 |
| [2025-04-11_09-56-32](rule-matcher-extension/2025-04-11_09-56-32.md) | 2025年04月11日 09:56 | 2025年04月11日 09:56 | 1 |

### awesome-cursor-rules-mdc

[📝 记忆文件](awesome-cursor-rules-mdc/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-10_11-52-10](awesome-cursor-rules-mdc/2025-04-10_11-52-10.md) | 2025年04月10日 11:52 | 2025年04月10日 11:53 | 7 |
| [分析大模型调用功能
](awesome-cursor-rules-mdc/分析大模型调用功能_.md) | 2025年04月10日 11:54 | 2025年04月11日 09:50 | 13 |

### vscode-cursor-rules

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-10_06-07-34](vscode-cursor-rules/2025-04-10_06-07-34.md) | 2025年04月10日 06:07 | 2025年04月11日 08:32 | 6 |

### awesome-n8n-templates

[📝 记忆文件](awesome-n8n-templates/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-09_07-28-35](awesome-n8n-templates/2025-05-09_07-28-35.md) | 2025年05月09日 07:28 | 2025年05月09日 07:40 | 21 |
| [2025-05-09_07-49-52](awesome-n8n-templates/2025-05-09_07-49-52.md) | 2025年05月09日 07:49 | 2025年05月09日 08:18 | 21 |
| [2025-05-09_08-20-29](awesome-n8n-templates/2025-05-09_08-20-29.md) | 2025年05月09日 08:20 | 2025年05月12日 10:57 | 26 |

### OpenAIAgents

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-05_02-04-11](OpenAIAgents/2025-04-05_02-04-11.md) | 2025年04月05日 02:04 | 2025年04月05日 02:04 | 0 |
| [2025-04-05_02-04-11](OpenAIAgents/2025-04-05_02-04-11.md) | 2025年04月05日 02:04 | 2025年04月05日 02:06 | 3 |
| [Welcome to the Augment Agent](OpenAIAgents/Welcome_to_the_Augment_Agent.md) | 2025年04月05日 02:04 | 2025年04月05日 02:04 | 4 |

### system-prompts-and-models-of-ai-tools

[📝 记忆文件](system-prompts-and-models-of-ai-tools/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-14_03-34-51](system-prompts-and-models-of-ai-tools/2025-04-14_03-34-51.md) | 2025年04月14日 03:34 | 2025年04月14日 03:46 | 5 |
| [2025-04-14_03-41-12](system-prompts-and-models-of-ai-tools/2025-04-14_03-41-12.md) | 2025年04月14日 03:41 | 2025年04月14日 04:12 | 16 |
| [Create Chinese translation directories
](system-prompts-and-models-of-ai-tools/Create_Chinese_translation_directories_.md) | 2025年04月14日 04:14 | 2025年04月14日 06:41 | 54 |

### AI-Product-Development-Toolkit

[📝 记忆文件](AI-Product-Development-Toolkit/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-15_07-01-58](AI-Product-Development-Toolkit/2025-05-15_07-01-58.md) | 2025年05月15日 07:01 | 2025年05月15日 08:02 | 43 |
| [2025-05-15_08-09-41](AI-Product-Development-Toolkit/2025-05-15_08-09-41.md) | 2025年05月15日 08:09 | 2025年05月15日 08:28 | 21 |
| [2025-05-15_08-24-30](AI-Product-Development-Toolkit/2025-05-15_08-24-30.md) | 2025年05月15日 08:24 | 2025年05月15日 08:24 | 0 |
| [2025-05-15_08-29-20](AI-Product-Development-Toolkit/2025-05-15_08-29-20.md) | 2025年05月15日 08:29 | 2025年05月15日 10:23 | 16 |
| [2025-05-15_10-32-37](AI-Product-Development-Toolkit/2025-05-15_10-32-37.md) | 2025年05月15日 10:32 | 2025年05月16日 02:46 | 35 |

### cards-go

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-17_11-41-27](cards-go/2025-04-17_11-41-27.md) | 2025年04月17日 11:41 | 2025年04月17日 11:41 | 0 |
| [2025-04-17_11-41-27](cards-go/2025-04-17_11-41-27.md) | 2025年04月17日 11:41 | 2025年04月17日 11:41 | 1 |

### git-prompt

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-05-07_01-53-17](git-prompt/2025-05-07_01-53-17.md) | 2025年05月07日 01:53 | 2025年05月07日 01:53 | 0 |
| [2025-05-07_01-53-17](git-prompt/2025-05-07_01-53-17.md) | 2025年05月07日 01:53 | 2025年05月07日 02:03 | 3 |
| [2025-05-07_02-04-05](git-prompt/2025-05-07_02-04-05.md) | 2025年05月07日 02:04 | 2025年05月07日 02:04 | 1 |

### cofounder

[📝 记忆文件](cofounder/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-18_06-35-50](cofounder/2025-04-18_06-35-50.md) | 2025年04月18日 06:35 | 2025年04月18日 07:01 | 41 |
| [I'll help you analyze the system prompts in the codebase and create Chinese translations. Let me start by retrieving the content of the specified file.
](cofounder/I_ll_help_you_analyze_the_system_prompts_in_the_codebase_and_create_Chinese_translations._Let_me_start_by_retrieving_the_content_of_the_specified_file._.md) | 2025年04月18日 07:01 | 2025年04月21日 01:47 | 39 |

### directories

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-10_01-46-45](directories/2025-04-10_01-46-45.md) | 2025年04月10日 01:46 | 2025年04月10日 01:46 | 0 |
| [2025-04-10_01-46-45](directories/2025-04-10_01-46-45.md) | 2025年04月10日 01:46 | 2025年04月10日 01:46 | 0 |

### awesome-cursorrules

[📝 记忆文件](awesome-cursorrules/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-10_05-59-46](awesome-cursorrules/2025-04-10_05-59-46.md) | 2025年04月10日 05:59 | 2025年04月10日 08:37 | 197 |
| [2025-04-10_08-31-54](awesome-cursorrules/2025-04-10_08-31-54.md) | 2025年04月10日 08:31 | 2025年04月11日 01:48 | 125 |
| [2025-04-11_01-50-04](awesome-cursorrules/2025-04-11_01-50-04.md) | 2025年04月11日 01:50 | 2025年04月11日 03:16 | 76 |
| [分析processRuleDirectory方法调用位置
](awesome-cursorrules/分析processRuleDirectory方法调用位置_.md) | 2025年04月11日 03:06 | 2025年04月11日 03:30 | 29 |
| [2025-04-11_03-32-56](awesome-cursorrules/2025-04-11_03-32-56.md) | 2025年04月11日 03:32 | 2025年04月11日 06:23 | 99 |
| [2025-04-11_06-29-24](awesome-cursorrules/2025-04-11_06-29-24.md) | 2025年04月11日 06:29 | 2025年04月11日 06:35 | 20 |
| [2025-04-11_06-40-48](awesome-cursorrules/2025-04-11_06-40-48.md) | 2025年04月11日 06:40 | 2025年04月11日 08:23 | 126 |
| [2025-04-11_08-30-00](awesome-cursorrules/2025-04-11_08-30-00.md) | 2025年04月11日 08:30 | 2025年04月11日 09:22 | 70 |

### AI-test

[📝 记忆文件](AI-test/Memories.md)

| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |
|---------|----------|--------------|----------|
| [2025-04-10_01-48-11](AI-test/2025-04-10_01-48-11.md) | 2025年04月10日 01:48 | 2025年04月10日 02:46 | 59 |
| [Augment Documentation Overview
](AI-test/Augment_Documentation_Overview_.md) | 2025年04月10日 02:47 | 2025年04月10日 02:56 | 6 |
| [VSCode插件开发代码分析可行性
](AI-test/VSCode插件开发代码分析可行性_.md) | 2025年04月10日 05:54 | 2025年04月10日 05:55 | 1 |

