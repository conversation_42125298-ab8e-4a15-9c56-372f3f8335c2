# ai-assisted-development-guide 的记忆

# Web Clipper & Templates
- User shared a complex Twitter/X post template for Obsidian Web Clipper that extracts avatars, usernames, post content, media, and quoted tweets using CSS selectors and multiple filters.
- User wants help creating system prompts to generate Obsidian Web Clipper template JSON data for specific websites.
- User wants to use the Firecrawl MCP tool to analyze website structure to provide more accurate context for Web Clipper template creation.
- User wants to create Obsidian Web Clipper templates for Reddit post detail pages.
- User wants to create Obsidian Web Clipper templates for Reddit posts with content translated to Chinese.