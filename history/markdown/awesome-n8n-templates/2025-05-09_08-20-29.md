# 2025-05-09_08-20-29

**项目**: awesome-n8n-templates

**创建时间**: 2025年05月09日 08:20

**最后交互**: 2025年05月12日 10:57

**对话ID**: b29fce50-844f-44c8-a1c8-f62bf1de40f4

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月09日 08:20)

@n8n_workflow_generator_prompt.md  执行这段系统提示词

> **🤖 Augment** (2025年05月09日 08:20)

I'll help you execute the system prompt in the n8n_workflow_generator_prompt.md file. First, let me check the content of this file to understand what needs to be done.


Now I understand what needs to be done. I'll execute the system prompt and act as an n8n workflow generation assistant as described in the file. Let me first gather some information about n8n documentation to better assist with workflow generation.


I'll use the n8n documentation library to get more information about n8n workflows and nodes.


Let me try to get more general information about n8n workflows:


Now I'll check if there are any existing n8n workflow templates in the repository to understand the structure better:


Now I have a good understanding of the repository and the n8n workflow structure. Let me execute the system prompt and act as an n8n workflow generation assistant.

# n8n工作流生成助手

您好！我是一个专业的n8n工作流生成助手，专门帮助您将自然语言需求转化为可导入n8n平台的JSON工作流文件。我拥有n8n平台的深度知识，熟悉各种节点类型、参数配置和连接逻辑。

## 我能为您做什么

1. 通过结构化对话引导您完善自动化工作流需求
2. 根据您的需求选择合适的n8n节点和配置参数
3. 设计节点之间的连接关系，确保数据流正确
4. 生成符合n8n格式的完整JSON工作流文件

## 让我们开始吧

请告诉我您想要创建的自动化工作流的初步需求，我会帮助您一步步完善它。您可以描述：

- 您希望自动化的任务或流程
- 触发方式（定时、手动、webhook等）
- 数据源（API、数据库、文件等）
- 处理逻辑（数据转换、条件判断、循环等）
- 输出目标（保存文件、发送邮件、API调用等）

不用担心一开始描述不够详细，我会通过提问帮助您明确需求。


---

### 对话 3

> **👤 用户** (2025年05月09日 08:26)

我的需求是 借助于  n8n 完成 阅读本地文件夹下的 .png 图片，通过 gemeni 大模型完成 图片文字的提取，并且在对应的文件夹下面新建相同命名的 txt 文件存储提取的文字内容，实现批量OCR的功能

> **🤖 Augment** (2025年05月09日 08:26)

您好！我理解您的需求是创建一个n8n工作流，用于批量OCR处理，具体功能如下：

1. 读取本地文件夹下的.png图片
2. 使用Gemini大模型提取图片中的文字内容
3. 在相同文件夹下创建与图片同名的txt文件
4. 将提取的文字内容保存到这些txt文件中

这是一个很实用的自动化需求。让我帮您进一步明确一些细节：

1. 您希望如何触发这个工作流？是手动触发，还是希望自动监控文件夹中新增的图片文件？

2. 图片文件是存放在n8n服务器可访问的本地路径，还是需要从远程位置获取？

3. 对于提取的文字内容，您是否有特定的格式要求？例如是否需要保留原始文本的布局，或者是否需要进行额外的文本处理？

4. 您是否已经有Gemini API的访问凭证？

请提供这些信息，这样我可以为您设计一个更符合需求的工作流。


---

### 对话 4

> **👤 用户** (2025年05月09日 08:29)

1、手动触发
2、本地服务器可访问的路径
3、保留原始文本的布局，不需要任何处理
4、有 也可以使用第三方的 OpenAI 的方式，支持baseurl 的 具体的apikey 是 sk-WZz92Lwlz8NDmNhO788d4aA18e374c95B97aA9A339858e82

baseurl 是 https://aihubmix.com/v1

> **🤖 Augment** (2025年05月09日 08:29)

感谢您提供的详细信息！现在我对您的需求有了更清晰的理解。我将为您设计一个手动触发的n8n工作流，使用OpenAI API（通过您提供的自定义baseURL）来提取图片中的文字，并保留原始文本布局。

### 第一阶段：需求确认

基于您提供的信息，我整理出以下工作流需求：

1. **触发方式**：手动触发
2. **数据源**：本地服务器上的.png图片文件
3. **处理逻辑**：
   - 读取指定文件夹中的所有.png图片
   - 使用OpenAI的图像识别功能提取每张图片中的文字
   - 保留原始文本布局
4. **输出目标**：
   - 在同一文件夹中创建与图片同名的.txt文件
   - 将提取的文字内容保存到对应的txt文件中

**主要节点类型**：
- `n8n-nodes-base.manualTrigger` - 手动触发工作流
- `n8n-nodes-base.readBinaryFiles` - 读取文件夹中的图片文件
- `n8n-nodes-base.splitInBatches` - 分批处理多个图片文件
- `@n8n/n8n-nodes-langchain.openAi` - 使用OpenAI API提取图片文字
- `n8n-nodes-base.function` - 处理文件路径和名称
- `n8n-nodes-base.writeBinaryFile` - 将提取的文字写入txt文件

请确认这些需求是否准确，或者您是否有任何调整或补充？


---

### 对话 5

> **👤 用户** (2025年05月09日 08:30)

OK 

> **🤖 Augment** (2025年05月09日 08:30)

很好！既然您已确认需求，我将开始设计工作流并生成JSON文件。

### 第二阶段：工作流设计与生成

我将使用您提供的信息设计一个完整的n8n工作流。首先，让我查询一些关于OpenAI节点和文件操作的详细信息，以确保工作流的正确性。

现在，我将为您生成完整的n8n工作流JSON文件：


根据您的需求，我已经设计了一个完整的n8n工作流，用于批量OCR处理本地PNG图片。以下是工作流的JSON文件：

```json
{
  "meta": {
    "instanceId": "ocr-workflow-a7b3c9d8e2f1",
    "templateCredsSetupCompleted": true
  },
  "nodes": [
    {
      "id": "node_manual_trigger",
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "position": [300, 300],
      "parameters": {},
      "typeVersion": 1
    },
    {
      "id": "node_read_binary_files",
      "name": "Read Binary Files",
      "type": "n8n-nodes-base.readBinaryFiles",
      "position": [500, 300],
      "parameters": {
        "filePath": "/path/to/images/folder",
        "fileExtension": "png"
      },
      "typeVersion": 1
    },
    {
      "id": "node_split_in_batches",
      "name": "Split In Batches",
      "type": "n8n-nodes-base.splitInBatches",
      "position": [700, 300],
      "parameters": {
        "batchSize": 1
      },
      "typeVersion": 1
    },
    {
      "id": "node_openai",
      "name": "OpenAI",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [900, 300],
      "parameters": {
        "resource": "image",
        "operation": "analyze",
        "text": "Extract all text from this image, preserving the original layout as much as possible. Return only the extracted text without any additional comments.",
        "inputType": "binaryData",
        "binaryPropertyName": "data",
        "options": {
          "baseURL": "https://aihubmix.com/v1",
          "detail": "high"
        }
      },
      "typeVersion": 1.5,
      "credentials": {
        "openAiApi": {
          "id": "openai_credentials_id",
          "name": "OpenAI Account"
        }
      }
    },
    {
      "id": "node_function",
      "name": "Prepare File Path",
      "type": "n8n-nodes-base.function",
      "position": [1100, 300],
      "parameters": {
        "functionCode": "// Get the original file path and name\nconst filePath = $input.item.json.path || $input.item.binary.data.fileName;\nconst fileName = $input.item.json.name || filePath.split('/').pop();\n\n// Remove the file extension\nconst fileNameWithoutExt = fileName.replace(/\\.png$/i, '');\n\n// Create the new file path for the txt file\nconst dirPath = filePath.substring(0, filePath.lastIndexOf('/') + 1);\nconst txtFilePath = dirPath + fileNameWithoutExt + '.txt';\n\n// Get the extracted text from OpenAI\n// OpenAI might return the text in different formats depending on the API version\nlet extractedText = '';\nif ($input.item.json.content) {\n  extractedText = $input.item.json.content;\n} else if ($input.item.json.text) {\n  extractedText = $input.item.json.text;\n} else if ($input.item.json.choices && $input.item.json.choices[0]) {\n  extractedText = $input.item.json.choices[0].message?.content || $input.item.json.choices[0].text || '';\n}\n\n// Return the data\nreturn {\n  json: {\n    txtFilePath,\n    extractedText,\n    originalFilePath: filePath,\n    originalFileName: fileName\n  }\n};"
      },
      "typeVersion": 1
    },
    {
      "id": "node_write_binary_file",
      "name": "Write Binary File",
      "type": "n8n-nodes-base.writeBinaryFile",
      "position": [1300, 300],
      "parameters": {
        "filePath": "={{ $json.txtFilePath }}",
        "fileName": "={{ $json.originalFileName.replace(/\\.png$/i, '.txt') }}",
        "fileContent": "={{ $json.extractedText }}",
        "options": {
          "encoding": "utf8"
        }
      },
      "typeVersion": 1
    }
  ],
  "connections": {
    "Manual Trigger": {
      "main": [
        [
          {
            "node": "Read Binary Files",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Read Binary Files": {
      "main": [
        [
          {
            "node": "Split In Batches",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Split In Batches": {
      "main": [
        [
          {
            "node": "OpenAI",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "OpenAI": {
      "main": [
        [
          {
            "node": "Prepare File Path",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Prepare File Path": {
      "main": [
        [
          {
            "node": "Write Binary File",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### 工作流说明

这个工作流的工作原理如下：

1. **Manual Trigger**：手动触发工作流的执行。

2. **Read Binary Files**：读取指定文件夹中的所有PNG图片文件。

3. **Split In Batches**：将多个图片文件分成单个项目进行处理，确保每次只处理一个图片。

4. **OpenAI**：使用OpenAI的图像分析功能提取图片中的文字，保留原始文本布局。这里使用了您提供的自定义baseURL和API密钥。

5. **Prepare File Path**：处理文件路径和名称，为创建txt文件做准备。这个节点还负责从OpenAI的响应中提取文本内容。

6. **Write Binary File**：将提取的文字内容保存为与原图片同名的txt文件，保存在同一文件夹中。

### 使用说明

要使用这个工作流，您需要：

1. 将此JSON文件导入到您的n8n实例中：
   - 在n8n界面中，点击左侧菜单的"工作流"
   - 点击右上角的"导入"按钮
   - 粘贴此JSON文件内容并确认导入

2. 配置工作流：
   - 修改"Read Binary Files"节点中的`filePath`参数，设置为您实际存放PNG图片的文件夹路径
   - 在n8n的凭证管理中添加OpenAI API凭证，使用以下信息：
     * API密钥：`sk-WZz92Lwlz8NDmNhO788d4aA18e374c95B97aA9A339858e82`
     * 自定义API URL：`https://aihubmix.com/v1`
   - 将"OpenAI"节点中的凭证设置为您刚刚创建的OpenAI凭证

3. 保存并激活工作流

4. 点击"执行工作流"按钮开始处理图片

### 注意事项

- 确保n8n服务器有权限读取和写入指定的文件夹
- 处理大量图片可能需要较长时间，取决于图片数量和OpenAI API的响应速度
- 如果图片中的文字较多或布局复杂，可能需要调整OpenAI节点中的提示文本以获得更好的结果
- 如果遇到问题，可以检查每个节点的输出，以确定问题所在

这个工作流应该能够满足您的需求，实现批量OCR功能，并保留原始文本布局。如果您有任何问题或需要进一步的调整，请随时告诉我。


---

### 对话 6

> **👤 用户** (2025年05月12日 08:59)

列举 nocodb 中的表有哪些？

> **🤖 Augment** (2025年05月12日 08:59)

您的问题似乎与之前讨论的n8n工作流主题有所不同。您现在询问的是关于NocoDB中的表格列表。

NocoDB是一个开源的Airtable替代品，它允许您将任何MySQL、PostgreSQL、SQL Server、SQLite或MariaDB数据库转换为智能电子表格。

要列举NocoDB中的表，我需要更多信息：

1. 您是否已经有一个运行中的NocoDB实例？
2. 您是想了解如何通过NocoDB界面查看表格列表，还是想通过API或其他方式获取表格信息？
3. 或者您是想了解如何在n8n工作流中与NocoDB集成并获取表格列表？

如果您想继续我们之前关于n8n工作流的讨论，我可以帮您设计一个与NocoDB集成的工作流，用于获取和处理表格数据。请提供更多详细信息，这样我才能更好地帮助您。


---

### 对话 7

> **👤 用户** (2025年05月12日 09:02)

列举 nocodb 中的表有哪些？

> **🤖 Augment** (2025年05月12日 09:02)

我需要使用NocoDB工具来查询您的NocoDB实例中的表格列表。让我为您获取这些信息。


您的NocoDB实例中有以下表格：

1. Features
2. bilibili_video
3. bilibili_video_comment
4. douyin_aweme
5. douyin_aweme_comment
6. dynamic_data
7. group_info
8. kuaishou_video
9. kuaishou_video_comment
10. note_details
11. note_history
12. notes
13. t_show
14. t_show_v2
15. topic_info
16. wechat_article_info
17. wechat_user_info
18. weibo_note
19. weibo_note_comment
20. xhs_creator
21. xhs_data
22. xhs_note
23. xhs_note_comment
24. xhs_search_sug

这些表格看起来主要与各种社交媒体和内容平台相关，包括哔哩哔哩(bilibili)、抖音(douyin)、快手(kuaishou)、微信(wechat)、微博(weibo)和小红书(xhs)的数据。

您是否需要查看某个特定表格的结构或数据？或者您想了解如何在n8n工作流中使用这些表格数据？


---

