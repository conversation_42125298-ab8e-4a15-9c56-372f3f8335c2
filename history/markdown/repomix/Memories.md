# repomix 的记忆

# 用户偏好
- User prefers responses in Chinese language.
- 用户要求用中文回复技术分析内容。
- 用户偏好将大型任务分成多个阶段完成，而不是一次性完成所有内容。
- 用户希望在开发过程中创建并维护checklist.md文件来跟踪项目进度。
- 用户希望tutorial-generator页面的展示效果与网站首页保持一致。
- 用户希望在代码中添加日志输出以便调试问题。

# 用户需求
- 用户对使用AI编程软件（如Cursor）进行前端设计感兴趣，希望获得有效的AI交互提示词。
- User wants documentation about Repomix integration with large language models formatted as Markdown with specific sections (description, use cases, value considerations) and organized in a docs directory.
- 用户希望在现有项目中集成大模型能力，实现GitHub项目的自动化教程生成功能。
- 用户不需要GitHub仓库获取模块，因为现有项目已能获取并结构化GitHub代码，只需完成教程生成功能。
- 在规划代码时不要使用Python代码，要与现有项目的技术选型保持一致。
- 用户希望添加路由展示AI生成的教程列表，每个教程可点击查看详细文档，布局类似现有guide页面，有目录和内容。
- 用户希望将教程生成表单直接集成到主页面，实现先pack后生成教程的流程，而不是分开的页面。
- 用户对界面交互不满意，希望系统设计考虑未来添加更多功能（除教程生成外）的扩展点。
- 用户希望将动作面板设计方案添加到checklist.md文件中，并优先于教程展示功能实现。
- 用户对OpenAI大模型API调用教程感兴趣，可能需要在项目中集成OpenAI功能。
- 用户希望在项目中使用.env文件管理环境变量，项目应该从.env文件获取API密钥。
- 用户希望将大模型调用的Prompt参数和输出结果记录到单独的日志文件中，而不输出到终端。
- 用户希望参考VitePress的guide页面实现方式来实现教程详情页。
- 用户认为在 website/client/.vitepress/config/configZhCn.ts 文件中可能需要进行教程页面的配置。