# 2025-04-17_08-39-19

**项目**: rules_template

**创建时间**: 2025年04月17日 08:39

**最后交互**: 2025年04月17日 09:51

**对话ID**: f691010e-8afa-4f4f-a1a8-0acb584b71db

---

### 对话 1

> **👤 用户**

Provide a short summary of this project

> **🤖 Augment**

# Project Summary

This project appears to be a multi-agent question generation system designed for creating scientific and mathematical questions. The system uses a network of LLM agents working together in a coordinated workflow.

Key components include:

1. **Architecture**: A multi-agent system with specialized roles including prompt engineering, question generation, solution generation, and solution aggregation/validation.

2. **Development Structure**: Follows Agile methodology with a phased implementation plan (core engine, multi-agent network, refinement).

3. **Documentation Framework**: Extensive documentation system with memory files for maintaining context across sessions, including product requirements, architecture, technical specifications, and task planning.

4. **Directory Structure**: Well-organized with separate directories for source code, tests, documentation, configuration, and data.

5. **Custom Rules System**: Cross-platform compatibility with AI coding assistants like Cursor, CLINE, RooCode, and Windsurf through custom rule files.

6. **MCP Integration**: Support for Model Context Protocol (MCP) servers to extend capabilities with additional tools and resources.

The project aims to generate high-quality scientific and mathematical questions with validated solutions through a sophisticated orchestration of AI agents.


---

### 对话 2

> **👤 用户**

@README.md 阅读这份文档，帮我梳理这个项目的实践路径，分别对应  Cursor 、cline、roocline

> **🤖 Augment**

我将帮您梳理这个项目在 Cursor、CLINE 和 RooCode（您提到的"roocline"，我假设是指 RooCode）中的实践路径。

## 项目实践路径总结

这个项目是一个跨平台的 AI 编码助手规则模板，旨在提高 AI 编码助手的输出质量。以下是各平台的实践路径：

### Cursor 实践路径

1. **基础设置**：
   - 将 `.cursor/rules/` 目录复制到您项目的根目录
   - 这个目录包含所有原始规则文件，其他平台的规则文件通过符号链接指向这些文件

2. **规则文件结构**：
   ```
   .cursor/rules/
   ├── rules.mdc
   ├── plan.mdc
   ├── implement.mdc
   ├── debug.mdc
   ├── memory.mdc
   ├── directory-structure.mdc
   └── 其他规则文件
   ```

3. **自定义模式**：
   - 项目引入了新的自定义模式以节省令牌使用量
   - 这些模式包括：Chat、Write 和 MCP

### CLINE 实践路径

1. **基础设置**：
   - 将 `.clinerules/` 目录复制到您项目的根目录
   - 这些文件是通过符号链接指向 `.cursor/rules/` 中的原始文件

2. **规则文件结构**：
   ```
   .clinerules/
   ├── rules
   ├── plan
   ├── implement
   ├── debug
   ├── memory
   ├── directory-structure
   └── 其他规则文件
   ```

3. **模式使用**：
   - 在 CLINE 中使用 **PLAN Mode** 和 **Act Mode**
   - 文档提到 CLINE 目前不支持自定义模式，因此没有像 Cursor 和 RooCode 那样的令牌节省功能

### RooCode 实践路径

1. **基础设置**：
   - 将 `.roo/` 目录复制到您项目的根目录
   - 这些文件也是通过符号链接指向 `.cursor/rules/` 中的原始文件

2. **规则文件结构**：
   - 规则被分离到模式特定的规则文件中：`.roo/rules-{mode}/`
   - 这种结构允许按需加载规则，节省令牌使用量

3. **自定义模式设置**：
   - **Chat 模式**：
     - 无文件读写和命令执行功能，类似传统 ChatGPT
     - 在 Prompts 部分创建新模式，命名为"Chat"，slug 为"chat"
     - 不启用任何工具
   
   - **Write 模式**：
     - 具有读取、写入和运行命令的功能
     - 在 Prompts 部分创建新模式，命名为"Write"，slug 为"write"
     - 启用三个工具：Read Files、Edit Files、Run Command

4. **模式使用**：
   - 在 RooCode 中使用 **Architect Mode**、**Code Mode** 和 **Debug Mode**

## 共同特点

1. **跨平台兼容性**：可以在任何 IDE 和任何数量的不同计算机上使用
2. **共享内存库**：所有 AI 助手共享同一个内存库，保持上下文一致性
3. **基于软件工程原则**：遵循敏捷开发工作流程和软件工程最佳实践
4. **令牌使用优化**：通过按需加载规则和使用精简模式来减少令牌使用量
5. **目录结构**：项目遵循一个明确的目录结构，包括 docs/、tasks/、src/ 等

这个项目的核心价值在于提供了一个结构化的、基于软件工程原则的 AI 编码助手规则模板，可以跨平台使用，并且通过优化的规则组织和自定义模式来节省令牌使用量。


---

### 对话 3

> **👤 用户**

除了 .cursor .clinerules .roo 文件夹呢？

> **🤖 Augment**

除了 `.cursor`、`.clinerules` 和 `.roo` 文件夹外，项目还建议创建以下目录结构：

## 核心目录结构

```
项目根目录/
├── docs/                      # 项目文档
│   ├── literature/            # 文献调查和研究
│   ├── architecture.md        # 系统架构文档
│   ├── technical.md           # 技术规范文档
│   └── product_requirement_docs.md  # 产品需求文档
│
├── tasks/                     # 任务管理
│   ├── rfc/                   # 各个任务的详细规范(RFC)
│   ├── active_context.md      # 当前开发上下文
│   └── tasks_plan.md          # 任务计划
│
├── src/                       # 主要源代码
├── test/                      # 测试套件
├── utils/                     # 工具脚本或库
├── config/                    # 配置文件
└── data/                      # 数据资源
```

## 详细说明

### 1. `docs/` 目录

这个目录包含所有项目文档，是项目"记忆"的核心部分：

- **`literature/`**：存放文献调查和研究文档，通常使用 LaTeX 格式（`*.tex`）
- **`architecture.md`**：描述系统架构、组件关系和依赖关系
- **`technical.md`**：记录开发环境、技术栈、关键技术决策和设计模式
- **`product_requirement_docs.md`**：产品需求文档(PRD)，定义项目的核心需求和目标

### 2. `tasks/` 目录

这个目录用于任务管理和当前开发上下文：

- **`rfc/`**：包含各个任务的详细规范(RFC)，通常使用 LaTeX 格式
- **`active_context.md`**：记录当前开发重点、活跃决策和最近变更
- **`tasks_plan.md`**：任务计划，列出待完成的任务和优先级

### 3. 其他核心目录

- **`src/`**：主要源代码目录
- **`test/`**：测试套件，包含单元测试、集成测试等
- **`utils/`**：工具脚本或库
- **`config/`**：配置文件
- **`data/`**：数据资源

## 特定项目的目录结构

根据项目类型，可能会有不同的目录结构建议。例如：

### PyTorch 项目结构

```
project_root/
├── data/
│   ├── raw/
│   ├── processed/
│   └── ...
├── models/
│   ├── layers.py
│   ├── networks.py
│   ├── losses.py
│   └── ...
├── src/
│   ├── data/
│   │   ├── datasets.py
│   │   ├── dataloaders.py
│   │   └── transforms.py
│   └── ...
├── notebooks/
└── ...
```

### LangGraph 项目结构

```
my_langgraph_project/
├── data/
├── src/
│   ├── components/
│   ├── graphs/
│   ├── utils/
│   ├── schemas/
│   └── main.py
├── tests/
└── ...
```

## 记忆文件工作流

这些目录和文件形成了一个结构化的"记忆系统"，它们之间有明确的层次关系：

1. 产品需求文档(PRD) → 技术规范和架构文档
2. 技术规范和架构文档 → 任务计划
3. 任务计划 → 当前活动上下文
4. 当前活动上下文 → 错误文档和经验教训

这种结构确保了 AI 助手能够维持项目上下文，并按照软件工程最佳实践进行工作。

## 使用方法

1. 创建上述目录结构
2. 在第一次与 AI 助手交互时，给出以下提示：
   > Follow Custom Prompt to initialize and document the project in Memory Files following the structure and instructions for documenting in Memory Files. Write everything about the project in Memory Files, build a good context for the project.

这将指导 AI 助手初始化项目并在记忆文件中建立良好的项目上下文。


---

### 对话 4

> **👤 用户**

能否创建一个脚本，一键初始化某个项目

> **🤖 Augment**

我可以为您创建一个 Python 脚本，用于一键初始化项目目录结构和配置文件。这个脚本将创建所有必要的目录和文件，包括 `.cursor`、`.clinerules` 和 `.roo` 目录及其规则文件。

让我使用 `firecrawl_scrape_firecrawl-mcp` 工具来获取更多关于项目结构的信息，以确保脚本的完整性：


看起来我无法直接访问 GitHub 上的模板仓库。不过，根据之前提供的信息，我可以创建一个初始化脚本。以下是一个 Python 脚本，用于一键初始化项目：

````python path=init_project.py mode=EDIT
#!/usr/bin/env python3
"""
Project Initializer for AI Coding Assistant Rules Template
This script creates the necessary directory structure and rule files for using
AI coding assistants like Cursor, CLINE, and RooCode.
"""

import os
import shutil
import argparse
import sys
from pathlib import Path
import textwrap

# Base rule files content
RULES_MDC = """---
description: Always include these rules.
globs: 
alwaysApply: true
---
# Rules to Follow
This document outlines the fundamental principles, required files, workflow structure, and essential procedures that govern this project.

## Core Principles
1. **Clarity First**: Always prioritize clarity over cleverness.
2. **Documentation**: Document all key decisions and implementations.
3. **Systematic Approach**: Follow the five-phase workflow for all tasks.
4. **Memory Management**: Maintain and update memory files regularly.
5. **Directory Structure**: Adhere to the established directory structure.

## Five-Phase Workflow
1. **Requirements and Clarifications**
2. **Design and Planning**
3. **Implementation**
4. **Testing and Validation**
5. **Documentation and Review**

Always load the active context from tasks/active_context.md before starting work.
"""

PLAN_MDC = """---
description: Always include these rules.
globs: 
alwaysApply: true
---
# Planning Workflow

1. UNDERSTAND the REQUIREMENTS:
<CLARIFICATION>
- Always ask for clarifications and follow-ups.
- Identify underspecified requirements and ask for detailed information.
- Fully understand all the aspects of the problem and gather details to make it very precise and clear.
- Ask towards all the hypothesis and assumptions needed to be made. Remove all the ambiguities and uncertainties.
- Suggest solutions that I didn't think about, i.e. anticipate my needs and things to be specified.
- Only after having hundred percent clarity and confidence, proceed for SOLUTION.
</CLARIFICATION>

2. DESIGN the SOLUTION:
<DESIGN>
- Provide a high-level design of the solution.
- Break down the problem into smaller, manageable components.
- Identify the key algorithms, data structures, and patterns to be used.
- Consider alternative approaches and trade-offs.
- Discuss potential edge cases and how to handle them.
</DESIGN>

3. PLAN the IMPLEMENTATION:
<PLAN>
- Create a step-by-step plan for implementing the solution.
- Identify dependencies between components.
- Estimate the complexity and effort required for each step.
- Prioritize tasks based on dependencies and importance.
- Consider potential roadblocks and how to mitigate them.
</PLAN>

4. DOCUMENT the PLAN:
<DOCUMENT>
- Update the tasks/tasks_plan.md file with the new plan.
- Document key design decisions and rationale.
- Create or update relevant RFC documents in tasks/rfc/.
- Ensure the plan aligns with the overall project architecture.
</DOCUMENT>
"""

IMPLEMENT_MDC = """---
description: Always include these rules.
globs: 
alwaysApply: true
---
# Implementation Workflow

1. PREPARE the ENVIRONMENT:
<PREPARE>
- Ensure all necessary dependencies are installed.
- Set up the development environment as needed.
- Review the implementation plan from the planning phase.
- Load the active context from tasks/active_context.md.
</PREPARE>

2. IMPLEMENT INCREMENTALLY:
<IMPLEMENT>
- Follow the step-by-step plan created during the planning phase.
- Implement one component at a time.
- Write clean, readable, and maintainable code.
- Add appropriate comments and documentation.
- Follow the established coding standards and patterns.
</IMPLEMENT>

3. TEST CONTINUOUSLY:
<TEST>
- Write unit tests for each component.
- Ensure all tests pass before moving to the next component.
- Consider edge cases and handle them appropriately.
- Perform integration testing as needed.
</TEST>

4. REFACTOR and OPTIMIZE:
<REFACTOR>
- Review the implementation for potential improvements.
- Refactor code for better readability and maintainability.
- Optimize for performance where necessary.
- Ensure the code follows the DRY (Don't Repeat Yourself) principle.
</REFACTOR>

5. DOCUMENT the IMPLEMENTATION:
<DOCUMENT>
- Update the active_context.md file with the implementation details.
- Document any deviations from the original plan and the reasons for them.
- Update technical documentation as needed.
</DOCUMENT>
"""

DEBUG_MDC = """---
description: Always include these rules.
globs: 
alwaysApply: true
---
# Debugging Workflow

1. UNDERSTAND the ISSUE:
<UNDERSTAND>
- Clearly define the problem or bug.
- Gather all relevant information about the issue.
- Identify the expected behavior versus the actual behavior.
- Determine the conditions under which the issue occurs.
</UNDERSTAND>

2. REPRODUCE the ISSUE:
<REPRODUCE>
- Create a minimal reproducible example.
- Identify the steps to consistently reproduce the issue.
- Document the environment and conditions where the issue occurs.
</REPRODUCE>

3. ISOLATE the CAUSE:
<ISOLATE>
- Use debugging tools and techniques to trace the issue.
- Narrow down the potential causes.
- Identify the specific component or code section causing the issue.
- Understand the root cause of the problem.
</ISOLATE>

4. FIX the ISSUE:
<FIX>
- Develop a solution that addresses the root cause.
- Implement the fix in a clean and maintainable way.
- Ensure the fix doesn't introduce new issues.
- Test the fix thoroughly to ensure it resolves the issue.
</FIX>

5. DOCUMENT the RESOLUTION:
<DOCUMENT>
- Update the error-documentation.mdc file with the issue and its resolution.
- Document any lessons learned in the lessons-learned.mdc file.
- Update the active_context.md file as needed.
</DOCUMENT>
"""

MEMORY_MDC = """---
description: ALWAYS INCLUDE to HAVE Project Context
globs: 
alwaysApply: true
---
# Memory Files Structure
This outlines the fundamental principles, required files, workflow structure, and essential procedures that govern documentation, and maintaining a memory using file system.
The Memory Files consists of required core files and optional context files. Files build upon each other in a clear hierarchy:

```mermaid
flowchart TD
    PB[ [product_requirement_docs.md](mdc:docs/product_requirement_docs.md) ] --> PC[ [technical.md](mdc:docs/technical.md) ]
    PB --> SP[ [architecture.md](mdc:docs/architecture.md) ]

    SP --> TC[ [tasks_plan.md](mdc:tasks/tasks_plan.md) ]
    PC --> TC
    PB --> TC
    
    TC --> AC[ [active_context.md](mdc:tasks/active_context.md) ]

    AC --> ER[ [error-documentation.mdc](mdc:.cursor/rules/error-documentation.mdc) ]
    AC --> LL[ [lessons-learned.mdc](mdc:.cursor/rules/lessons-learned.mdc) ]
    
    subgraph LIT[ docs/literature ]
        L1[Research 1]
        L2[Research 2]
    end
    
    subgraph RFC[ tasks/rfc ]
        R1[RFC 1]
        R2[RFC 2]
    end
    
    PC --o LIT
    TC --o RFC
```

## Core Files (Required)
1. **docs/product_requirement_docs.md**: Product Requirements Document (PRD)
   - Project goals and objectives
   - User stories and requirements
   - Success criteria and metrics
   - Constraints and assumptions

2. **docs/technical.md**: Technical Specification
   - Development environment setup
   - Technology stack and dependencies
   - API specifications
   - Data models and schemas

3. **docs/architecture.md**: Architecture Document
   - System components and their relationships
   - Data flow diagrams
   - Design patterns used
   - Scalability and performance considerations

4. **tasks/tasks_plan.md**: Tasks Plan
   - Prioritized list of tasks
   - Task dependencies
   - Timeline and milestones
   - Resource allocation

5. **tasks/active_context.md**: Active Context
   - Current development focus
   - Active decisions and recent changes
   - Known issues and blockers
   - Short-term goals

## Context Files (Optional)
1. **.cursor/rules/error-documentation.mdc**: Error Documentation
   - Common errors and their solutions
   - Debugging strategies
   - System limitations

2. **.cursor/rules/lessons-learned.mdc**: Lessons Learned
   - Best practices discovered
   - Anti-patterns to avoid
   - Performance optimizations
   - Refactoring insights

3. **docs/literature/**: Research and Literature
   - Academic papers
   - Industry reports
   - Competitive analysis
   - Market research

4. **tasks/rfc/**: Request for Comments
   - Detailed specifications for major features
   - Design proposals
   - Implementation strategies
   - Review comments and decisions
"""

DIRECTORY_STRUCTURE_MDC = """---
description: directory structure to follow
globs: 
alwaysApply: false
---
# Directory Structure
```mermaid
flowchart TD
    Root[Project Root]
    Root --> Docs[docs/]
    Root --> Tasks[tasks/]
    Root --> Cursor[.cursor/rules/]
    Root --> CLINE[.clinerules]
    Root --> SourceCode[src/]
    Root --> Test[test/]
    Root --> Utils[utils/]
    Root --> Config[config/]
    Root --> Data[data/]
    Root --> Other[Other Directories]
```

## Directory Descriptions

1. **docs/**: Project documentation
   - Product requirements
   - Technical specifications
   - Architecture documents
   - API documentation
   - User guides

2. **tasks/**: Task management
   - Task plans
   - Active context
   - RFCs (Request for Comments)
   - Progress tracking

3. **.cursor/rules/**: Cursor AI rules
   - Custom prompts
   - Workflow definitions
   - Memory management
   - Directory structure

4. **.clinerules/**: CLINE AI rules
   - Similar to Cursor rules but formatted for CLINE

5. **src/**: Source code
   - Main application code
   - Modules and components
   - Libraries and frameworks

6. **test/**: Testing
   - Unit tests
   - Integration tests
   - End-to-end tests
   - Test fixtures and utilities

7. **utils/**: Utilities
   - Helper scripts
   - Development tools
   - Automation scripts

8. **config/**: Configuration
   - Environment configurations
   - Application settings
   - Build configurations

9. **data/**: Data resources
   - Sample data
   - Datasets
   - Data processing scripts
"""

ERROR_DOCUMENTATION_MDC = """---
description: Document major failure points in this project and they were solved. To be filled by AI.
globs: 
alwaysApply: false
---
# Error Documentation

This document tracks significant errors, bugs, and challenges encountered during the project, along with their resolutions. It serves as a knowledge base for troubleshooting and avoiding similar issues in the future.

## Error Log

### [Error ID]: [Brief Error Description]
- **Date**: [Date when the error was encountered]
- **Component**: [Affected component or module]
- **Description**: [Detailed description of the error]
- **Root Cause**: [Analysis of what caused the error]
- **Resolution**: [How the error was fixed]
- **Prevention**: [Steps to prevent similar errors in the future]

## Common Issues and Solutions

### Development Environment
- **Issue**: [Common development environment issue]
- **Solution**: [How to resolve it]

### Build and Deployment
- **Issue**: [Common build or deployment issue]
- **Solution**: [How to resolve it]

### Runtime
- **Issue**: [Common runtime issue]
- **Solution**: [How to resolve it]
"""

LESSONS_LEARNED_MDC = """---
description: Document lessons learned during the project. To be filled by AI.
globs: 
alwaysApply: false
---
# Lessons Learned

This document captures insights, best practices, and lessons learned throughout the project. It serves as a knowledge repository to improve future development and decision-making.

## Technical Insights

### Architecture
- [Insight about system architecture]
- [Best practice discovered]

### Performance
- [Performance optimization technique]
- [Bottleneck identified and solution]

### Code Quality
- [Code organization insight]
- [Refactoring strategy that worked well]

## Process Improvements

### Development Workflow
- [Workflow improvement]
- [Collaboration technique]

### Testing Strategy
- [Effective testing approach]
- [Test coverage insight]

## Tools and Technologies

### Framework Usage
- [Framework-specific best practice]
- [Integration insight]

### Development Tools
- [Tool usage optimization]
- [Configuration improvement]
"""

PRODUCT_REQUIREMENT_DOCS_MD = """# Product Requirements Document (PRD)

## Overview
[Brief description of the product and its purpose]

## Goals and Objectives
- [Primary goal 1]
- [Primary goal 2]
- [Secondary goal 1]

## User Stories
1. As a [user type], I want to [action] so that [benefit].
2. As a [user type], I want to [action] so that [benefit].
3. As a [user type], I want to [action] so that [benefit].

## Requirements

### Functional Requirements
- [Requirement 1]
- [Requirement 2]
- [Requirement 3]

### Non-Functional Requirements
- **Performance**: [Performance requirements]
- **Security**: [Security requirements]
- **Usability**: [Usability requirements]
- **Reliability**: [Reliability requirements]

## Constraints and Assumptions
- [Constraint 1]
- [Constraint 2]
- [Assumption 1]
- [Assumption 2]

## Success Criteria
- [Criterion 1]
- [Criterion 2]
- [Criterion 3]

## Timeline and Milestones
- [Milestone 1]: [Date]
- [Milestone 2]: [Date]
- [Milestone 3]: [Date]
"""

TECHNICAL_MD = """# Technical Specification

## Development Environment
- **Programming Language**: [Language(s)]
- **Framework**: [Framework(s)]
- **Database**: [Database(s)]
- **Version Control**: [VCS]
- **CI/CD**: [CI/CD tools]

## Technology Stack
- **Frontend**: [Frontend technologies]
- **Backend**: [Backend technologies]
- **Data Storage**: [Data storage technologies]
- **Infrastructure**: [Infrastructure technologies]

## API Specifications
### [API Name 1]
- **Endpoint**: [Endpoint]
- **Method**: [HTTP Method]
- **Request Format**: [Request format]
- **Response Format**: [Response format]
- **Authentication**: [Authentication method]

### [API Name 2]
- **Endpoint**: [Endpoint]
- **Method**: [HTTP Method]
- **Request Format**: [Request format]
- **Response Format**: [Response format]
- **Authentication**: [Authentication method]

## Data Models
### [Model Name 1]
- **Fields**:
  - [Field 1]: [Type] - [Description]
  - [Field 2]: [Type] - [Description]
- **Relationships**:
  - [Relationship 1]
  - [Relationship 2]

### [Model Name 2]
- **Fields**:
  - [Field 1]: [Type] - [Description]
  - [Field 2]: [Type] - [Description]
- **Relationships**:
  - [Relationship 1]
  - [Relationship 2]

## Security Considerations
- [Security consideration 1]
- [Security consideration 2]
- [Security consideration 3]

## Performance Considerations
- [Performance consideration 1]
- [Performance consideration 2]
- [Performance consideration 3]
"""

ARCHITECTURE_MD = """# Architecture Document

## System Overview
[Brief description of the system architecture]

## Components
### [Component 1]
- **Purpose**: [Purpose of the component]
- **Responsibilities**: [Responsibilities of the component]
- **Interfaces**: [Interfaces provided by the component]
- **Dependencies**: [Dependencies of the component]

### [Component 2]
- **Purpose**: [Purpose of the component]
- **Responsibilities**: [Responsibilities of the component]
- **Interfaces**: [Interfaces provided by the component]
- **Dependencies**: [Dependencies of the component]

## Data Flow
[Description of how data flows through the system]

```mermaid
flowchart TD
    A[Component 1] --> B[Component 2]
    B --> C[Component 3]
    C --> D[Component 4]
    D --> A
```

## Design Patterns
- [Design pattern 1]: [Usage in the system]
- [Design pattern 2]: [Usage in the system]
- [Design pattern 3]: [Usage in the system]

## Scalability and Performance
- [Scalability consideration 1]
- [Scalability consideration 2]
- [Performance consideration 1]
- [Performance consideration 2]

## Security Architecture
- [Security measure 1]
- [Security measure 2]
- [Security measure 3]

## Deployment Architecture
[Description of the deployment architecture]

```mermaid
flowchart TD
    A[Client] --> B[Load Balancer]
    B --> C[Web Server 1]
    B --> D[Web Server 2]
    C --> E[Database]
    D --> E
```
"""

TASKS_PLAN_MD = """# Tasks Plan

## Current Sprint
- **Sprint Goal**: [Sprint goal]
- **Start Date**: [Start date]
- **End Date**: [End date]

## Tasks

### High Priority
1. [Task 1]
   - **Description**: [Task description]
   - **Assignee**: [Assignee]
   - **Estimated Effort**: [Effort]
   - **Dependencies**: [Dependencies]
   - **Status**: [Status]

2. [Task 2]
   - **Description**: [Task description]
   - **Assignee**: [Assignee]
   - **Estimated Effort**: [Effort]
   - **Dependencies**: [Dependencies]
   - **Status**: [Status]

### Medium Priority
1. [Task 3]
   - **Description**: [Task description]
   - **Assignee**: [Assignee]
   - **Estimated Effort**: [Effort]
   - **Dependencies**: [Dependencies]
   - **Status**: [Status]

2. [Task 4]
   - **Description**: [Task description]
   - **Assignee**: [Assignee]
   - **Estimated Effort**: [Effort]
   - **Dependencies**: [Dependencies]
   - **Status**: [Status]

### Low Priority
1. [Task 5]
   - **Description**: [Task description]
   - **Assignee**: [Assignee]
   - **Estimated Effort**: [Effort]
   - **Dependencies**: [Dependencies]
   - **Status**: [Status]

## Milestones
- [Milestone 1]:


---

