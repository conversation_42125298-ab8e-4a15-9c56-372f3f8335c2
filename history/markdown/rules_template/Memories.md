# rules_template 的记忆

# Project Initialization and AI Interaction
- User wants to know how to initialize project documentation and interact with AI for new projects.
- User wants to know how to use the @.cursor/rules/ directory (specifically the memory.mdc file) to initialize documentation for an existing project, using Cursor as an AI programming tool.

# Translation Management
- User wants to organize Chinese translations in a 'zh' directory.
- User wants to translate content from @.cursor/rules/ directory.
- User wants to translate files from @docs/ directory into Chinese and store them in a 'zh' subdirectory.
- User prefers translations to be done as separate independent files without coupling them together.

# Communication, Presentation, and Learning Preferences
- User prefers information to be presented using visual flows.
- 用户偏好使用可视化流程来呈现信息.
- User prefers structured analysis reports with visual flow diagrams when explaining complex systems like rule files.
- 用户希望以中文进行交流。
- 用户希望通过具体开发场景案例来理解规则文件之间的关联和协同工作方式，偏好包含可视化依赖关系图的分析。
`