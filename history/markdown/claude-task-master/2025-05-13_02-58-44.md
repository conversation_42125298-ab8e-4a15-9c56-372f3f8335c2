# 2025-05-13_02-58-44

**项目**: claude-task-master

**创建时间**: 2025年05月13日 02:58

**最后交互**: 2025年05月13日 03:07

**对话ID**: 3ea7ffd3-6f7f-4735-ac21-5f175b1ef4ff

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月13日 02:59)

分析该项目当前支持的模型，如何配置？ 为我生成一个基于 OpenRouter 的使用文档

> **🤖 Augment** (2025年05月13日 02:59)

I'll analyze the project's currently supported models and how to configure them, then generate documentation for using OpenRouter. Let me first gather information about the project structure and model configurations.


Let me search for more specific information about OpenRouter integration or configuration:


Let me check if there's any specific OpenRouter implementation or configuration in the project:


Based on my research, I don't see any specific OpenRouter integration in the current codebase. The project primarily uses Anthropic's Claude models and Perplexity AI for research capabilities. Let me create documentation for adding OpenRouter support to this project.

# OpenRouter Integration Documentation for Claude Task Master

## Overview

This documentation explains how to configure and use OpenRouter with Claude Task Master. OpenRouter provides access to various AI models through a unified API, allowing you to use models from different providers including Anthropic, OpenAI, and others.

## Why Use OpenRouter?

- **Access to multiple models**: Use models from Anthropic, OpenAI, Google, and other providers through a single API
- **Fallback options**: Configure fallback models if your primary model is unavailable
- **Cost optimization**: Choose models based on price-performance ratio
- **Simplified API keys**: Manage a single API key instead of multiple provider keys

## Prerequisites

- An OpenRouter account (sign up at [openrouter.ai](https://openrouter.ai))
- An OpenRouter API key
- Claude Task Master project set up

## Configuration

### 1. Environment Variables

Add the following to your `.env` file:

```
# OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=anthropic/claude-3-7-sonnet-********  # Default model
OPENROUTER_FALLBACK_MODEL=anthropic/claude-3-haiku-********  # Optional fallback model
```

### 2. Install Required Packages

```bash
npm install openai
```

OpenRouter uses the OpenAI client library with a custom base URL.

### 3. Create OpenRouter Client Utility

Create a new file `mcp-server/src/core/utils/openrouter-client-utils.js`:

```javascript
/**
 * openrouter-client-utils.js
 * Utility functions for initializing OpenRouter clients in MCP context
 */

import dotenv from 'dotenv';

// Load environment variables for CLI mode
dotenv.config();

/**
 * Get an OpenRouter client instance initialized with MCP session environment variables
 * @param {Object} [session] - Session object from MCP containing environment variables
 * @param {Object} [log] - Logger object to use (defaults to console)
 * @returns {OpenAI} OpenAI client configured for OpenRouter API
 * @throws {Error} If API key is missing or OpenAI package can't be imported
 */
export async function getOpenRouterClientForMCP(session, log = console) {
  try {
    // Extract API key from session.env or fall back to environment variables
    const apiKey =
      session?.env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;

    if (!apiKey) {
      throw new Error(
        'OPENROUTER_API_KEY not found in session environment or process.env'
      );
    }

    // Dynamically import OpenAI (it may not be used in all contexts)
    const { default: OpenAI } = await import('openai');

    // Initialize and return a new OpenAI client configured for OpenRouter
    return new OpenAI({
      apiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      defaultHeaders: {
        'HTTP-Referer': 'https://github.com/your-username/claude-task-master', // Replace with your project URL
        'X-Title': 'Claude Task Master' // Replace with your project name
      }
    });
  } catch (error) {
    log.error(`Failed to initialize OpenRouter client: ${error.message}`);
    throw error;
  }
}

/**
 * Get OpenRouter model configuration from session environment or fall back to defaults
 * @param {Object} [session] - Session object from MCP containing environment variables
 * @returns {Object} Model configuration with model, maxTokens, and temperature
 */
export function getOpenRouterModelConfig(session) {
  // Get values from session or fall back to defaults
  return {
    model: session?.env?.OPENROUTER_MODEL || process.env.OPENROUTER_MODEL || 'anthropic/claude-3-7-sonnet-********',
    fallbackModel: session?.env?.OPENROUTER_FALLBACK_MODEL || process.env.OPENROUTER_FALLBACK_MODEL || 'anthropic/claude-3-haiku-********',
    maxTokens: parseInt(session?.env?.MAX_TOKENS || process.env.MAX_TOKENS || '64000'),
    temperature: parseFloat(session?.env?.TEMPERATURE || process.env.TEMPERATURE || '0.2')
  };
}
```

### 4. Update AI Client Utils

Modify `mcp-server/src/core/utils/ai-client-utils.js` to include OpenRouter support:

```javascript
// Add import for OpenRouter utilities
import { getOpenRouterClientForMCP, getOpenRouterModelConfig } from './openrouter-client-utils.js';

// Add to the getBestAvailableAIModel function
export async function getBestAvailableAIModel(
  session,
  options = {},
  log = console
) {
  const { requiresResearch = false, claudeOverloaded = false } = options;

  // Try OpenRouter first if configured
  if (session?.env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY) {
    try {
      const client = await getOpenRouterClientForMCP(session, log);
      return { type: 'openrouter', client };
    } catch (error) {
      log.warn(`OpenRouter not available: ${error.message}`);
      // Fall through to other options
    }
  }

  // Existing code for Perplexity and Claude...
  // ...

  // If we got here, no models were successfully initialized
  throw new Error('No AI models available. Please check your API keys.');
}
```

### 5. Create Message Handler for OpenRouter

Create a new function to handle OpenRouter message creation:

```javascript
/**
 * Send a message to OpenRouter and return the response
 * @param {Object} client - OpenRouter client
 * @param {Object} modelConfig - Model configuration
 * @param {string} systemPrompt - System prompt
 * @param {string} userPrompt - User prompt
 * @returns {Promise<string>} - Response from the model
 */
export async function sendOpenRouterMessage(client, modelConfig, systemPrompt, userPrompt) {
  try {
    const response = await client.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature
    });
    
    return response.choices[0].message.content;
  } catch (error) {
    // If primary model fails and fallback is configured, try fallback
    if (modelConfig.fallbackModel && error.status !== 401) {
      console.warn(`Primary model failed, trying fallback model: ${modelConfig.fallbackModel}`);
      
      const fallbackResponse = await client.chat.completions.create({
        model: modelConfig.fallbackModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: Math.min(modelConfig.maxTokens, 32000), // Fallback models may have lower token limits
        temperature: modelConfig.temperature
      });
      
      return fallbackResponse.choices[0].message.content;
    }
    
    throw error;
  }
}
```

## Usage Examples

### Basic Usage

```javascript
import { getOpenRouterClientForMCP, getOpenRouterModelConfig } from '../utils/openrouter-client-utils.js';
import { sendOpenRouterMessage } from '../utils/ai-client-utils.js';

async function generateTasksWithOpenRouter(session, prompt) {
  try {
    // Get OpenRouter client and model configuration
    const client = await getOpenRouterClientForMCP(session);
    const modelConfig = getOpenRouterModelConfig(session);
    
    // System prompt for task generation
    const systemPrompt = 'You are a helpful assistant that generates tasks based on project requirements.';
    
    // Send message to OpenRouter
    const response = await sendOpenRouterMessage(client, modelConfig, systemPrompt, prompt);
    
    return response;
  } catch (error) {
    console.error('Error generating tasks with OpenRouter:', error);
    throw error;
  }
}
```

### Advanced Usage with Model Selection

```javascript
import { getOpenRouterClientForMCP } from '../utils/openrouter-client-utils.js';

async function generateWithSpecificModel(session, prompt, modelName) {
  try {
    const client = await getOpenRouterClientForMCP(session);
    
    // Custom model configuration
    const modelConfig = {
      model: modelName || 'anthropic/claude-3-7-sonnet-********',
      maxTokens: parseInt(session?.env?.MAX_TOKENS || '64000'),
      temperature: parseFloat(session?.env?.TEMPERATURE || '0.2')
    };
    
    const systemPrompt = 'You are a helpful assistant.';
    
    const response = await client.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature
    });
    
    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error with specific model:', error);
    throw error;
  }
}
```

## Available Models

OpenRouter provides access to many models. Here are some recommended models for Claude Task Master:

| Model ID | Provider | Description |
|----------|----------|-------------|
| anthropic/claude-3-7-sonnet-******** | Anthropic | Recommended for most tasks |
| anthropic/claude-3-opus-20240229 | Anthropic | Highest quality for complex tasks |
| anthropic/claude-3-haiku-******** | Anthropic | Faster, more economical option |
| openai/gpt-4o | OpenAI | Alternative high-quality model |
| google/gemini-1.5-pro | Google | Alternative with strong capabilities |

## Configuration in MCP Server

To configure OpenRouter in your MCP server, update `.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "taskmaster-ai": {
      "command": "node",
      "args": ["./mcp-server/server.js"],
      "env": {
        "ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE",
        "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE",
        "OPENROUTER_API_KEY": "YOUR_OPENROUTER_API_KEY_HERE",
        "OPENROUTER_MODEL": "anthropic/claude-3-7-sonnet-********",
        "MODEL": "claude-3-7-sonnet-********",
        "PERPLEXITY_MODEL": "sonar-pro",
        "MAX_TOKENS": 64000,
        "TEMPERATURE": 0.2,
        "DEFAULT_SUBTASKS": 5,
        "DEFAULT_PRIORITY": "medium"
      }
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Ensure your OpenRouter API key is correct
   - Check that the API key has sufficient credits

2. **Model Not Available**
   - Verify the model ID is correct
   - Some models may be temporarily unavailable
   - Try using a fallback model

3. **Token Limits**
   - Different models have different token limits
   - Adjust MAX_TOKENS based on the model you're using

4. **Rate Limiting**
   - OpenRouter may impose rate limits
   - Implement exponential backoff for retries

## Conclusion

OpenRouter provides a flexible way to access multiple AI models through a single API. By integrating OpenRouter with Claude Task Master, you can leverage different models based on your specific needs, ensure higher availability, and potentially optimize costs.

For more information, visit the [OpenRouter documentation](https://openrouter.ai/docs).


---

### 对话 3

> **👤 用户** (2025年05月13日 03:02)

使用中文回复

> **🤖 Augment** (2025年05月13日 03:02)

# Claude Task Master 的 OpenRouter 集成文档

## 概述

本文档介绍如何在 Claude Task Master 项目中配置和使用 OpenRouter。OpenRouter 提供了一个统一的 API 接口，让您可以访问来自不同提供商的各种 AI 模型，包括 Anthropic、OpenAI 等。

## 为什么使用 OpenRouter？

- **访问多种模型**：通过单一 API 使用来自 Anthropic、OpenAI、Google 等提供商的模型
- **备选方案**：当主要模型不可用时，可配置备用模型
- **成本优化**：根据性价比选择模型
- **简化 API 密钥管理**：管理单个 API 密钥，而不是多个提供商密钥

## 前提条件

- OpenRouter 账户（在 [openrouter.ai](https://openrouter.ai) 注册）
- OpenRouter API 密钥
- 已设置的 Claude Task Master 项目

## 配置步骤

### 1. 环境变量

在您的 `.env` 文件中添加以下内容：

```
# OpenRouter 配置
OPENROUTER_API_KEY=你的_openrouter_api_密钥
OPENROUTER_MODEL=anthropic/claude-3-7-sonnet-********  # 默认模型
OPENROUTER_FALLBACK_MODEL=anthropic/claude-3-haiku-********  # 可选的备用模型
```

### 2. 安装所需包

```bash
npm install openai
```

OpenRouter 使用 OpenAI 客户端库，但配置了自定义的基础 URL。

### 3. 创建 OpenRouter 客户端工具

创建新文件 `mcp-server/src/core/utils/openrouter-client-utils.js`：

```javascript
/**
 * openrouter-client-utils.js
 * 在 MCP 上下文中初始化 OpenRouter 客户端的工具函数
 */

import dotenv from 'dotenv';

// 为 CLI 模式加载环境变量
dotenv.config();

/**
 * 获取使用 MCP 会话环境变量初始化的 OpenRouter 客户端实例
 * @param {Object} [session] - 包含环境变量的 MCP 会话对象
 * @param {Object} [log] - 日志记录器对象（默认为 console）
 * @returns {OpenAI} 为 OpenRouter API 配置的 OpenAI 客户端
 * @throws {Error} 如果 API 密钥缺失或无法导入 OpenAI 包
 */
export async function getOpenRouterClientForMCP(session, log = console) {
  try {
    // 从 session.env 提取 API 密钥或回退到环境变量
    const apiKey =
      session?.env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;

    if (!apiKey) {
      throw new Error(
        '在会话环境或 process.env 中未找到 OPENROUTER_API_KEY'
      );
    }

    // 动态导入 OpenAI（可能不会在所有上下文中使用）
    const { default: OpenAI } = await import('openai');

    // 初始化并返回为 OpenRouter 配置的新 OpenAI 客户端
    return new OpenAI({
      apiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      defaultHeaders: {
        'HTTP-Referer': 'https://github.com/your-username/claude-task-master', // 替换为您的项目 URL
        'X-Title': 'Claude Task Master' // 替换为您的项目名称
      }
    });
  } catch (error) {
    log.error(`初始化 OpenRouter 客户端失败: ${error.message}`);
    throw error;
  }
}

/**
 * 从会话环境获取 OpenRouter 模型配置或回退到默认值
 * @param {Object} [session] - 包含环境变量的 MCP 会话对象
 * @returns {Object} 包含模型、最大令牌数和温度的模型配置
 */
export function getOpenRouterModelConfig(session) {
  // 从会话获取值或回退到默认值
  return {
    model: session?.env?.OPENROUTER_MODEL || process.env.OPENROUTER_MODEL || 'anthropic/claude-3-7-sonnet-********',
    fallbackModel: session?.env?.OPENROUTER_FALLBACK_MODEL || process.env.OPENROUTER_FALLBACK_MODEL || 'anthropic/claude-3-haiku-********',
    maxTokens: parseInt(session?.env?.MAX_TOKENS || process.env.MAX_TOKENS || '64000'),
    temperature: parseFloat(session?.env?.TEMPERATURE || process.env.TEMPERATURE || '0.2')
  };
}
```

### 4. 更新 AI 客户端工具

修改 `mcp-server/src/core/utils/ai-client-utils.js` 以包含 OpenRouter 支持：

```javascript
// 添加 OpenRouter 工具的导入
import { getOpenRouterClientForMCP, getOpenRouterModelConfig } from './openrouter-client-utils.js';

// 在 getBestAvailableAIModel 函数中添加
export async function getBestAvailableAIModel(
  session,
  options = {},
  log = console
) {
  const { requiresResearch = false, claudeOverloaded = false } = options;

  // 如果已配置，首先尝试 OpenRouter
  if (session?.env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY) {
    try {
      const client = await getOpenRouterClientForMCP(session, log);
      return { type: 'openrouter', client };
    } catch (error) {
      log.warn(`OpenRouter 不可用: ${error.message}`);
      // 继续尝试其他选项
    }
  }

  // 现有的 Perplexity 和 Claude 代码...
  // ...

  // 如果到达这里，则没有成功初始化任何模型
  throw new Error('没有可用的 AI 模型。请检查您的 API 密钥。');
}
```

### 5. 为 OpenRouter 创建消息处理程序

创建一个新函数来处理 OpenRouter 消息创建：

```javascript
/**
 * 向 OpenRouter 发送消息并返回响应
 * @param {Object} client - OpenRouter 客户端
 * @param {Object} modelConfig - 模型配置
 * @param {string} systemPrompt - 系统提示
 * @param {string} userPrompt - 用户提示
 * @returns {Promise<string>} - 来自模型的响应
 */
export async function sendOpenRouterMessage(client, modelConfig, systemPrompt, userPrompt) {
  try {
    const response = await client.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature
    });
    
    return response.choices[0].message.content;
  } catch (error) {
    // 如果主要模型失败且配置了备用模型，尝试备用模型
    if (modelConfig.fallbackModel && error.status !== 401) {
      console.warn(`主要模型失败，尝试备用模型: ${modelConfig.fallbackModel}`);
      
      const fallbackResponse = await client.chat.completions.create({
        model: modelConfig.fallbackModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: Math.min(modelConfig.maxTokens, 32000), // 备用模型可能有较低的令牌限制
        temperature: modelConfig.temperature
      });
      
      return fallbackResponse.choices[0].message.content;
    }
    
    throw error;
  }
}
```

## 使用示例

### 基本用法

```javascript
import { getOpenRouterClientForMCP, getOpenRouterModelConfig } from '../utils/openrouter-client-utils.js';
import { sendOpenRouterMessage } from '../utils/ai-client-utils.js';

async function generateTasksWithOpenRouter(session, prompt) {
  try {
    // 获取 OpenRouter 客户端和模型配置
    const client = await getOpenRouterClientForMCP(session);
    const modelConfig = getOpenRouterModelConfig(session);
    
    // 任务生成的系统提示
    const systemPrompt = '你是一个根据项目需求生成任务的助手。';
    
    // 向 OpenRouter 发送消息
    const response = await sendOpenRouterMessage(client, modelConfig, systemPrompt, prompt);
    
    return response;
  } catch (error) {
    console.error('使用 OpenRouter 生成任务时出错:', error);
    throw error;
  }
}
```

### 高级用法与模型选择

```javascript
import { getOpenRouterClientForMCP } from '../utils/openrouter-client-utils.js';

async function generateWithSpecificModel(session, prompt, modelName) {
  try {
    const client = await getOpenRouterClientForMCP(session);
    
    // 自定义模型配置
    const modelConfig = {
      model: modelName || 'anthropic/claude-3-7-sonnet-********',
      maxTokens: parseInt(session?.env?.MAX_TOKENS || '64000'),
      temperature: parseFloat(session?.env?.TEMPERATURE || '0.2')
    };
    
    const systemPrompt = '你是一个有帮助的助手。';
    
    const response = await client.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature
    });
    
    return response.choices[0].message.content;
  } catch (error) {
    console.error('使用特定模型时出错:', error);
    throw error;
  }
}
```

## 可用模型

OpenRouter 提供了对多种模型的访问。以下是一些推荐用于 Claude Task Master 的模型：

| 模型 ID | 提供商 | 描述 |
|----------|----------|-------------|
| anthropic/claude-3-7-sonnet-******** | Anthropic | 推荐用于大多数任务 |
| anthropic/claude-3-opus-20240229 | Anthropic | 用于复杂任务的最高质量选项 |
| anthropic/claude-3-haiku-******** | Anthropic | 更快、更经济的选项 |
| openai/gpt-4o | OpenAI | 高质量的替代模型 |
| google/gemini-1.5-pro | Google | 具有强大能力的替代选项 |

## MCP 服务器中的配置

要在 MCP 服务器中配置 OpenRouter，请更新 `.cursor/mcp.json`：

```json
{
  "mcpServers": {
    "taskmaster-ai": {
      "command": "node",
      "args": ["./mcp-server/server.js"],
      "env": {
        "ANTHROPIC_API_KEY": "你的_ANTHROPIC_API_密钥",
        "PERPLEXITY_API_KEY": "你的_PERPLEXITY_API_密钥",
        "OPENROUTER_API_KEY": "你的_OPENROUTER_API_密钥",
        "OPENROUTER_MODEL": "anthropic/claude-3-7-sonnet-********",
        "MODEL": "claude-3-7-sonnet-********",
        "PERPLEXITY_MODEL": "sonar-pro",
        "MAX_TOKENS": 64000,
        "TEMPERATURE": 0.2,
        "DEFAULT_SUBTASKS": 5,
        "DEFAULT_PRIORITY": "medium"
      }
    }
  }
}
```

## 故障排除

### 常见问题

1. **认证错误**
   - 确保您的 OpenRouter API 密钥正确
   - 检查 API 密钥是否有足够的额度

2. **模型不可用**
   - 验证模型 ID 是否正确
   - 某些模型可能暂时不可用
   - 尝试使用备用模型

3. **令牌限制**
   - 不同模型有不同的令牌限制
   - 根据您使用的模型调整 MAX_TOKENS

4. **速率限制**
   - OpenRouter 可能实施速率限制
   - 实现指数退避重试策略

## 结论

OpenRouter 提供了一种灵活的方式，通过单一 API 访问多种 AI 模型。通过将 OpenRouter 与 Claude Task Master 集成，您可以根据特定需求利用不同的模型，确保更高的可用性，并可能优化成本。

有关更多信息，请访问 [OpenRouter 文档](https://openrouter.ai/docs)。


---

### 对话 4

> **👤 用户** (2025年05月13日 03:06)

目前该项目是已经集成了 OpenRouter 的 client 了吧？ 通过配置是如何选择不同的提供商的？

> **🤖 Augment** (2025年05月13日 03:06)

让我检查一下 `bin/task-master.js` 文件和项目中是否已经集成了 OpenRouter 客户端，以及如何通过配置选择不同的提供商。


让我检查一下项目中是否有与 OpenRouter 相关的代码：


让我查看一下环境变量配置文件，看看是否有 OpenRouter 相关的配置：


让我检查一下 MCP 配置文件：


让我查看一下 AI 客户端工具的实现：


基于我的研究，我可以回答您的问题：

## 关于 OpenRouter 集成

目前，Claude Task Master 项目**尚未**集成 OpenRouter 客户端。项目当前支持两种 AI 模型提供商：

1. **Anthropic Claude** - 主要用于大多数任务，通过 `getAnthropicClientForMCP` 函数实现
2. **Perplexity AI** - 用于需要研究能力的任务，通过 `getPerplexityClientForMCP` 函数实现

在 `ai-client-utils.js` 文件中的 `getBestAvailableAIModel` 函数负责根据任务需求和可用性选择最合适的模型。目前的选择逻辑是：

1. 如果任务需要研究能力且 Perplexity API 可用，则使用 Perplexity
2. 如果 Claude 未过载且 API 可用，则使用 Claude
3. 如果 Claude 过载但没有其他选择，仍然尝试使用 Claude

## 如何添加 OpenRouter 支持

要添加 OpenRouter 支持，您需要：

1. **创建 OpenRouter 客户端工具函数**：类似于我在之前的文档中提供的 `getOpenRouterClientForMCP` 函数

2. **更新环境变量配置**：在 `.env.example` 和 `.cursor/mcp.json` 中添加 OpenRouter 相关的配置项：
   ```
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   OPENROUTER_MODEL=anthropic/claude-3-7-sonnet-********
   ```

3. **修改模型选择逻辑**：更新 `getBestAvailableAIModel` 函数，在其中添加 OpenRouter 作为选项

## 如何通过配置选择不同的提供商

一旦实现了 OpenRouter 支持，您可以通过以下方式选择不同的提供商：

1. **通过环境变量**：
   - 设置 `OPENROUTER_API_KEY` 和 `OPENROUTER_MODEL` 来启用 OpenRouter
   - 设置 `ANTHROPIC_API_KEY` 来启用 Claude
   - 设置 `PERPLEXITY_API_KEY` 来启用 Perplexity

2. **通过模型选择参数**：
   - 在 OpenRouter 中，模型 ID 包含提供商前缀，例如：
     - `anthropic/claude-3-7-sonnet-********` - 使用 Anthropic 的 Claude 模型
     - `openai/gpt-4o` - 使用 OpenAI 的 GPT-4o 模型
     - `google/gemini-1.5-pro` - 使用 Google 的 Gemini 模型

3. **通过功能标志**：
   - 使用 `requiresResearch` 标志可以优先选择 Perplexity
   - 可以添加类似的标志来优先选择 OpenRouter，例如 `preferOpenRouter`

## 实现建议

要实现 OpenRouter 支持，您需要：

1. 创建 `openrouter-client-utils.js` 文件，实现 OpenRouter 客户端创建函数
2. 更新 `ai-client-utils.js` 中的 `getBestAvailableAIModel` 函数，添加 OpenRouter 支持
3. 更新环境变量配置文件，添加 OpenRouter 相关配置
4. 更新文档，说明如何配置和使用 OpenRouter

这样，用户就可以通过配置环境变量和模型 ID 来选择不同的提供商，而无需修改代码。


---

