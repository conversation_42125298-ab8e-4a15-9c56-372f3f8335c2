# claude-task-master 的记忆

# OpenRouter & MCP
- The user is interested in OpenRouter integration and configuration for using different AI models with MCP.
- The user is interested in OpenRouter integration and documentation for model configuration.
- The user is interested in OpenRouter integration configuration in mcp-server, particularly regarding default AI models when using OpenRouter API Key.
- 用户使用OpenRouter作为AI服务提供商，并已在mcp.json中配置了OpenRouter API密钥。
- User observed that OpenRouter API key is showing as present in CLI (.env) but missing in MCP (mcp.json), contrary to their expectations.

# Task Master & AI Integration
- User wants to understand which Task Master commands inherently require AI models, which ones optionally use AI, and which ones don't need AI integration at all.
- User is interested in understanding how Task Master integrates with AI programming tools like Cursor and Cline, particularly regarding model usage versus custom API implementation.
- User wants to understand the structure of PRD (Product Requirements Document) documentation.
- User wants to add a 'create-prd' command to Claude Task Master with template options, interactive mode, and AI-assisted content generation.
- When using <PERSON> Task Master's create-prd command with AI assistance, the process requires user input for project description but may not clearly prompt for it.
- The user wants to add functionality to automatically generate PRD documents for existing projects, as the current system can only parse existing PRDs.
- The user wants to add a code analysis module to Claude Task Master that can analyze existing project code and automatically generate PRD documents, potentially using Cursor Rules and AI capabilities rather than traditional parsing approaches.
- The user is interested in understanding the execution flow of the parse_prd MCP tool in the Claude Task Master project.
- User wants detailed documentation of task-manager.js commands in Chinese, with special focus on which commands involve AI model integration.
- Using Task Master's AI features requires correct configuration of AI service API keys and related configuration files.

# UI & Technology Stack
- 用户对UI展示技术栈感兴趣，希望了解代码中使用的UI相关技术。

# Preferences
- The user prefers responses in Chinese language.