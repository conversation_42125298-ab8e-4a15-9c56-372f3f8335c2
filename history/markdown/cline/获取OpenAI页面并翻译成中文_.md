# 获取OpenAI页面并翻译成中文


**项目**: cline

**创建时间**: 2025年04月15日 09:52

**最后交互**: 2025年04月15日 10:00

**对话ID**: e347e486-2d39-43e3-bf25-880b7c419bed

---

### 对话 1

> **👤 用户**

https://openai.com/index/gpt-4-1/  获取这个页面的原始内容，帮我翻译成中文

> **🤖 Augment**

我将帮您获取OpenAI GPT-4-1页面的内容并翻译成中文。让我先抓取该页面的内容。


现在我将把这个页面的内容翻译成中文：

# 在API中推出GPT-4.1

2025年4月14日

[产品](https://openai.com/news/product-releases/)

## 在API中推出GPT-4.1

一系列新的GPT模型，在编码、指令遵循和长上下文方面有重大改进——以及我们首个超小型模型。

[在Playground中尝试](https://platform.openai.com/playground/p/HqaxY9MEZ8Ta0zFbzfASn5bJ?mode=chat)

今天，我们在API中推出三个新模型：GPT-4.1、GPT-4.1 mini和GPT-4.1 nano。这些模型在各方面都优于GPT-4o和GPT-4o mini，在编码和指令遵循方面有重大提升。它们还拥有更大的上下文窗口——支持高达100万个token的上下文——并且能够通过改进的长上下文理解能力更好地利用这些上下文。它们的知识截止日期更新至2024年6月。

GPT-4.1在以下行业标准指标上表现出色：

- **编码**：GPT-4.1在[SWE-bench Verified](https://openai.com/index/introducing-swe-bench-verified/)上得分54.6%，比GPT-4o提高了21.4个百分点，比GPT-4.5提高了26.6个百分点——使其成为编码领域的领先模型。
- **指令遵循**：在[Scale的MultiChallenge](https://scale.com/leaderboard/multichallenge)基准测试中，这是衡量指令遵循能力的指标，GPT-4.1得分38.3%，比GPT-4o提高了10.5个百分点。
- **长上下文**：在[Video-MME](https://video-mme.github.io/home_page.html)上，这是一个多模态长上下文理解的基准测试，GPT-4.1创造了新的最先进结果——在长视频无字幕类别中得分72.0%，比GPT-4o提高了6.7个百分点。

虽然基准测试提供了宝贵的见解，但我们训练这些模型时注重实际应用价值。与开发者社区的密切合作使我们能够优化这些模型，以适应对他们的应用最重要的任务。

为此，GPT-4.1模型系列以更低的成本提供卓越的性能。这些模型在延迟曲线的每个点上都推动了性能的提升。

![按延迟划分的GPT-4.1系列智能](https://images.ctfassets.net/kftzwdyauwt9/1QIXpeDCuUBwiGQ6xxkIeg/75a11a9c0e222c9b93896fab30304d66/GPT-4.1_Family_Intelligence_by_Latency_lightMode.svg?w=3840&q=90)

GPT-4.1 mini在小型模型性能方面取得了重大飞跃，甚至在许多基准测试中超过了GPT-4o。它在智能评估中与GPT-4o相当或超过GPT-4o，同时将延迟减少了近一半，成本降低了83%。

对于需要低延迟的任务，GPT-4.1 nano是我们提供的最快速和最便宜的模型。它以小尺寸提供卓越的性能，拥有100万token的上下文窗口，在MMLU上得分80.1%，在GPQA上得分50.3%，在Aider多语言编码上得分9.8%——甚至高于GPT-4o mini。它非常适合分类或自动完成等任务。

这些在指令遵循可靠性和长上下文理解方面的改进也使GPT-4.1模型在驱动代理方面更加有效，即可以代表用户独立完成任务的系统。当与[Responses API](https://platform.openai.com/docs/api-reference/responses)等原语结合使用时，开发者现在可以构建更有用、更可靠的代理，用于实际软件工程、从大型文档中提取见解、以最少的指导解决客户请求以及其他复杂任务。

请注意，GPT-4.1将仅通过API提供。在ChatGPT中，指令遵循、编码和智能方面的许多改进已逐步纳入[GPT-4o的最新版本](https://help.openai.com/en/articles/6825453-chatgpt-release-notes)，我们将在未来的版本中继续整合更多功能。

我们还将开始在API中弃用GPT-4.5 Preview，因为GPT-4.1在许多关键能力上提供了改进或类似的性能，同时大大降低了成本和延迟。GPT-4.5 Preview将在三个月后，即2025年7月14日关闭，以便开发者有时间过渡。GPT-4.5是作为研究预览版[推出](https://openai.com/index/introducing-gpt-4-5/)的，目的是探索和试验大型、计算密集型模型，我们从开发者反馈中学到了很多。我们将继续在未来的API模型中保留您告诉我们您欣赏的GPT-4.5的创造力、写作质量、幽默感和细微差别。

下面，我们分析了GPT-4.1在几个基准测试中的表现，以及来自Windsurf、Qodo、Hex、Blue J、Thomson Reuters和Carlyle等alpha测试者的示例，展示了它在特定领域任务的生产环境中的表现。

## 编码

GPT-4.1在各种编码任务上明显优于GPT-4o，包括代理式解决编码任务、前端编码、减少多余编辑、可靠地遵循差异格式、确保一致的工具使用等。

在SWE-bench Verified上，这是衡量真实世界软件工程技能的指标，GPT-4.1完成了54.6%的任务，而GPT-4o(2024-11-20)为33.2%。这反映了模型在探索代码库、完成任务以及生成既能运行又能通过测试的代码方面的能力提升。

对于希望编辑大型文件的API开发者，GPT-4.1在各种格式的代码差异方面更加可靠。GPT-4.1在[Aider的多语言差异基准测试](https://aider.chat/docs/leaderboards/)上的得分是GPT-4o的两倍多，甚至比GPT-4.5高出8个百分点。这项评估既是衡量各种编程语言的编码能力，也是衡量模型在整体和差异格式中产生变化的能力。我们专门训练GPT-4.1更可靠地遵循差异格式，这使开发者只需让模型输出更改的行，而不是重写整个文件，从而节省成本和延迟。为了获得最佳的代码差异性能，请参考我们的[提示指南](http://platform.openai.com/docs/guides/text?api-mode=responses#prompting-gpt-4-1-models)。对于喜欢重写整个文件的开发者，我们已将GPT-4.1的输出token限制增加到32,768个token（从GPT-4o的16,384个token增加）。我们还建议使用[Predicted Outputs](https://platform.openai.com/docs/guides/predicted-outputs)来减少完整文件重写的延迟。

GPT-4.1在前端编码方面也大大改进了GPT-4o，能够创建更加功能性和美观的Web应用程序。在我们的一对一比较中，付费人类评分者80%的时间更喜欢GPT-4.1的网站而非GPT-4o的网站。

除了上述基准测试外，GPT-4.1更能可靠地遵循格式，并且更少地进行多余编辑。在我们的内部评估中，代码上的多余编辑从GPT-4o的9%下降到GPT-4.1的2%。

## 真实世界示例

[**Windsurf**](https://windsurf.com/editor)：GPT-4.1在Windsurf的内部编码基准测试中的得分比GPT-4o高60%，这与代码更改在首次审查中被接受的频率有很强的相关性。他们的用户注意到，它在工具调用方面效率提高了30%，重复不必要的编辑或以过于狭窄、增量步骤阅读代码的可能性降低了约50%。这些改进为工程团队带来了更快的迭代和更流畅的工作流程。

[**Qodo**](https://www.qodo.ai/)：Qodo对GPT-4.1与其他领先模型进行了一对一测试，测试它们从GitHub拉取请求生成高质量代码审查的能力，使用的方法受到他们的微调基准测试的启发。在200个有意义的真实世界拉取请求中，使用相同的提示和条件，他们发现GPT-4.1在[55%的情况下](https://www.qodo.ai/blog/benchmarked-gpt-4-1/)产生了更好的建议。值得注意的是，他们发现GPT-4.1在精确性（知道何时不提出建议）和全面性（在需要时提供彻底分析）方面表现出色，同时保持对真正关键问题的关注。

## 指令遵循

GPT-4.1更可靠地遵循指令，我们在各种指令遵循评估中测量到了显著的改进。

我们开发了一个内部评估系统来跟踪模型在多个维度和指令遵循的几个关键类别中的表现，包括：

- **格式遵循**。提供指定模型响应自定义格式的指令，如XML、YAML、Markdown等。
- **否定指令**。指定模型应避免的行为。（例如："不要要求用户联系支持"）
- **有序指令**。提供模型必须按给定顺序遵循的一组指令。（例如："首先询问用户的姓名，然后询问他们的电子邮件"）
- **内容要求**。输出包含特定信息的内容。（例如："在编写营养计划时始终包括蛋白质量"）
- **排名**。以特定方式对输出进行排序。（例如："按人口数量对响应进行排序"）
- **过度自信**。指示模型在请求的信息不可用或请求不属于给定类别时说"我不知道"或类似的话。（例如："如果您不知道答案，请提供支持联系电子邮件"）

这些类别是开发者反馈的结果，关于哪些指令遵循方面对他们最相关和重要。在每个类别中，我们将提示分为简单、中等和困难。GPT-4.1在困难提示方面特别比GPT-4o有显著改进。

多轮指令遵循对许多开发者至关重要——模型在对话深入时保持连贯性，并跟踪用户之前告诉它的内容很重要。我们训练GPT-4.1能够更好地从对话中的过去消息中挑选信息，使对话更自然。Scale的MultiChallenge基准测试是衡量这种能力的有用指标，GPT-4.1的表现比GPT-4o好10.5个百分点。

GPT-4.1在IFEval上的得分也达到87.4%，而GPT-4o为81.0%。IFEval使用带有可验证指令的提示（例如，指定内容长度或避免某些术语或格式）。

更好的指令遵循使现有应用程序更可靠，并使以前因可靠性差而受限的新应用成为可能。早期测试者注意到GPT-4.1可能更加字面化，因此我们建议在提示中明确和具体。有关GPT-4.1提示最佳实践的更多信息，请参阅提示指南。

## 真实世界示例

[**Blue J**](https://www.bluej.com/)：GPT-4.1在Blue J最具挑战性的真实世界税务场景的内部基准测试中，准确率比GPT-4o高53%。这种准确率的飞跃——对系统性能和用户满意度都至关重要——突显了GPT-4.1在理解复杂法规和在长上下文中遵循细微指令的能力提升。对Blue J用户来说，这意味着更快、更可靠的税务研究，以及更多时间用于高价值咨询工作。

[**Hex**](https://hex.tech/)：GPT-4.1在Hex最具挑战性的[SQL评估集](https://hex.tech/blog/im-sorry-but-those-are-vanity-evals)上提供了近2倍的改进，展示了指令遵循和语义理解方面的显著提升。该模型在从大型、模糊的架构中选择正确表格方面更加可靠——这是一个直接影响整体准确性的上游决策点，仅通过提示很难调整。对Hex来说，这导致手动调试明显减少，更快地实现生产级工作流程。

## 长上下文

GPT-4.1、GPT-4.1 mini和GPT-4.1 nano可以处理高达100万个token的上下文——比之前的GPT-4o模型的128,000个增加了很多。100万个token相当于整个React代码库的8个副本，因此长上下文非常适合处理大型代码库或大量长文档。

我们训练GPT-4.1能够可靠地关注完整的100万上下文长度中的信息。我们还训练它比GPT-4o更可靠地注意相关文本，并在长短上下文长度中忽略干扰因素。长上下文理解是法律、编码、客户支持和许多其他领域应用的关键能力。

下面，我们展示了GPT-4.1在上下文窗口中不同位置找到隐藏的小信息（"针"）的能力。GPT-4.1在所有位置和所有上下文长度上都能一致准确地检索到针，一直到100万个token。无论输入中的相关细节位置如何，它都能有效地提取出与任务相关的细节。

![GPT-4.1大海捞针准确率图表](https://images.ctfassets.net/kftzwdyauwt9/5N4CGIc8M9XNVP4UxyrFNd/2918a7016861e8ceac30d06da80f6326/GPT-4.1__GPT-4.1_mini__and_GPT-4.1_nano_Needle_in_a_Haystack_Accuracy_LightMode.svg?w=3840&q=90)

然而，很少有现实世界的任务像检索单个明显的针答案那样简单。我们发现用户经常需要我们的模型检索和理解多个信息片段，并理解这些片段之间的关系。为了展示这种能力，我们开源了一个新的评估：OpenAI-MRCR（多轮共指）。

OpenAI-MRCR测试模型在上下文中找到并区分多个隐藏针的能力。评估包括用户和助手之间的多轮合成对话，用户要求撰写关于某个主题的文章，例如"写一首关于貘的诗"或"写一篇关于岩石的博客文章"。然后我们在整个上下文中插入两个、四个或八个相同的请求。模型必须检索对应于特定实例的响应（例如，"给我第三首关于貘的诗"）。

挑战来自于这些请求与上下文其余部分的相似性——模型很容易被微妙的差异误导，例如关于貘的短篇故事而不是诗，或者关于青蛙而不是貘的诗。我们发现GPT-4.1在高达128K个token的上下文长度上优于GPT-4o，并且即使在高达100万个token时也保持强劲的性能。

但这个任务仍然很困难——即使对于高级推理模型也是如此。我们正在分享[评估数据集](https://huggingface.co/datasets/openai/mrcr)以鼓励在实际长上下文检索方面的进一步工作。

我们还发布了[Graphwalks](https://huggingface.co/datasets/openai/graphwalks)，这是一个用于评估多跳长上下文推理的数据集。长上下文的许多开发者用例需要在上下文内进行多个逻辑跳跃，比如在编写代码时在多个文件之间跳转，或者在回答复杂法律问题时交叉引用文档。

模型（甚至人类）理论上可以通过对提示进行一次传递或阅读来解决OpenAI-MRCR问题，但Graphwalks的设计需要在上下文的多个位置进行推理，不能顺序解决。

Graphwalks用一个由十六进制哈希组成的有向图填充上下文窗口，然后要求模型从图中的随机节点执行广度优先搜索（BFS）。然后我们要求它返回特定深度的所有节点。GPT-4.1在这个基准测试上达到61.7%的准确率，与o1的性能相当，并且大大超过GPT-4o。

基准测试并不能说明全部情况，因此我们与alpha合作伙伴合作，测试GPT-4.1在他们的真实世界长上下文任务上的表现。

## 真实世界示例

[**Thomson Reuters**](https://blogs.thomsonreuters.com/en-us/innovation/legal-ai-benchmarking-evaluating-long-context-performance-for-llms)：Thomson Reuters使用GPT-4.1测试了他们的专业级法律工作AI助手CoCounsel。与GPT-4o相比，他们能够在内部长上下文基准测试中将多文档审查准确率提高17%——这是衡量CoCounsel处理涉及多个长文档的复杂法律工作流程能力的重要指标。特别是，他们发现该模型在跨来源保持上下文和准确识别文档之间的细微关系方面非常可靠，例如冲突条款或额外的补充上下文——这些任务对法律分析和决策至关重要。

[**Carlyle**](https://www.carlyle.com/)：Carlyle使用GPT-4.1准确地从多个长文档中提取细粒度财务数据——包括PDF、Excel文件和其他复杂格式。根据他们的内部评估，它在从具有密集数据的非常大的文档中检索数据方面表现提高了50%，并且是第一个成功克服其他可用模型所见关键限制的模型，包括大海捞针检索、中间丢失错误和跨文档的多跳推理。

除了模型性能和准确性外，开发者还需要能够快速响应的模型，以跟上并满足用户的需求。我们改进了推理堆栈以减少首个token的时间，并且通过提示缓存，您可以进一步减少延迟同时节省成本。在我们的初步测试中，GPT-4.1在128,000个token上下文的情况下，首个token的延迟约为15秒，对于100万个token的上下文约为1分钟。GPT-4.1 mini和nano更快，例如，GPT-4.1 nano在具有128,000个输入token的查询中，通常在不到5秒的时间内返回第一个token。

## 视觉

GPT-4.1系列在图像理解方面表现非常出色，特别是GPT-4.1 mini代表了一个重大飞跃，在图像基准测试中经常超过GPT-4o。

GPT-4.1在长上下文性能对多模态用例也很重要，例如处理长视频。在[Video-MME](https://video-mme.github.io/home_page.html)（长无字幕）中，模型根据30-60分钟长的无字幕视频回答多项选择题。GPT-4.1达到了


---

### 对话 2

> **👤 用户**

总结这篇文章

> **🤖 Augment**

# GPT-4.1 API推出：OpenAI的最新模型系列

## 主要亮点

OpenAI于2025年4月14日推出了三个新模型：**GPT-4.1**、**GPT-4.1 mini**和**GPT-4.1 nano**。这些模型在各方面都优于GPT-4o系列，具有以下特点：

- 支持高达**100万tokens**的上下文窗口
- 知识截止日期更新至**2024年6月**
- 显著提升的编码、指令遵循和长上下文理解能力

## 性能提升

1. **编码能力**：
   - GPT-4.1在SWE-bench Verified上得分54.6%，比GPT-4o提高21.4个百分点
   - 在Aider的多语言差异基准测试上表现是GPT-4o的两倍多
   - 前端编码质量大幅提升，80%的人类评估者更喜欢GPT-4.1创建的网站

2. **指令遵循**：
   - 在Scale的MultiChallenge上得分38.3%，比GPT-4o提高10.5个百分点
   - 在IFEval上得分87.4%，而GPT-4o为81.0%
   - 特别擅长格式遵循、否定指令、有序指令和内容要求

3. **长上下文理解**：
   - 在Video-MME上创造新的最先进结果，得分72.0%
   - 能够在100万tokens的完整上下文中可靠地检索和理解信息
   - 在新的OpenAI-MRCR和Graphwalks基准测试上表现优异

## 实际应用案例

- **Windsurf**：编码基准测试得分提高60%，工具调用效率提高30%
- **Qodo**：在55%的真实世界代码审查中提供更好的建议
- **Blue J**：在复杂税务场景中准确率提高53%
- **Hex**：在SQL评估中提供近2倍的改进
- **Thomson Reuters**：多文档法律审查准确率提高17%
- **Carlyle**：从大型财务文档中提取数据的能力提高50%

## 定价和可用性

- GPT-4.1比GPT-4o便宜26%
- 提供75%的提示缓存折扣（之前为50%）
- 长上下文请求不收取额外费用
- GPT-4.1 nano是OpenAI最便宜、最快速的模型

GPT-4.1将仅通过API提供，而ChatGPT将继续使用不断改进的GPT-4o版本。同时，GPT-4.5 Preview将在三个月后（2025年7月14日）停用，因为GPT-4.1在许多关键能力上提供了类似或更好的性能，同时大大降低了成本和延迟。


---

