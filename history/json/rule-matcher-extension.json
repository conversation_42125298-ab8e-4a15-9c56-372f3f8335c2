{"name": "rule-matcher-extension", "path": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "workspace_id": "fb5f37c89f0aac9e9a6659b1eea2d988", "conversations": [{"id": "af399ed3-dae0-49c6-aa51-ff318ba885d3", "name": "", "created_at": "2025-04-11T09:56:32.275Z", "last_interacted_at": "2025-04-11T09:56:32.275Z", "messages": []}, {"id": "adb18d30-616a-45d3-b76c-1ec5d17a8b4a", "name": "", "created_at": "2025-04-11T09:56:32.291Z", "last_interacted_at": "2025-04-11T09:56:38.820Z", "messages": [{"request_id": "0da4ab46-5554-4168-ac70-3f5ca21c35ca", "request_message": "Provide a short summary of this project", "response_text": "# Rule Matcher Extension Summary\n\nThis is a VSCode extension that analyzes projects and recommends appropriate coding rules. Key features include:\n\n- Project analysis: Automatically detects dependencies, code structure, and tech stack\n- Rule matching: Recommends coding rules based on project characteristics\n- AI enhancement: Uses AI models (OpenAI, Anthropic, or compatible APIs) for smarter rule matching\n- GitHub integration: Supports loading projects and rules from repositories\n- Rule management: Apply, remove, and view details of rules\n- Custom rule generation: Creates project-specific rules using AI\n\nThe extension works by analyzing project files, identifying technologies used, matching with appropriate rules from a repository, and allowing users to apply these rules to improve code quality and development efficiency.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 56, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/extension.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 9, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/extension.ts", "full_range": {"startLineNumber": 12, "startColumn": 0, "endLineNumber": 81, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/commands/commands.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 26, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/commands/commands.ts", "full_range": {"startLineNumber": 42, "startColumn": 0, "endLineNumber": 79, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/commands/commands.ts", "full_range": {"startLineNumber": 205, "startColumn": 0, "endLineNumber": 222, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/commands/commands.ts", "full_range": {"startLineNumber": 353, "startColumn": 0, "endLineNumber": 370, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/analyzer/project-analyzer.ts", "full_range": {"startLineNumber": 17, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "CHANGELOG.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/rule-matcher-service.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/rule-matcher-service.ts", "full_range": {"startLineNumber": 79, "startColumn": 0, "endLineNumber": 95, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/rule-matcher-service.ts", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 167, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/rule-matcher-service.ts", "full_range": {"startLineNumber": 191, "startColumn": 0, "endLineNumber": 217, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "package.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 50, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "package.json", "full_range": {"startLineNumber": 145, "startColumn": 0, "endLineNumber": 230, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/ai-matcher/ai-matcher.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/ai-matcher/ai-matcher.ts", "full_range": {"startLineNumber": 185, "startColumn": 0, "endLineNumber": 201, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/ai-matcher/ai-matcher.ts", "full_range": {"startLineNumber": 217, "startColumn": 0, "endLineNumber": 237, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/ai-matcher/ai-matcher.ts", "full_range": {"startLineNumber": 254, "startColumn": 0, "endLineNumber": 277, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/ai-matcher/ai-matcher.ts", "full_range": {"startLineNumber": 299, "startColumn": 0, "endLineNumber": 324, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/ai-matcher/ai-matcher.ts", "full_range": {"startLineNumber": 411, "startColumn": 0, "endLineNumber": 443, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/custom-rule-generator/custom-rule-generator-service.ts", "full_range": {"startLineNumber": 106, "startColumn": 0, "endLineNumber": 128, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/analyzer/types.ts", "full_range": {"startLineNumber": 37, "startColumn": 0, "endLineNumber": 72, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/rule-matcher.ts", "full_range": {"startLineNumber": 26, "startColumn": 0, "endLineNumber": 48, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/rule-matcher.ts", "full_range": {"startLineNumber": 81, "startColumn": 0, "endLineNumber": 103, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/analyzer/code-analyzer/code-analyzer.ts", "full_range": {"startLineNumber": 14, "startColumn": 0, "endLineNumber": 36, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/analyzer/code-analyzer/code-analyzer.ts", "full_range": {"startLineNumber": 141, "startColumn": 0, "endLineNumber": 163, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/analyzer/tech-stack-detector/tech-stack-detector.ts", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 18, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/commands/add-rule-command.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 54, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "webpack.config.js", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 39, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": ".vscodeign<PERSON>", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 32, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/custom-rule-generator/ai-client.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 27, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/custom-rule-generator/ai-client.ts", "full_range": {"startLineNumber": 42, "startColumn": 0, "endLineNumber": 68, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/custom-rule-generator/ai-client.ts", "full_range": {"startLineNumber": 76, "startColumn": 0, "endLineNumber": 143, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/custom-rule-generator/ai-client.ts", "full_range": {"startLineNumber": 217, "startColumn": 0, "endLineNumber": 235, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/custom-rule-generator/prompt-templates.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 21, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/commands/custom-rule-commands.ts", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 68, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/providers/project-analysis-provider.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 2, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/providers/project-analysis-provider.ts", "full_range": {"startLineNumber": 22, "startColumn": 0, "endLineNumber": 46, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/vscode-extension/providers/project-analysis-provider.ts", "full_range": {"startLineNumber": 242, "startColumn": 0, "endLineNumber": 274, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/rule-repository/rule-repository.ts", "full_range": {"startLineNumber": 192, "startColumn": 0, "endLineNumber": 218, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/rule-matcher-extension", "path_name": "src/rule-matcher/rule-repository/rule-repository.ts", "full_range": {"startLineNumber": 368, "startColumn": 0, "endLineNumber": 385, "endColumn": 0}}]}]}]}