{"name": "302_prompt_generator", "path": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "workspace_id": "bea0093aeb983317da3ed9d7ea672158", "conversations": [{"id": "1b910a08-ef40-49f2-a483-cd400f7578ef", "name": "", "created_at": "2025-05-26T09:21:17.337Z", "last_interacted_at": "2025-05-26T09:26:48.576Z", "messages": [{"request_id": "3ab09179-98fe-4827-89c3-ddb60824e8fe", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "fd3e690b-09cd-4801-9c12-8a534e8d96d0", "request_message": "分析该项目实现的主要功能是什么？ 项目中的文件关系是什么？", "response_text": "", "timestamp": "2025-05-26T09:21:55.929Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": ".clinerules", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 164, "endColumn": 226}}]}, {"request_id": "2c0acb1d-9988-4182-929c-d00b05c5d18c", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:22:51.994Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "cline_docs/techStack.md", "full_range": {"startLineNumber": 10, "startColumn": 0, "endLineNumber": 10, "endColumn": 0}}]}, {"request_id": "8ba77ad0-8b22-4707-bc68-571b793a117c", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:23:13.463Z", "workspace_files": []}, {"request_id": "aa846836-2d86-46db-abc5-b59ae3abb6b0", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:23:33.018Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/store/globalSlice.ts", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "6522c0af-8d82-495e-9a57-610ecfacf007", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:24:16.520Z", "workspace_files": []}, {"request_id": "cd1c768e-fdd0-4891-a972-b8bd51658917", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "fa8c14a0-f2a0-4c9e-9d6d-0eeac01e5dc8", "request_message": "@src/lib/prompt.ts  阅读这个文件，主要是用来生成提示词的？请为我梳理有多少种提示词模板？每种的作用是什么？", "response_text": "", "timestamp": "2025-05-26T09:26:18.061Z", "workspace_files": []}]}, {"id": "658a7dd9-8220-449d-bd07-7ab4c338039f", "name": "", "created_at": "2025-05-26T09:27:07.251Z", "last_interacted_at": "2025-05-26T09:44:05.977Z", "messages": [{"request_id": "fc10c7f1-454c-4fb8-a3fa-1388d6bc4d20", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "a5db7829-634d-44b2-8e7a-c8cb590bc861", "request_message": "@src/lib/prompt.ts 阅读这个文件，主要是用来生成提示词的？请为我梳理有多少种提示词模板？每种的作用是什么？", "response_text": "我来帮您阅读并分析这个文件，了解其中的提示词模板。\n", "timestamp": "2025-05-26T09:27:13.578Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "5f5791b3-5d8a-4fc7-9591-ebe4a37fa3af", "request_message": "", "response_text": "根据对 `src/lib/prompt.ts` 文件的分析，我可以为您梳理出这个文件中包含的提示词模板。这个文件主要用于生成各种类型的提示词，包含了多种不同的提示词框架和模板。\n\n## 提示词模板总览\n\n该文件包含 **13种** 不同的提示词模板，每种都有其特定的用途：\n\n### 1. **CO-STAR** 框架\n- **作用**：基于CO-STAR框架（上下文、目标、身份、语气、受众、结果）来分析和拆解任务，生成结构化的提示词\n- **特点**：将任务分解为6个部分，确保AI能够完全理解并准确执行任务\n\n### 2. **CRISPE** 框架  \n- **作用**：基于CRISPE提示框架（Capacity、Role、Insight、Statement、Personality、Experiment）优化常规提示词\n- **特点**：包含角色定义、技能描述、目标设定、约束条件等完整结构\n\n### 3. **DRAW** 绘画提示词优化\n- **作用**：专门用于优化绘画相关的提示词，将画面描述拆解为镜头、光线、主体、背景、风格和氛围六个要素\n- **特点**：生成适合AI绘画工具使用的高质量提示词\n\n### 4. **MicrOptimization** 微优化\n- **作用**：对现有提示词进行深度优化，增加复杂度和具体性\n- **特点**：在原有基础上增加30-50个字的新内容，提升提示词效果\n\n### 5. **Q*** 算法提示词\n- **作用**：基于Q*算法设计个性化学习路径，适用于教育场景\n- **特点**：包含系统指令、变量定义、A*搜索、Q值估计等复杂结构\n\n### 6. **RISE** 递归内省算法\n- **作用**：通过多轮交互持续改进回答质量，具备自我学习和改进能力\n- **特点**：包含初始回应、自我分析、改进策略、迭代优化等步骤\n\n### 7. **VARI** 变分规划\n- **作用**：将任务转化为变分规划的提示词，适用于内容生成任务\n- **特点**：定义状态空间、动作空间、奖励函数等数学化结构\n\n### 8. **CoT** 思维链\n- **作用**：基于思维链推理方法，适用于需要逐步推理的复杂任务\n- **特点**：包含医疗诊断、金融风险评估、创意写作等多个示例\n\n### 9. **Meta Prompting** 元提示\n- **作用**：优化提示词的结构和语法，提高token使用效率\n- **特点**：专注于保持核心目标的同时简化和优化提示词结构\n\n### 10. **Custom** 自定义\n- **作用**：允许用户输入自定义的提示词模板\n- **特点**：支持{input}占位符替换，灵活性最高\n\n### 11. **O1-STYLE** O1风格推理\n- **作用**：模拟O1模型的推理风格，包含思考、步骤、反思、奖励评分等元素\n- **特点**：强调逐步思考、自我反思和质量评估\n\n### 12. **OpenAI** 官方风格\n- **作用**：按照OpenAI官方推荐的提示词结构来优化任务描述\n- **特点**：包含任务理解、推理顺序、示例、格式要求等标准化结构\n\n### 13. **Claude** Anthropic风格\n- **作用**：基于Anthropic Claude的提示词编写风格，适用于复杂任务指导\n- **特点**：详细的任务说明、输入变量定义、结构化指导等\n\n## 核心功能\n\n这个文件的主要功能是：\n1. **提示词模板管理**：集中管理多种不同类型的提示词框架\n2. **动态内容替换**：支持将用户任务动态插入到模板中\n3. **自定义支持**：允许用户使用自定义模板\n4. **多场景覆盖**：涵盖教育、创作、分析、优化等多个应用场景\n\n每种模板都针对特定的使用场景和需求，用户可以根据具体任务选择最适合的提示词框架来生成高质量的AI指令。\n", "timestamp": "2025-05-26T09:27:36.299Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "1ef05980-3c96-48f8-9881-0c5a7dc2cf2a", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "eda8546d-454e-4078-9393-ea6e17fc4d0d", "request_message": "分析每个提示词模板的使用场景", "response_text": "", "timestamp": "2025-05-26T09:42:49.601Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "d22ec8db-8388-4596-8d59-23e40d1e4936", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:43:00.790Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "ed6362b7-ad1d-40eb-9f9f-a8db8bb4c8c5", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:43:08.511Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "e86fea77-ac4d-4e27-8066-4f5ff032d262", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:43:23.995Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "ab8cd5dd-6562-48b9-99e4-af8b77b0a72d", "request_message": "", "response_text": "", "timestamp": "2025-05-26T09:43:41.285Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}, {"request_id": "0e4d11f3-534a-4b29-83af-cb1e836f0ac8", "request_message": "", "response_text": "基于对代码的深入分析，我为您详细梳理每个提示词模板的使用场景：\n\n## 📋 通用任务分解类\n\n### 1. **CO-STAR** 框架\n**最佳使用场景：**\n- 复杂的多维度任务需要全面规划\n- 需要考虑受众、目标、身份等多个因素的项目\n- 商业策划、营销方案、产品设计等\n\n**适用时机：**\n- 任务目标不够明确时\n- 需要系统性思考的复杂项目\n- 多利益相关方的任务\n\n**实际应用示例：**\n- 制定社交媒体营销策略\n- 设计用户体验流程\n- 撰写商业计划书\n\n### 2. **CRISPE** 框架\n**最佳使用场景：**\n- 需要AI扮演特定专业角色\n- 技能导向的任务执行\n- 需要明确工作流程的项目\n\n**适用时机：**\n- 需要专业知识输出\n- 角色扮演类任务\n- 需要标准化流程的工作\n\n**实际应用示例：**\n- AI客服助手设计\n- 专业咨询服务\n- 教学辅导系统\n\n### 3. **OpenAI** 官方风格\n**最佳使用场景：**\n- 需要标准化、规范化的提示词\n- 企业级应用开发\n- 需要高度可靠性的任务\n\n**适用时机：**\n- 生产环境部署\n- 需要一致性输出的场景\n- 团队协作项目\n\n### 4. **Claude** Anthropic风格\n**最佳使用场景：**\n- 复杂的指令设计\n- 需要详细示例的任务\n- 高精度要求的应用\n\n**适用时机：**\n- 精细化控制需求\n- 多步骤复杂任务\n- 需要错误处理的场景\n\n## 🧠 推理分析类\n\n### 5. **CoT** 思维链\n**最佳使用场景：**\n- 需要逐步推理的复杂问题\n- 医疗诊断、金融分析等专业领域\n- 需要展示推理过程的任务\n\n**适用时机：**\n- 问题解决需要多个步骤\n- 需要可解释的AI决策\n- 专业判断类任务\n\n**实际应用示例：**\n- 医疗症状分析\n- 投资风险评估\n- 法律案例分析\n\n### 6. **O1-STYLE** 高级推理\n**最佳使用场景：**\n- 需要深度思考的复杂问题\n- 科研、学术分析\n- 需要自我反思和质量评估的任务\n\n**适用时机：**\n- 高难度问题求解\n- 需要多角度分析\n- 质量要求极高的场景\n\n**实际应用示例：**\n- 科学研究问题\n- 复杂数学证明\n- 战略决策分析\n\n### 7. **RISE** 递归改进\n**最佳使用场景：**\n- 需要多轮优化的任务\n- 创作类工作的迭代改进\n- 需要持续学习的场景\n\n**适用时机：**\n- 初始方案不够完善\n- 需要反复打磨的内容\n- 追求完美的项目\n\n**实际应用示例：**\n- 文章写作优化\n- 产品设计迭代\n- 学习计划调整\n\n## 🎨 创作生成类\n\n### 8. **DRAW** 绘画优化\n**最佳使用场景：**\n- AI绘画工具使用\n- 视觉创作项目\n- 艺术设计需求\n\n**适用时机：**\n- 使用Midjourney、DALL-E等工具\n- 需要精确控制画面元素\n- 商业设计项目\n\n**实际应用示例：**\n- 产品宣传图设计\n- 插画创作\n- 概念艺术设计\n\n### 9. **VARI** 变分规划\n**最佳使用场景：**\n- 内容生成任务\n- 需要数学化建模的创作\n- 大规模内容生产\n\n**适用时机：**\n- 自动化内容生成\n- 需要优化生成策略\n- 批量创作需求\n\n**实际应用示例：**\n- 自动文章生成\n- 个性化推荐内容\n- 营销文案批量生产\n\n## 🔧 优化改进类\n\n### 10. **Meta Prompting** 元优化\n**最佳使用场景：**\n- 现有提示词效果不佳\n- 需要提高token使用效率\n- 提示词工程优化\n\n**适用时机：**\n- 提示词过于冗长\n- 输出质量不稳定\n- 成本控制需求\n\n**实际应用示例：**\n- 企业AI应用优化\n- 提示词库管理\n- 性能调优项目\n\n### 11. **MicrOptimization** 微调优化\n**最佳使用场景：**\n- 现有提示词基础较好但需要提升\n- 细节优化需求\n- 渐进式改进\n\n**适用时机：**\n- 提示词已有基础版本\n- 需要增加复杂度\n- 精细化调整需求\n\n## 🎯 专业算法类\n\n### 12. **Q*** 学习路径规划\n**最佳使用场景：**\n- 教育培训领域\n- 个性化学习方案\n- 智能推荐系统\n\n**适用时机：**\n- 需要长期规划\n- 个性化需求强烈\n- 效果可量化的场景\n\n**实际应用示例：**\n- 在线教育平台\n- 职业发展规划\n- 技能提升路径\n\n## 🛠️ 灵活定制类\n\n### 13. **Custom** 自定义\n**最佳使用场景：**\n- 特殊需求无法用标准模板满足\n- 行业特定的应用场景\n- 实验性项目\n\n**适用时机：**\n- 标准模板都不适用\n- 需要完全自主控制\n- 创新性尝试\n\n## 📊 选择建议矩阵\n\n| 任务复杂度 | 推理需求 | 创作需求 | 优化需求 | 推荐模板 |\n|------------|----------|----------|----------|----------|\n| 低 | 低 | 低 | 无 | OpenAI, Custom |\n| 中 | 中 | 低 | 无 | CO-STAR, CRISPE |\n| 高 | 高 | 低 | 无 | CoT, O1-STYLE |\n| 中 | 低 | 高 | 无 | DRAW, VARI |\n| 任意 | 任意 | 任意 | 有 | Meta Prompting, MicrOptimization |\n| 高 | 高 | 中 | 有 | RISE, Q* |\n\n选择提示词模板时，建议根据具体任务的复杂度、推理要求、创作需求和优化需求来综合考虑，选择最匹配的模板类型。\n", "timestamp": "2025-05-26T09:44:05.977Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/302workspace/302_prompt_generator", "path_name": "src/lib/prompt.ts", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 94, "endColumn": 0}}]}]}]}