{"name": "git-doc", "path": "/Users/<USER>/workspace/lovableworkspace/git-doc", "workspace_id": "54ab555f25262f6deee6cb45dcf16c3a", "conversations": [{"id": "89b3ae24-2111-43f2-a99d-61df96b79694", "name": "", "created_at": "2025-04-27T10:00:09.206Z", "last_interacted_at": "2025-04-27T10:00:09.206Z", "messages": []}, {"id": "538af4e9-6e4a-4fbf-a776-36b6a0481802", "name": "", "created_at": "2025-04-27T10:00:09.217Z", "last_interacted_at": "2025-04-27T10:00:18.823Z", "messages": [{"request_id": "e6be9ddc-0da6-4f88-80df-e2a1eccc0efd", "request_message": "Provide a short summary of this project", "response_text": "", "timestamp": "2025-04-27T10:00:18.838Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 73, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/data/markdown-content.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 99, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 48, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "vite.config.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 22, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "index.html", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 27, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/integrations/supabase/client.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 10, "endColumn": 87}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "package.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 57, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "package.json", "full_range": {"startLineNumber": 68, "startColumn": 0, "endLineNumber": 88, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/Index.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 24, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "supabase/config.toml", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 0, "endColumn": 35}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/main.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 5, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/MainLayout.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "components.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 19, "endColumn": 1}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "eslint.config.js", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 29, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/sonner.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 29, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "tailwind.config.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 20, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "tailwind.config.ts", "full_range": {"startLineNumber": 75, "startColumn": 0, "endLineNumber": 101, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "tsconfig.app.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 30, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/MarkdownRenderer.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 18, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/MarkdownRenderer.tsx", "full_range": {"startLineNumber": 40, "startColumn": 0, "endLineNumber": 67, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/Tutorial.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 12, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/Tutorial.tsx", "full_range": {"startLineNumber": 40, "startColumn": 0, "endLineNumber": 75, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/Tutorial.tsx", "full_range": {"startLineNumber": 103, "startColumn": 0, "endLineNumber": 129, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/Tutorial.tsx", "full_range": {"startLineNumber": 198, "startColumn": 0, "endLineNumber": 235, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/TutorialList.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 10, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/pages/TutorialList.tsx", "full_range": {"startLineNumber": 25, "startColumn": 0, "endLineNumber": 55, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "tsconfig.node.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 22, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/Header.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/App.css", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": ".giti<PERSON>re", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 24, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/carousel.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 20, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/carousel.tsx", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 84, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "package-lock.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 24, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/Logo.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/tutorial/ContentDisplay.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 23, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "postcss.config.js", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 6, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/index.css", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/integrations/supabase/types.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 46, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/chart.tsx", "full_range": {"startLineNumber": 33, "startColumn": 0, "endLineNumber": 52, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/chart.tsx", "full_range": {"startLineNumber": 55, "startColumn": 0, "endLineNumber": 65, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/sidebar.tsx", "full_range": {"startLineNumber": 128, "startColumn": 0, "endLineNumber": 163, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/lovableworkspace/git-doc", "path_name": "src/components/ui/sheet.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 14, "endColumn": 0}}]}]}]}