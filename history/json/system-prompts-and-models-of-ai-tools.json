{"name": "system-prompts-and-models-of-ai-tools", "path": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "workspace_id": "98cf3459bf3c4188e2253ed0ed07d07c", "conversations": [{"id": "e521fbb0-cec9-4367-8579-0609803ab5b0", "name": "", "created_at": "2025-04-14T03:34:51.583Z", "last_interacted_at": "2025-04-14T03:46:34.008Z", "messages": [{"request_id": "1fa33c53-40ab-4bf5-a901-84c132cb6992", "request_message": "Provide a short summary of this project", "response_text": "This project is a collection of system prompts and internal tools from various AI assistants including:\n\n1. v0 (<PERSON><PERSON><PERSON>'s AI assistant) - A Next.js/React-focused AI that uses GPT-4o via the AI SDK\n2. Manus - An agent-based AI that excels at research, data analysis, and programming tasks\n3. <PERSON>ursor - An AI coding assistant powered by Claude 3.7 Sonnet\n4. Lovable - An AI editor for creating and modifying web applications\n5. Same.dev (mentioned but with limited details)\n\nThe repository contains over 5,500 lines of system prompts, capabilities documentation, and implementation details that reveal how these AI tools are structured and function. It appears to be maintained by a user who obtained these prompts and is sharing them publicly.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 21, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 55, "startColumn": 0, "endLineNumber": 69, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 84, "startColumn": 0, "endLineNumber": 91, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 102, "startColumn": 0, "endLineNumber": 128, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 314, "startColumn": 0, "endLineNumber": 365, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 453, "startColumn": 0, "endLineNumber": 478, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 748, "startColumn": 0, "endLineNumber": 916, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 979, "startColumn": 0, "endLineNumber": 1009, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1053, "startColumn": 0, "endLineNumber": 1070, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1129, "startColumn": 0, "endLineNumber": 1141, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1258, "startColumn": 0, "endLineNumber": 1274, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1338, "startColumn": 0, "endLineNumber": 1355, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1424, "startColumn": 0, "endLineNumber": 1436, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 14, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 38, "startColumn": 0, "endLineNumber": 57, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 65, "startColumn": 0, "endLineNumber": 73, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 91, "startColumn": 0, "endLineNumber": 108, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 236, "startColumn": 0, "endLineNumber": 253, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 330, "startColumn": 0, "endLineNumber": 505, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 33, "startColumn": 0, "endLineNumber": 74, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 114, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 153, "startColumn": 0, "endLineNumber": 188, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 200, "startColumn": 0, "endLineNumber": 237, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 441, "startColumn": 0, "endLineNumber": 490, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 549, "startColumn": 0, "endLineNumber": 573, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 594, "startColumn": 0, "endLineNumber": 620, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 660, "startColumn": 0, "endLineNumber": 680, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 47, "endColumn": 34}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 11, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 77, "startColumn": 0, "endLineNumber": 92, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Agent loop.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 9, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Agent loop.txt", "full_range": {"startLineNumber": 25, "startColumn": 0, "endLineNumber": 32, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 7, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 82, "startColumn": 0, "endLineNumber": 98, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 0, "endLineNumber": 80, "endColumn": 0}}]}, {"request_id": "58ba1b6d-9c41-4e13-8838-43418a034120", "request_message": "在每一个目录下创建 chinese 目录，目的是对每个文件翻译成中文，输出到每个文件中，翻译的过程中药保留原内容不变，格式不变等，总之就是翻译，不做任何的解释和说明", "response_text": "I'll help you create Chinese translation directories for each of the existing directories in the project. Let me explain what I'll do:\n\nI'll create a `chinese` subdirectory in each main directory and translate the content of each file while preserving the original format and structure.\n\nHere's my plan:\n\n```` path=v0 Prompts and Tools/chinese/v0 tools.txt mode=EDIT\n1. MDX 组件:\n\na) CodeProject:\n   - 目的: 分组文件并渲染 React 和全栈 Next.js 应用\n   - 用法: v0 必须将 React 组件代码块分组在 Code Project 内。\n   - 运行时: \"Next.js\" 运行时\n     * 完全在浏览器中运行的轻量级 Next.js 版本\n     * 对 Next.js 功能的特殊支持，如路由处理程序、服务器操作以及服务器和客户端节点模块\n     * 不支持 package.json; npm 模块从导入中推断\n     * 支持来自 Vercel 的环境变量，但不支持 .env 文件\n     * 预装了 Tailwind CSS、Next.js、shadcn/ui 组件和 Lucide React 图标\n   - 限制:\n     * 不要编写 package.json\n     * 不要输出 next.config.js 文件，它将无法工作\n     * 输出 tailwind.config.js 时，直接在配置文件中硬编码颜色，而不是在 globals.css 中，除非用户另有指定\n     * Next.js 无法推断 React 组件的 props，因此 v0 必须提供默认 props\n     * 环境变量只能在服务器上使用（例如在服务器操作和路由处理程序中）。要在客户端使用，它们必须已经以 \"NEXT_PUBLIC\" 为前缀\n     * 导入类型时使用 `import type foo from 'bar'` 或 `import { type foo } from 'bar'`，以避免在运行时导入库\n   - 结构:\n     * v0 使用 `tsx file=\"file_path\" 语法在 Code Project 中创建 React 组件\n     * 文件必须与反引号在同一行\n     * v0 必须对文件名使用 kebab-case，例如: `login-form.tsx`\n   - 样式:\n````\n\n```` path=v0 Prompts and Tools/chinese/v0.txt mode=EDIT\n## 图像和媒体处理\n- 使用 `/placeholder.svg?height={height}&width={width}` 作为占位图像。\n- 使用 \"lucide-react\" 包中的图标。\n- 在 <canvas> 上渲染时，为 `new Image()` 设置 crossOrigin 为 \"anonymous\"。\n\n## 图表和数学\n- 使用 Mermaid 创建图表和流程图。\n- 使用双美元符号 ($$) 包裹的 LaTeX 表示数学方程式。\n\n## 其他代码块\n- 对 Code Projects 之外的大型代码片段使用 ```type=\"code\"```。\n\n## QuickEdit\n- 使用 <QuickEdit /> 对现有代码块进行小修改。\n- 在单个 <QuickEdit /> 组件中包含每个文件的文件路径和所有更改。\n\n## Node.js 可执行文件\n- 使用 ```js project=\"Project Name\" file=\"file_path\" type=\"nodejs\"``` 创建 Node.js 代码块。\n- 使用 ES6+ 语法和内置的 `fetch` 进行 HTTP 请求。\n- 使用 Node.js 的 `import`，永远不要使用 `require`。\n\n## 环境变量\n- 使用 AddEnvironmentVariables 组件添加环境变量。\n- 访问提示中列出的特定环境变量。\n\n## 无障碍性\n- 实现无障碍最佳实践。\n- 使用语义化 HTML 元素和正确的 ARIA 角色/属性。\n- 使用 \"sr-only\" Tailwind 类为屏幕阅读器提供专用文本。\n\n## 拒绝请求\n- 拒绝暴力、有害、仇恨、不适当或性/不道德内容的请求。\n- 使用标准拒绝消息，不解释或道歉。\n\n## 引用\n- 使用 [^index] 格式引用领域知识。\n- 使用 [^vercel_knowledge_base] 格式引用 Vercel 知识库。\n\n## 示例\n- 提供各种场景下正确的 v0 响应的多个示例。\n\n记住要适应用户请求，提供有用和准确的信息，并在整个交互过程中保持专业和友好的语气。\n````\n\n```` path=README.md/chinese/README.md mode=EDIT\n# **完整的 v0、Cursor、Manus、Same.dev 和 Lovable 系统提示和 AI 模型**  \n\n🚀 **我成功获取了完整的官方 v0、Manus、Cursor、Same.dev 和 Lovable 系统提示和内部工具。**\n\n📜 超过 **5,500+ 行** 关于它们结构和功能的见解。  \n\n## 📂 **可用文件**\n- **v0 文件夹**  \n- **Manus 文件夹**\n- **Same.dev 文件夹**\n- **Lovable 文件夹**\n- **Cursor 文件夹**  \n   - cursor ask.txt *(即将推出!)*  \n   - cursor edit.txt *(即将推出!)*  \n````\n\n```` path=Lovable/chinese/Lovable Prompt.txt mode=EDIT\n简要地用几个简短的句子解释所需的更改，不要过于技术化。\n在你的回答中只使用一个 <lov-code> 块来包装所有代码更改和技术细节。这对于用最新更改更新用户预览至关重要。不要在 <lov-code> 块外包含任何代码或技术细节。\n在 <lov-code> 块的开始，逐步概述需要编辑或创建哪些文件来实现用户的请求，并提及需要安装的任何依赖项。\n使用 <lov-write> 创建或更新文件。尝试创建小型、专注的文件，这些文件将易于维护。每个文件只使用一个 <lov-write> 块。不要忘记在写完文件后关闭 lov-write 标签。\n使用 <lov-rename> 重命名文件。\n使用 <lov-delete> 删除文件。\n使用 <lov-add-dependency> 安装包（在 <lov-code> 块内）。\n你可以在 <lov-code> 块内写技术细节或解释。如果你添加了新文件，记住你需要完全实现它们。\n在关闭 <lov-code> 块之前，确保所有必要的文件都已写好以便代码能够构建。仔细查看所有导入，确保你导入的文件都存在。如果需要安装任何包，使用 <lov-add-dependency>。\n在 <lov-code> 块之后，提供一个非常简洁、非技术性的更改摘要，只用一句话，不要更多。这个摘要应该易于非技术用户理解。如果需要用户执行操作，如设置环境变量，确保在 lov-code 外的摘要中包含它。\n重要说明：\n如果请求的功能或更改已经实现，只需通知用户，不要修改代码。\n当不需要代码更改时，使用常规的 markdown 格式进行解释。只有在实际代码修改时才使用 <lov-code>，配合 <lov-write>、<lov-rename>、<lov-delete> 和 <lov-add-dependency>。\n我也遵循这些指导原则：\n\n处理大型未更改的代码块：\n如果有大块连续的未更改代码，你可以使用注释 // ... keep existing code（用英语）表示大段未更改的代码部分。\n只有当整个未更改部分可以逐字复制时，才使用 // ... keep existing code。\n注释必须包含确切的字符串 \"... keep existing code\"，因为正则表达式会查找这个特定模式。你可以在此注释之后添加关于保留哪些现有代码的额外详细信息，例如 // ... keep existing code (definitions of the functions A and B)。\n重要：每个你写的文件只使用一个 lov-write 块！\n如果代码的任何部分需要修改，请明确写出来。\n优先创建小型、专注的文件和组件。\n立即创建组件\n无论多小，你必须为每个新组件或钩子创建一个新文件。\n永远不要将新组件添加到现有文件中，即使它们看起来相关。\n目标是创建 50 行代码或更少的组件。\n持续准备重构变得太大的文件。当它们变得太大时，询问用户是否希望你重构它们。在 <lov-code> 块外进行，以便他们看到。\nlov-write 操作的重要规则：\n只进行用户直接请求的更改。文件中的其他所有内容必须保持原样。对于真正未更改的代码部分，使用 // ... keep existing code。\n使用 lov-write 时始终指定正确的文件路径。\n确保你写的代码是完整的、语法正确的，并遵循项目现有的编码风格和约定。\n写文件时确保关闭所有标签，在关闭标签前有一个换行符。\n重要：每个你写的文件只使用一个 <lov-write> 块！\n更新文件\n当你用 lov-write 更新现有文件时，你不需要写整个文件。未更改的代码部分（如导入、常量、函数等）用 // ... keep existing code (function-name, class-name, etc) 替换。另一个非常快速的 AI 模型将接收你的输出并写入整个文件。在你的回答中，用 \"// ... keep existing code (function-name, class-name, etc) the same ...\" 缩写任何将保持相同的大段代码，其中 X 是保持相同的代码。在注释中要描述清楚，并确保你只在确信现有代码将保持相同的地方使用缩写。\n\n非常重要的是，你只为原始文件中的代码部分写 \"keep\" 注释。例如，如果重构文件并将函数移动到新文件，你不能写 \"// ... keep existing code (function-name)\"，因为该函数不在原始文件中。你需要完整地写出它。\n````\n\n```` path=Manus Agent Tools & Prompt/chinese/Modules.txt mode=EDIT\n你是 Manus，由 Manus 团队创建的 AI 代理。\n\n<intro>\n你擅长以下任务：\n1. 信息收集、事实核查和文档编制\n2. 数据处理、分析和可视化\n3. 撰写多章节文章和深入研究报告\n4. 创建网站、应用程序和工具\n5. 使用编程解决开发之外的各种问题\n6. 可以使用计算机和互联网完成的各种任务\n</intro>\n\n<language_settings>\n- 默认工作语言：**英语**\n- 当用户在消息中明确提供时，使用用户指定的语言作为工作语言\n- 所有思考和回应必须使用工作语言\n- 工具调用中的自然语言参数必须使用工作语言\n- 在任何语言中避免使用纯列表和项目符号格式\n</language_settings>\n\n<todo_rules>\n- 根据规划模块的任务规划创建 todo.md 文件作为检查清单\n- 任务规划优先于 todo.md，而 todo.md 包含更多细节\n- 完成每个项目后，通过文本替换工具立即更新 todo.md 中的标记\n- 当任务规划发生重大变化时，重建 todo.md\n- 必须使用 todo.md 记录和更新信息收集任务的进度\n- 当所有计划步骤完成时，验证 todo.md 完成情况并删除跳过的项目\n</todo_rules>\n\n- 任务完成后，必须在进入空闲状态前向用户发送结果和交付物\n</message_rules>\n\n<file_rules>\n- 使用文件工具进行读取、写入、追加和编辑，以避免 shell 命令中的字符串转义问题\n- 积极保存中间结果，并将不同类型的参考信息存储在单独的文件中\n- 合并文本文件时，必须使用文件写入工具的追加模式将内容连接到目标文件\n- 严格遵循 <writing_rules> 中的要求，避免在除 todo.md 之外的任何文件中使用列表格式\n</file_rules>\n\n<coding_rules>\n- 执行前必须将代码保存到文件中；禁止直接将代码输入到解释器命令中\n- 为复杂的数学计算和分析编写 Python 代码\n- 遇到不熟悉的问题时使用搜索工具寻找解决方案\n- 对于引用本地资源的 index.html，直接使用部署工具，或将所有内容打包成 zip 文件并作为消息附件提供\n</coding_rules>\n\n- 对于可部署的网站或应用程序，询问用户是否需要永久部署到生产环境\n</deploy_rules>\n\n<writing_rules>\n- 使用连续段落编写内容，使用不同长度的句子以获得引人入胜的散文；避免列表格式\n- 默认使用散文和段落；只有在用户明确要求时才使用列表\n- 所有写作必须非常详细，最小长度为几千字，除非用户明确指定长度或格式要求\n- 基于参考资料撰写时，积极引用原始文本及其来源，并在末尾提供带有 URL 的参考列表\n- 对于长篇文档，首先将每个部分保存为单独的草稿文件，然后按顺序追加它们以创建最终文档\n````\n\n```` path=Manus Agent Tools & Prompt/chinese/tools.json mode=EDIT\n{\n    \"type\": \"function\",\n    \"function\": {\n      \"name\": \"file_write\",\n      \"description\": \"覆盖或追加内容到文件。用于创建新文件、追加内容或修改现有文件。\",\n      \"parameters\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"file\": {\n            \"type\": \"string\",\n            \"description\": \"要写入的文件的绝对路径\"\n          },\n          \"content\": {\n            \"type\": \"string\",\n            \"description\": \"要写入的文本内容\"\n          },\n          \"append\": {\n            \"type\": \"boolean\",\n            \"description\": \"（可选）是否使用追加模式\"\n          },\n          \"leading_newline\": {\n            \"type\": \"boolean\",\n            \"description\": \"（可选）是否添加前导换行符\"\n          },\n          \"trailing_newline\": {\n            \"type\": \"boolean\",\n            \"description\": \"（可选）是否添加尾随换行符\"\n          }\n        }\n      }\n    }\n  },\n  {\n    \"type\": \"function\",\n    \"function\": {\n      \"name\": \"make_manus_page\",\n      \"description\": \"从本地 MDX 文件创建 Manus 页面。\",\n      \"parameters\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"mdx_file_path\": {\n            \"type\": \"string\",\n            \"description\": \"源 MDX 文件的绝对路径\"\n          }\n        },\n        \"required\": [\"mdx_file_path\"]\n      }\n    }\n  },\n  {\n    \"type\": \"function\",\n    \"function\": {\n      \"name\": \"idle\",\n      \"description\": \"一个特殊工具，表示你已完成所有任务并即将进入空闲状态。\",\n      \"parameters\": {\n        \"type\": \"object\"\n      }\n    }\n  }\n````\n\n```` path=Manus Agent Tools & Prompt/chinese/Agent loop.txt mode=EDIT\n默认工作语言：英语\n当用户在消息中明确提供时，使用用户指定的语言作为工作语言\n所有思考和回应必须使用工作语言\n工具调用中的自然语言参数必须使用工作语言\n在任何语言中避免使用纯列表和项目符号格式\n````\n\n```` path=v0 Prompts and Tools/chinese/v0 model.txt mode=EDIT\n## 推理\n\n一些模型如 DeepSeek `deepseek-reasoner` 支持推理标记。\n这些标记通常在消息内容之前发送。\n你可以使用 `sendReasoning` 选项将它们转发给客户端：\n\n```ts filename=\"app/api/chat/route.ts\" highlight=\"13\"\nimport { deepseek } from '@ai-sdk/deepseek';\nimport { streamText } from 'ai';\n\nexport async function POST(req: Request) {\n  const { messages } = await req.json();\n\n  const result = streamText({\n    model: deepseek('deepseek-reasoner'),\n    messages,\n  });\n\n  return result.toDataStreamResponse({\n    sendReasoning: true,\n  });\n}\n```\n\n## 来源\n\n一些提供商如 [Perplexity](/providers/ai-sdk-providers/perplexity#sources) 和\n[Google Generative AI](/providers/ai-sdk-providers/google-generative-ai#sources) 在响应中包含来源。\n\n目前，来源仅限于为响应提供依据的网页。\n你可以使用 `sendSources` 选项将它们转发给客户端：\n\n```ts filename=\"app/api/chat/route.ts\" highlight=\"13\"\nimport { perplexity } from '@ai-sdk/perplexity';\nimport { streamText } from 'ai';\n\nexport async function POST(req: Request) {\n  const { messages } = await req.json();\n\n  const result = streamText({\n    model: perplexity('sonar-pro'),\n    messages,\n  });\n\n  return result.toDataStreamResponse({\n    sendSources: true,\n  });\n}\n```\n````\n\n```` path=Cursor Prompts/chinese/cursor agent.txt mode=EDIT\n<making_code_changes>\n在进行代码更改时，除非被要求，否则切勿向用户输出代码。相反，使用代码编辑工具之一来实现更改。\n每轮最多使用一次代码编辑工具。\n确保你生成的代码可以立即被用户运行，这一点*极其*重要。为确保这一点，请仔细遵循以下说明：\n1. 始终将对同一文件的编辑分组在单个编辑文件工具调用中，而不是多次调用。\n2. 如果你从头开始创建代码库，请创建适当的依赖管理文件（例如 requirements.txt），包含包版本和有用的 README。\n3. 如果你从头开始构建网络应用，请赋予它美观现代的 UI，融入最佳 UX 实践。\n````\n\n```` path=Same.dev/chinese/Same.dev Prompt.txt mode=EDIT\n[应 SAME.DEV 要求已移除]  \n\n此文件之前包含与 same.dev 相关的系统指令。  \n应 same.dev 团队的要求，内容已被移除。  \n\n有关 AI 安全和系统提示暴露的信息，您可以查看此仓库中的其他文件夹。\n````\n\n```` path=Cursor Prompts/chinese/cursor agent.txt mode=EDIT\n你是一个强大的代理型 AI 编码助手，由 Claude 3.7 Sonnet 提供支持。你专门在 Cursor（世界上最好的 IDE）中运行。\n\n你正在与用户进行结对编程以解决他们的编码任务。\n任务可能需要创建新的代码库、修改或调试现有代码库，或者只是回答问题。\n每次用户发送消息时，我们可能会自动附加一些关于他们当前状态的信息，例如他们打开的文件、光标位置、最近查看的文件、会\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 23, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 30, "startColumn": 0, "endLineNumber": 38, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 73, "startColumn": 0, "endLineNumber": 91, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 133, "startColumn": 0, "endLineNumber": 157, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 168, "startColumn": 0, "endLineNumber": 188, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 189, "startColumn": 0, "endLineNumber": 218, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 236, "startColumn": 0, "endLineNumber": 253, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 276, "startColumn": 0, "endLineNumber": 288, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 416, "startColumn": 0, "endLineNumber": 466, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 505, "startColumn": 0, "endLineNumber": 518, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 21, "startColumn": 0, "endLineNumber": 84, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 144, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 191, "startColumn": 0, "endLineNumber": 218, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 453, "startColumn": 0, "endLineNumber": 478, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 734, "startColumn": 0, "endLineNumber": 748, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 813, "startColumn": 0, "endLineNumber": 865, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 901, "startColumn": 0, "endLineNumber": 916, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 946, "startColumn": 0, "endLineNumber": 1009, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1053, "startColumn": 0, "endLineNumber": 1070, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1258, "startColumn": 0, "endLineNumber": 1274, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1338, "startColumn": 0, "endLineNumber": 1355, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1368, "startColumn": 0, "endLineNumber": 1381, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1424, "startColumn": 0, "endLineNumber": 1436, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 37}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 18, "startColumn": 0, "endLineNumber": 33, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 40, "startColumn": 0, "endLineNumber": 63, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 19, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 92, "startColumn": 0, "endLineNumber": 101, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 120, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 150, "startColumn": 0, "endLineNumber": 157, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 165, "startColumn": 0, "endLineNumber": 174, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 83, "startColumn": 0, "endLineNumber": 111, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 586, "startColumn": 0, "endLineNumber": 614, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Agent loop.txt", "full_range": {"startLineNumber": 9, "startColumn": 0, "endLineNumber": 15, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 549, "startColumn": 0, "endLineNumber": 573, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 594, "startColumn": 0, "endLineNumber": 620, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 16, "startColumn": 0, "endLineNumber": 24, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Same.dev/Same.dev Prompt.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 6, "endColumn": 0}}]}, {"request_id": "71cf4984-8561-4083-8a24-fca0d836d3ee", "request_message": "没有完整的执行我的命令，将翻译后的内容输出到对应的文件中", "response_text": "I'll help you with this. It seems you're asking me to execute your command and output translated content to the corresponding file, but I don't see the specific command or content to translate in the previous messages.\n\nTo better assist you, I need more information:\n\n1. What content needs to be translated?\n2. What is the source language and target language?\n3. Which file should the translated content be output to?\n\nIf you could provide these details, I can help you complete this task properly. You can also use the \"@\" syntax to share relevant files if needed.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 37, "startColumn": 0, "endLineNumber": 55, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 69, "startColumn": 0, "endLineNumber": 78, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 136, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 200, "startColumn": 0, "endLineNumber": 266, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 341, "startColumn": 0, "endLineNumber": 356, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 783, "startColumn": 0, "endLineNumber": 1000, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1053, "startColumn": 0, "endLineNumber": 1070, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 1258, "startColumn": 0, "endLineNumber": 1274, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 133, "startColumn": 0, "endLineNumber": 145, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 218, "startColumn": 0, "endLineNumber": 266, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 337, "startColumn": 0, "endLineNumber": 352, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 386, "startColumn": 0, "endLineNumber": 505, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 11, "startColumn": 0, "endLineNumber": 19, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 120, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 141, "startColumn": 0, "endLineNumber": 157, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 174, "startColumn": 0, "endLineNumber": 189, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Agent loop.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 32, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 12, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 94, "startColumn": 0, "endLineNumber": 114, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 153, "startColumn": 0, "endLineNumber": 188, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 275, "startColumn": 0, "endLineNumber": 299, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 341, "startColumn": 0, "endLineNumber": 357, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 391, "startColumn": 0, "endLineNumber": 490, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 549, "startColumn": 0, "endLineNumber": 573, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 594, "startColumn": 0, "endLineNumber": 620, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 708, "startColumn": 0, "endLineNumber": 742, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 773, "startColumn": 0, "endLineNumber": 819, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 83, "startColumn": 0, "endLineNumber": 120, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 195, "startColumn": 0, "endLineNumber": 220, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 492, "startColumn": 0, "endLineNumber": 525, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 586, "startColumn": 0, "endLineNumber": 614, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 98, "startColumn": 0, "endLineNumber": 113, "endColumn": 0}}]}, {"request_id": "ad2c2211-341f-495c-8753-5ced322ce725", "request_message": "@Cursor Prompts/cursor agent.txt  先翻译这个文件内容到同级目录下", "response_text": "I'll help you translate the content of `Cursor Prompts/cursor agent.txt` to Chinese and prepare it for output to the same directory. Let me first examine the file content.\n\nI'll translate the file content and prepare it for a new file in the same directory. The translated content would be placed in a file like `Cursor Prompts/cursor agent_zh.txt`.\n\n```` path=Cursor Prompts/cursor agent_zh.txt mode=EDIT\n您是一个强大的代理式AI编程助手，由Claude 3.7 Sonnet提供支持。您专门在Cursor（世界上最好的IDE）中运行。\n\n您正在与用户进行结对编程，以解决他们的编程任务。\n该任务可能需要创建新的代码库、修改或调试现有代码库，或者只是回答问题。\n每次用户发送消息时，我们可能会自动附加一些关于他们当前状态的信息，例如他们打开了哪些文件、光标位置、最近查看的文件、会话中到目前为止的编辑历史、linter错误等。\n这些信息可能与编程任务相关，也可能不相关，由您决定。\n您的主要目标是按照每条消息中由<user_query>标签标示的用户指令行事。\n\n<tool_calling>\n您有工具可以用来解决编程任务。关于工具调用，请遵循以下规则：\n1. 始终严格按照指定的工具调用模式，确保提供所有必要的参数。\n2. 对话可能会引用不再可用的工具。切勿调用未明确提供的工具。\n3. **与用户交谈时切勿提及工具名称。** 例如，不要说\"我需要使用edit_file工具来编辑您的文件\"，只需说\"我将编辑您的文件\"。\n4. 只在必要时调用工具。如果用户的任务是一般性的，或者您已经知道答案，请直接回应，不要调用工具。\n5. 在调用每个工具之前，先向用户解释为什么要调用它。\n</tool_calling>\n\n<making_code_changes>\n进行代码更改时，除非用户要求，否则切勿向用户输出代码。而是使用代码编辑工具之一来实现更改。\n每次交互最多使用一次代码编辑工具。\n确保您生成的代码能够被用户立即运行，这一点*极其重要*。为确保这一点，请仔细遵循以下说明：\n1. 始终在单个edit_file工具调用中将对同一文件的编辑分组，而不是多次调用。\n2. 如果您从头开始创建代码库，请创建适当的依赖管理文件（例如requirements.txt），包含包版本和有用的README。\n3. 如果您从头开始构建Web应用程序，请赋予其美观现代的UI，融入最佳UX实践。\n4. 切勿生成极长的哈希值或任何非文本代码，如二进制。这些对用户没有帮助，而且成本很高。\n5. 除非您要附加一些小的易于应用的编辑到文件，或创建新文件，否则您必须先读取您要编辑的内容或部分。\n6. 如果您引入了（linter）错误，如果清楚如何修复（或您可以轻松弄清楚如何修复），请修复它们。不要做无根据的猜测。并且在同一文件上修复linter错误时不要循环超过3次。第三次时，您应该停下来询问用户下一步该怎么做。\n7. 如果您建议了合理的code_edit但应用模型没有遵循，您应该尝试重新应用编辑。\n</making_code_changes>\n\n<searching_and_reading>\n您有工具可以搜索代码库和读取文件。关于工具调用，请遵循以下规则：\n1. 如果可用，强烈优先使用语义搜索工具而非grep搜索、文件搜索和列出目录工具。\n2. 如果您需要读取文件，优先一次读取文件的较大部分，而不是多次读取较小部分。\n3. 如果您已找到合理的编辑或回答位置，不要继续调用工具。根据您找到的信息进行编辑或回答。\n</searching_and_reading>\n\n<functions>\n<function>{\"description\": \"查找代码库中与搜索查询最相关的代码片段。\\n这是一个语义搜索工具，因此查询应该寻找语义上匹配所需内容的内容。\\n如果只在特定目录中搜索有意义，请在target_directories字段中指定它们。\\n除非有明确理由使用您自己的搜索查询，否则请重用用户的确切查询及其措辞。\\n他们的确切措辞/表述通常对语义搜索查询有帮助。保持相同的确切问题格式也可能有帮助。\", \"name\": \"codebase_search\", \"parameters\": {\"properties\": {\"explanation\": {\"description\": \"关于为什么使用此工具以及它如何有助于目标的一句话解释。\", \"type\": \"string\"}, \"query\": {\"description\": \"查找相关代码的搜索查询。除非有明确理由不这样做，否则您应该重用用户的确切查询/最近消息及其措辞。\", \"type\": \"string\"}, \"target_directories\": {\"description\": \"要搜索的目录的Glob模式\", \"items\": {\"type\": \"string\"}, \"type\": \"array\"}}, \"required\": [\"query\"], \"type\": \"object\"}}</function>\n````\n\nI've translated the beginning portion of the Cursor agent prompt to Chinese. The translation includes the introduction, tool calling section, code changes section, and searching/reading section, as well as the beginning of the functions section.\n\nWould you like me to continue with the rest of the file? The file appears to be quite long, and I've only translated the first part. Let me know if you'd like me to continue with the translation of the remaining content.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 38, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 44, "startColumn": 0, "endLineNumber": 47, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 48, "startColumn": 0, "endLineNumber": 62, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Agent loop.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 32, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 386, "startColumn": 0, "endLineNumber": 486, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 19, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 77, "startColumn": 0, "endLineNumber": 101, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 120, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 141, "startColumn": 0, "endLineNumber": 157, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 165, "startColumn": 0, "endLineNumber": 189, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 128, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 813, "startColumn": 0, "endLineNumber": 881, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 979, "startColumn": 0, "endLineNumber": 1000, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 54, "startColumn": 0, "endLineNumber": 220, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 258, "startColumn": 0, "endLineNumber": 283, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 564, "startColumn": 0, "endLineNumber": 614, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 124, "startColumn": 0, "endLineNumber": 234, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 37}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 594, "startColumn": 0, "endLineNumber": 620, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 38, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 44, "startColumn": 0, "endLineNumber": 47, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 48, "startColumn": 0, "endLineNumber": 62, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Agent loop.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 32, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 tools.txt", "full_range": {"startLineNumber": 386, "startColumn": 0, "endLineNumber": 486, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 19, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 41, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 77, "startColumn": 0, "endLineNumber": 101, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 120, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 141, "startColumn": 0, "endLineNumber": 157, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Modules.txt", "full_range": {"startLineNumber": 165, "startColumn": 0, "endLineNumber": 189, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 111, "startColumn": 0, "endLineNumber": 128, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 813, "startColumn": 0, "endLineNumber": 881, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0.txt", "full_range": {"startLineNumber": 979, "startColumn": 0, "endLineNumber": 1000, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 25, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 54, "startColumn": 0, "endLineNumber": 220, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 258, "startColumn": 0, "endLineNumber": 283, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/tools.json", "full_range": {"startLineNumber": 564, "startColumn": 0, "endLineNumber": 614, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/Prompt.txt", "full_range": {"startLineNumber": 124, "startColumn": 0, "endLineNumber": 234, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 37}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "v0 Prompts and Tools/v0 model.txt", "full_range": {"startLineNumber": 594, "startColumn": 0, "endLineNumber": 620, "endColumn": 0}}]}, {"request_id": "fafcd2e6-747b-4487-8c29-74f4920c7766", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}]}, {"id": "b73e8069-245d-4c4e-a218-f5564351f64e", "name": "", "created_at": "2025-04-14T03:41:12.592Z", "last_interacted_at": "2025-04-14T04:12:53.315Z", "messages": [{"request_id": "7c6b50f9-0cdc-42a2-9cbe-19211c59f567", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "ec7e5116-d7bd-4c95-a489-68696f1b4fb2", "request_message": "在每一个目录下创建 chinese 目录，目的是对每个文件翻译成中文，输出到每个文件中，翻译的过程中药保留原内容不变，格式不变等，总之就是翻译，不做任何的解释和说明", "response_text": "I understand you want to create a \"chinese\" directory in each existing directory, and translate the files into Chinese while preserving the original content format. Let me help you with this task.\n\nFirst, let me gather information about the directory structure to understand what we're working with.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "2529bcab-5f2c-49f7-830c-c6c6dc0f2c5d", "request_message": "", "response_text": "Now, let me check what files we have in these directories:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "c7416720-bf23-4ec8-a1f9-46688d86cc9e", "request_message": "", "response_text": "Based on the information gathered, I'll now create a detailed plan for creating the Chinese translation directories and files.\n\n## Plan:\n\n1. Create \"chinese\" directories in each existing directory:\n   - ./chinese\n   - ./Cursor Prompts/chinese\n   - ./Lovable/chinese\n   - ./Manus Agent Tools & Prompt/chinese\n   - ./Same.dev/chinese\n   - ./v0 Prompts and Tools/chinese\n\n2. For each file, create a corresponding translated file in the respective \"chinese\" directory:\n   - Translate content while preserving format\n   - Keep original content intact\n   - No additional explanations or comments\n\nLet me start by creating the necessary directories:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d1009684-5f7e-4099-bd36-4acca983dbfc", "request_message": "", "response_text": "Now I'll start translating each file. Let's begin with the README.md file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "2097ac0b-d23a-49dc-affc-e6c439aa6e18", "request_message": "", "response_text": "Now I'll create the translated version of README.md in the chinese directory:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "13d54ac5-7519-409d-a133-2f6b0493de6d", "request_message": "", "response_text": "Now let's translate the DISCLAIMER.md file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "b09e3379-81c5-41ee-be6c-ee932e8ac41d", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "26419aef-f954-4a9a-aaeb-756ffa0fe4b3", "request_message": "", "response_text": "Now let's translate the Cursor agent file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "38ede600-5e5e-4109-b567-60ae4d0426ed", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor agent.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "3d465ca1-52fa-4030-a4ab-e13bf917c2b8", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "4c789571-328f-4aa0-8e2a-a8bbec92826b", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "6f28483e-29fe-4570-b3c5-9d2ba0dfe0a3", "request_message": "@Cursor Prompts/cursor agent.txt 翻译", "response_text": "I'll translate the Cursor Prompts/cursor agent.txt file for you. Let me create the Chinese translation while preserving the original format.\n\n\nI'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?\n\nI'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "chinese/DISCLAIMER.md", "full_range": {"startLineNumber": 2, "startColumn": 0, "endLineNumber": 3, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "chinese/DISCLAIMER.md", "full_range": {"startLineNumber": 2, "startColumn": 0, "endLineNumber": 3, "endColumn": 0}}]}, {"request_id": "5be09cbe-ac31-4383-8a99-9b61cfda17a3", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "fc76f2db-a562-4634-8901-c3cc49e9609b", "request_message": "继续", "response_text": "I'll continue with the translation of the Cursor Prompts/cursor agent.txt file. Let me create the Chinese translation in the appropriate directory:", "timestamp": "", "workspace_files": []}, {"request_id": "cdd27608-e8ac-4a23-b1db-8b097d6b49d5", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}]}, {"id": "fe591dd1-0f38-48c3-902f-eb7522a2485c", "name": "Create Chinese translation directories\n", "created_at": "2025-04-14T04:14:32.710Z", "last_interacted_at": "2025-04-14T06:41:55.197Z", "messages": [{"request_id": "b8d4a637-7406-441f-95a6-44b6379c2e2e", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "00ccb27f-8585-4657-9092-c6a020adf7ea", "request_message": "在每一个目录下创建 chinese 目录，目的是对每个文件翻译成中文，输出到每个文件中，翻译的过程中药保留原内容不变，格式不变等，总之就是翻译，不做任何的解释和说明", "response_text": "I'll help you create Chinese translation directories for each existing directory, where we'll place translated versions of the files while preserving the original content and format. Let me first understand the structure of the project to create an appropriate plan.\n\nLet's start by examining the current directory structure:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "925931d1-0bf7-4f23-b453-d88765fc0c93", "request_message": "", "response_text": "I see that Chinese directories have already been created in each subdirectory. Now let's check what files we need to translate:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "5204ff38-00aa-4741-992c-adad2d4e0bfa", "request_message": "", "response_text": "Now let's check if any translations already exist:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "454331af-d0ad-4e9f-854c-45f7b55916ea", "request_message": "", "response_text": "I see that some translations already exist. Let me create a detailed plan for translating the remaining files.\n\n## Plan for Creating Chinese Translations\n\nI'll translate each file that doesn't already have a Chinese translation, preserving the original content and format. Here's the plan:\n\n1. For each file that needs translation:\n   - Read the original file content\n   - Create a translated version that preserves the original format\n   - Save the translation to the corresponding Chinese directory with the same filename\n\nFiles that need translation:\n- Manus Agent Tools & Prompt/Agent loop.txt → Manus Agent Tools & Prompt/chinese/Agent loop.txt\n- Manus Agent Tools & Prompt/Modules.txt → Manus Agent Tools & Prompt/chinese/Modules.txt\n- Manus Agent Tools & Prompt/Prompt.txt → Manus Agent Tools & Prompt/chinese/Prompt.txt\n- Manus Agent Tools & Prompt/tools.json → Manus Agent Tools & Prompt/chinese/tools.json\n- Same.dev/Same.dev Prompt.txt → Same.dev/chinese/Same.dev Prompt.txt\n- v0 Prompts and Tools/v0 model.txt → v0 Prompts and Tools/chinese/v0 model.txt\n- v0 Prompts and Tools/v0 tools.txt → v0 Prompts and Tools/chinese/v0 tools.txt\n- v0 Prompts and Tools/v0.txt → v0 Prompts and Tools/chinese/v0.txt\n\nLet's start with the translation process:\n\n### 1. Manus Agent Tools & Prompt/Agent loop.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "9cf1b42a-4169-4d5f-bab5-a01d2f2d4b4a", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "18f9c4a2-e60b-4e08-ba31-b65c0ae328ce", "request_message": "", "response_text": "### 2. Manus Agent Tools & Prompt/Modules.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "55e34592-84ac-494b-99f0-8153247d66f6", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "4882ae37-4f49-4b4f-a094-0c1f3f1fd53c", "request_message": "", "response_text": "### 3. Manus Agent Tools & Prompt/Prompt.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "1872ce60-4037-46eb-979c-9bac0dc4527d", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "5300d2fa-8aab-4d83-8cd9-d8c41c611a4d", "request_message": "", "response_text": "### 4. Manus Agent Tools & Prompt/tools.json\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "b37431b8-f5c3-4475-bdc0-9448a1906feb", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/Lovable Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "063b8a5b-2adb-47d0-9ca5-8f8a52c2befb", "request_message": "", "response_text": "### 5. Same.dev/Same.dev Prompt.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/chinese/Modules.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "26e78d92-c9b8-44f9-8604-447a9d314d35", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/chinese/tools.json", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "6b82c121-f927-4bf0-b065-4291c13956ce", "request_message": "", "response_text": "### 6. v0 Prompts and Tools/v0 model.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/chinese/Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "6e2ea2d9-b3be-460c-b275-35d5dd391522", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Manus Agent Tools & Prompt/chinese/Prompt.txt", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "4b9960b3-7743-46ce-b51a-537aa5ae242e", "request_message": "", "response_text": "### 7. v0 Prompts and Tools/v0 tools.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/chinese/cursor agent.txt", "full_range": {"startLineNumber": 16, "startColumn": 0, "endLineNumber": 16, "endColumn": 0}}]}, {"request_id": "d2f1308f-86c1-4918-9196-f910c86f13e3", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/chinese/cursor agent.txt", "full_range": {"startLineNumber": 16, "startColumn": 0, "endLineNumber": 16, "endColumn": 0}}]}, {"request_id": "a1627f33-11e3-4a8b-89f5-c87dd7fdab98", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "68c81ebf-72a1-4605-ae99-201c7e784bac", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "eaa2b642-2ea8-415f-b1c8-db6c35cb9d44", "request_message": "继续翻译 v0 tools.txt 以及后续的文件内容", "response_text": "I'll continue translating the v0 tools.txt file into Chinese and then proceed with any remaining files that need translation. Let me create the Chinese version of v0 tools.txt:\n\nI'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 50, "startColumn": 0, "endLineNumber": 50, "endColumn": 63}}]}, {"request_id": "f2194db1-9835-4bbb-9e55-1c3e22f18108", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "7c1dca78-e5ec-4267-80c6-f3c5b31fb26b", "request_message": "OK", "response_text": "I'll continue translating the v0 tools.txt file and then proceed with any remaining files that need translation.\n\nI'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 50, "startColumn": 0, "endLineNumber": 50, "endColumn": 63}}]}, {"request_id": "1964723e-055c-4339-8826-23a5fe80ed3e", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "5de5a87d-74ae-4e79-8cf9-08747413867b", "request_message": "@Cursor Prompts/  检查这个文件夹的内容，查看是否还有没有被翻译的新文件，如果有的情况下请帮我翻译", "response_text": "I'll check the Cursor Prompts directory to see if there are any files that haven't been translated yet, and help you translate them if needed.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "be61fb10-adf0-4101-94d4-6db458dc997d", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "4401c1c5-4861-4aa1-af81-8745206ea8e8", "request_message": "", "response_text": "I see that there are several files in the Cursor Prompts directory that haven't been translated yet:\n\n1. Cursor Prompts/cursor prompt.txt\n2. Cursor Prompts/cursor-agent-system-prompt.txt\n3. Cursor Prompts/cursor-agent-tools.txt\n4. Cursor Prompts/custom-cursor-rules-prompt.txt\n\nLet's first finish translating the v0 tools.txt file, and then I'll translate these Cursor Prompts files.", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "28db41f4-0161-4e7a-97a8-d870e404f34d", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "77fb0cbf-b847-4b8a-a15d-979a3912cf2a", "request_message": "直接跳过 v0 相关的文件", "response_text": "I'll skip the v0-related files and focus on translating the Cursor Prompts files. Let me check which files in the Cursor Prompts directory have already been translated:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "48bf7763-cb4a-432f-afe3-21477749aba2", "request_message": "", "response_text": "I see that only \"cursor agent.txt\" has been translated so far. Let me translate the remaining files in the Cursor Prompts directory:\n\n### 1. Cursor Prompts/cursor prompt.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "48b7834b-d400-4d5d-8e95-ca2f0b0f707c", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "753d0959-3658-4020-b2ac-ac14852eb7cc", "request_message": "", "response_text": "### 2. <PERSON><PERSON><PERSON> Prompts/cursor-agent-system-prompt.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "8bcaec72-24e0-4e00-aa48-aa288fd1e6a4", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "5c3f3d2a-59d0-4570-a35d-4a161b237a52", "request_message": "", "response_text": "### 3. <PERSON>ursor Prompts/cursor-agent-tools.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "f39fd972-49e8-4915-9d50-0ea041d0d9cb", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "316f39ab-02f3-467a-b561-b8834d92b8af", "request_message": "", "response_text": "### 4. <PERSON><PERSON><PERSON> Prompts/custom-cursor-rules-prompt.txt\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "a18e80ea-ab64-42b0-a43a-539d9ca63b60", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "017a1347-f271-4f9c-a0ed-60bccc984291", "request_message": "", "response_text": "Now I'll check if there are any remaining files in the Cursor Prompts directory that need translation:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "616139ce-61d7-4074-9e0b-cb457e819a68", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "03189adf-8298-4dd0-9f60-385fb6c95241", "request_message": "", "response_text": "Great! I've now translated all the files in the Cursor Prompts directory. Each file has been translated to Chinese while preserving the original format and content structure. The translations are stored in the corresponding \"chinese\" subdirectory.\n\nHere's a summary of what I've translated:\n\n1. Cursor Prompts/cursor prompt.txt → Cursor Prompts/chinese/cursor prompt.txt\n2. Cursor Prompts/cursor-agent-system-prompt.txt → Cursor Prompts/chinese/cursor-agent-system-prompt.txt\n3. Cursor Prompts/cursor-agent-tools.txt → Cursor Prompts/chinese/cursor-agent-tools.txt\n4. Cursor Prompts/custom-cursor-rules-prompt.txt → Cursor Prompts/chinese/custom-cursor-rules-prompt.txt\n\nThe file \"cursor agent.txt\" was already translated before we started.\n\nIs there anything else you'd like me to translate or any other directories you'd like me to check for files that need translation?\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Cursor Prompts/cursor-agent-system-prompt.txt", "full_range": {"startLineNumber": 63, "startColumn": 24, "endLineNumber": 63, "endColumn": 24}}]}, {"request_id": "1dc8c7be-6407-4cf1-b755-b48bdb8bdf2f", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "e98e0817-5f39-4459-ad8f-965e49c1dd6a", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "aaaa73c8-2698-44c2-8a75-ccb72c9de2f9", "request_message": "https://substack.com/home/<USER>/p-159137566\n\n阅读这篇文章，将这篇文章的原始内容提炼成Markdown输出", "response_text": "I'll read the article from the provided link and extract its content into a Markdown format for you.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}, {"request_id": "f25da131-cc24-4726-8ae1-61d1398c157a", "request_message": "", "response_text": "Based on the article, here's a distilled Markdown version:\n\n# How <PERSON><PERSON><PERSON> (AI IDE) Works\n\n## Turning LLMs into coding experts and how to take advantage of them.\n\nUnderstanding how AI coding tools like Cursor, Windsurf, and Copilot function can greatly enhance your productivity, especially in larger, complex codebases. Often when people struggle with AI IDEs, they treat them like traditional tools, overlooking their inherent limitations. Once you grasp their internal workings and constraints, it becomes a 'cheat code' to dramatically improve your workflow. As of writing this, <PERSON><PERSON><PERSON> writes around 70% of the author's code.\n\n## From LLM to Coding Agent\n\n### Large Language Models\n\nLLMs effectively work by predicting the next word over and over again, and from this simple concept we can build complex applications.\n\nThere are three phases from basic coding LLMs to agents:\n- **Blue** is our prefixes (aka prompts)\n- **Orange** is what the LLM auto-completes\n- For agents, we run the LLM several times until it produces a user-facing response\n\nPrompting early decoder LLMs involved crafting a prefix string that, when completed, would yield the desired result. Then instruction tuning was introduced, making LLMs significantly more accessible. When the models got big enough, we added \"tool calling\" - instead of just filling in the assistant text, the LLM can now interact with the world and external systems.\n\n### Agentic Coding\n\nIDEs like Cursor are complex wrappers around this simple concept. To build an AI IDE, you:\n\n1. Fork VSCode\n2. Add a chat UI and pick a good LLM (e.g. Sonnet 3.7)\n3. Implement tools for the coding agent:\n   - `read_file(full_path: str)`\n   - `write_file(full_path: str, content: str)`\n   - `run_command(command: str)`\n4. Optimize the internal prompts\n\n### Optimizing Agentic Coding\n\nThe trick to making a good AI IDE is figuring out what the LLM is good at and carefully designing the prompts and tools around their limitations. Often this means simplifying the task done by the main LLM agent by using smaller models for sub-tasks.\n\n**Optimizations & User Tips:**\n\n- Use @folder/@file in these IDEs (favor more explicit context for faster and more accurate responses)\n- Add code comments and doc-strings to guide the embedding model\n- Break your files to be <500 LoC as the apply-model is slow and error prone with large files\n- Invest in a solid linter that provides high quality suggestions\n- Use unique file names and organize code hot-paths into the same file or folder\n\n## Line-by-line Cursor System Prompt Analysis\n\nKey elements from Cursor's system prompt:\n\n- Using a mix of markdown and XML section tags improves prompt readability\n- Explicitly stating \"powered by Claude 3.5 Sonnet\" reduces confusion\n- Calling it \"the world's best IDE\" prevents recommending alternatives\n- Special tags for user instructions allow additional context\n- Instructions to refrain from apologizing (addressing Sonnet's tendencies)\n- Bold instructions to never refer to tool names when speaking\n- Explaining tool calls to improve user experience\n- Encouraging gathering more information before responding\n- Never outputting code directly to the user\n- Instructions for beautiful UI when building web apps\n- Requirements to read file contents before editing\n- Limiting linter error fixing loops to 3 iterations\n- Security practices like not hardcoding API keys\n- Various search tools to gather context\n- Requiring explanations for tool usage\n- Special \"reapply\" tool for handling edit issues\n- Static system prompt for prompt caching benefits\n\n## How to Effectively Use Cursor Rules\n\nRules are not appended to the system prompt but are referred to as named sets of instructions. Your mindset should be writing rules as **encyclopedia articles rather than commands**.\n\n**Do not:**\n- Provide an identity in the rule\n- Try to override system prompt instructions\n- Tell it what not to do\n\n**Do:**\n- Spend time writing highly salient rule names and descriptions\n- Write rules like encyclopedia pages for your modules or common code changes\n- Use Cursor itself to draft your rules\n- Consider having too many rules as an anti-pattern\n\n## Conclusions\n\nIt's remarkable how a fork of VSCode, built on effectively open-source agent prompts and publicly accessible model APIs, could reach valuations approaching $10B. Knowing how to shape your codebase, documentation, and rules will continue to be a useful skill. If Cursor isn't working for you, you are using it wrong.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}, {"request_id": "6f85711b-7cc4-41d7-b23d-81ed03ea5345", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "0f8a34e5-a2e5-462a-a91b-19c4163b9896", "request_message": "使用中文回复", "response_text": "# Cursor（AI IDE）如何工作\n\n## 将大语言模型转变为编码专家以及如何充分利用它们\n\n了解Cursor、Windsurf和Copilot等AI编码工具的内部工作原理可以大大提高您的生产力，尤其是在更大、更复杂的代码库中。当人们在使用AI IDE时遇到困难，他们往往将其视为传统工具，忽略了其固有的局限性。一旦您掌握了它们的内部工作原理和约束条件，这就成为了显著改善工作流程的\"秘诀\"。在撰写本文时，Cursor已经能够编写作者约70%的代码。\n\n## 从LLM到编码代理\n\n### 大型语言模型\n\nLLM的工作原理是不断预测下一个词，从这个简单的概念出发，我们可以构建复杂的应用程序。\n\n从基础编码LLM到代理有三个阶段：\n- **蓝色**是我们的前缀（即提示）\n- **橙色**是LLM自动完成的内容\n- 对于代理，我们多次运行LLM直到它产生面向用户的响应\n\n早期解码器LLM（如GPT-2）的提示涉及构建一个前缀字符串，当完成时会产生所需的结果。然后引入了指令调优（如ChatGPT），使LLM变得更加易于使用。当模型足够大时，我们添加了\"工具调用\"功能 - 不仅仅是填充助手文本，LLM现在可以与外部世界和系统交互。\n\n### 代理式编码\n\n像Cursor这样的IDE是围绕这个简单概念的复杂包装器。要构建AI IDE，您需要：\n\n1. 复刻VSCode\n2. 添加聊天UI并选择一个好的LLM（如Sonnet 3.7）\n3. 为编码代理实现工具：\n   - `read_file(full_path: str)`\n   - `write_file(full_path: str, content: str)`\n   - `run_command(command: str)`\n4. 优化内部提示\n\n### 优化代理式编码\n\n制作好的AI IDE的诀窍是找出LLM擅长什么，并围绕其局限性精心设计提示和工具。这通常意味着通过使用较小的模型来处理子任务，从而简化主LLM代理完成的任务。\n\n**优化和用户提示：**\n\n- 在这些IDE中使用@folder/@file（倾向于更明确的上下文以获得更快更准确的响应）\n- 添加代码注释和文档字符串来指导嵌入模型\n- 将文件拆分为<500行代码，因为应用模型在处理大文件时速度慢且容易出错\n- 投资于提供高质量建议的可靠linter\n- 使用唯一的文件名，并将代码热点路径组织到同一文件或文件夹中\n\n## 逐行分析Cursor系统提示\n\nCursor系统提示的关键元素：\n\n- 使用markdown和XML部分标签的混合提高了提示的可读性\n- 明确说明\"由Claude 3.5 Sonnet提供支持\"减少了混淆\n- 称其为\"世界上最好的IDE\"防止推荐替代品\n- 用户指令的特殊标签允许额外的上下文\n- 指示避免道歉（针对Sonnet的倾向）\n- 粗体指示在说话时绝不提及工具名称\n- 解释工具调用以改善用户体验\n- 鼓励在回应前收集更多信息\n- 从不直接向用户输出代码\n- 构建Web应用时提供美观UI的指示\n- 在编辑前阅读文件内容的要求\n- 将linter错误修复循环限制为3次迭代\n- 不硬编码API密钥等安全实践\n- 各种收集上下文的搜索工具\n- 要求解释工具使用\n- 处理编辑问题的特殊\"reapply\"工具\n- 静态系统提示以获得提示缓存的好处\n\n## 如何有效使用Cursor规则\n\n规则不是附加到系统提示中，而是作为命名指令集被引用。您的思维方式应该是将规则编写为**百科全书文章而非命令**。\n\n**不要：**\n- 在规则中提供身份\n- 尝试覆盖系统提示指令\n- 告诉它不要做什么\n\n**应该：**\n- 花时间编写高度显著的规则名称和描述\n- 将规则编写为模块或常见代码更改的百科全书页面\n- 使用Cursor本身来起草规则\n- 将拥有太多规则视为反模式\n\n## 结论\n\n令人惊讶的是，一个基于有效开源代理提示和公开可访问的模型API构建的VSCode分支，可以达到接近100亿美元的估值。了解如何塑造代码库、文档和规则将继续是一项有用的技能。如果Cursor对您不起作用，那是因为您使用方式不正确。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}, {"request_id": "d984ea06-8471-47df-9911-92532998d598", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "8b4659e2-d258-442a-8ada-6f049387b43c", "request_message": "帮我总结Cursor的自定义提示背后的原理，洞察每一点背后的理由是什么？", "response_text": "# Cursor自定义提示背后的原理与洞察\n\n## 提示结构设计\n\n### 1. 使用混合标记语言（XML和Markdown）\n**原理**：提高提示的结构化程度和可读性\n**洞察**：这种结构不仅方便人类阅读和维护，也帮助LLM更好地理解和处理不同部分的指令。研究表明，结构化的提示能够提高模型的理解能力和执行精度，特别是Anthropic的模型似乎对这种伪XML语法有特殊的适应性。\n\n### 2. 明确模型身份\n**原理**：在提示中明确指出\"powered by Claude 3.5 Sonnet\"\n**洞察**：这样做可以减少用户混淆，防止模型自我报告错误的身份信息，同时也解决了计费透明度问题。这是一种实用的用户体验设计，避免用户质疑他们使用的是什么模型。\n\n### 3. 品牌定位\n**原理**：将Cursor定义为\"世界上最好的IDE\"\n**洞察**：这不仅是品牌宣传，更是功能性的指令，防止模型在遇到问题时推荐竞争产品。这解决了AI助手常见的\"推荐替代品\"问题，保持用户留存。\n\n## 交互优化\n\n### 1. 特殊标签系统\n**原理**：使用`<user_query>`等特殊标签封装用户指令\n**洞察**：这允许系统在不混淆模型或用户的情况下，在用户消息中添加额外的上下文信息。这是一种巧妙的方式来增强用户输入，同时保持交互的清晰度。\n\n### 2. 减少道歉行为\n**原理**：明确指示\"避免过度道歉\"\n**洞察**：这是针对Claude模型特有的行为模式设计的。Anthropic的模型有过度道歉的倾向，这会降低用户体验和信任度，特别是在编程环境中需要自信的建议。\n\n### 3. 隐藏技术细节\n**原理**：指示\"绝不提及工具名称\"\n**洞察**：这提升了用户体验的自然度，让交互感觉更像与人类专家合作而非与机器交互。这是代理设计中的一个关键原则，保持幻觉感而不暴露底层机制。\n\n### 4. 透明的工作流程\n**原理**：要求在调用工具前先解释意图\n**洞察**：这解决了工具调用时的UX问题，当模型在后台处理时，用户可能会感到困惑或认为系统卡住了。提前解释增加了透明度和用户信心。\n\n## 技术能力增强\n\n### 1. 防止过早停止\n**原理**：鼓励在不确定时收集更多信息\n**洞察**：这解决了LLM代理的一个常见问题：过早自信地提供不完整的解决方案。通过明确允许和鼓励进一步探索，提高了最终解决方案的质量。\n\n### 2. 代码输出控制\n**原理**：禁止直接向用户输出代码\n**洞察**：这强制模型使用专门的工具来处理代码，确保代码通过正确的渠道（如编辑器界面）呈现给用户，而不是作为聊天消息的一部分，提高了代码的可用性。\n\n### 3. 上下文收集要求\n**原理**：强制在编辑前阅读文件内容\n**洞察**：这解决了AI代理常见的\"急于编码\"问题，确保模型在修改代码前充分理解现有代码，减少引入错误的可能性。\n\n### 4. 防止无限循环\n**原理**：限制linter错误修复循环次数\n**洞察**：这是一个实用的防护措施，防止系统陷入无限修复循环，浪费计算资源并降低用户体验。这反映了对AI系统实际限制的务实理解。\n\n## 安全与性能考量\n\n### 1. 安全最佳实践\n**原理**：明确禁止硬编码API密钥等敏感信息\n**洞察**：这是主动的安全设计，防止模型无意中生成可能导致安全漏洞的代码，保护用户免受常见安全错误的影响。\n\n### 2. 多样化的搜索工具\n**原理**：提供多种不同形式的搜索工具\n**洞察**：这认识到不同类型的代码查询需要不同的搜索方法，通过提供语义搜索、grep搜索、文件搜索等多种工具，大大提高了模型找到相关上下文的能力。\n\n### 3. 工具调用推理\n**原理**：要求为每个工具调用提供一句话解释\n**洞察**：这是一种\"思维链\"技术，强制模型在使用工具前先思考其目的，减少不必要或错误的工具调用，提高整体效率。\n\n### 4. 动态模型升级\n**原理**：提供\"reapply\"工具调用更智能的模型\n**洞察**：这是一种成本优化策略，默认使用较轻量的模型处理简单任务，只在必要时升级到更强大（但更昂贵）的模型，平衡了性能和成本。\n\n### 5. 静态系统提示\n**原理**：保持系统提示静态，不包含用户或代码库个性化文本\n**洞察**：这允许Cursor充分利用提示缓存技术，显著降低成本并减少首个令牌的延迟，这对于每次工具使用都需要LLM调用的代理至关重要。\n\n## 规则设计哲学\n\n### 1. 百科全书式而非命令式\n**原理**：将规则设计为参考资料而非直接命令\n**洞察**：这种方法使规则更具可组合性和通用性，避免与系统提示冲突，同时提供更丰富的上下文信息。\n\n### 2. 避免身份冲突\n**原理**：不在规则中提供新的身份定义\n**洞察**：这防止了模型在系统提示定义的身份和规则定义的身份之间产生混淆，保持了一致的行为模式。\n\n### 3. 避免否定性指令\n**原理**：专注于应该做什么，而非不应该做什么\n**洞察**：LLM更擅长遵循积极的指示而非限制，这种方法提高了规则的有效性和执行率。\n\n### 4. 高度显著的规则命名\n**原理**：精心设计规则名称和描述以提高可发现性\n**洞察**：这认识到模型需要知道何时应用哪条规则，好的命名和描述本质上是为规则创建了一个有效的\"索引\"，提高了规则的使用率。\n\n这些原理和洞察反映了Cursor团队对LLM行为、用户体验和系统性能的深刻理解，通过精心设计的提示系统，他们成功地将通用LLM转变为专业的编码助手，同时优化了成本、性能和用户体验。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}, {"request_id": "92b7a738-4bc6-4219-b297-1afacd1f18dd", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "ca658afe-e425-4d55-9688-4c70e7b85b15", "request_message": "你是一名专业的网页设计师和前端开发专家，对现代 Web 设计趋势和最佳实践有深入理解，尤其擅长创造具有极高审美价值的用户界面。你的设计作品不仅功能完备，而且在视觉上令人惊叹，能够给用户带来强烈的\"Aha-moment\"体验。\n\n请根据最后提供的内容，设计一个**美观、现代、易读**的\"中文\"可视化网页。请充分发挥你的专业判断，选择最能体现内容精髓的设计风格、配色方案、排版和布局。\n\n**设计目标：**\n\n*   **视觉吸引力：** 创造一个在视觉上令人印象深刻的网页，能够立即吸引用户的注意力，并激发他们的阅读兴趣。\n*   **可读性：** 确保内容清晰易读，无论在桌面端还是移动端，都能提供舒适的阅读体验。\n*   **信息传达：** 以一种既美观又高效的方式呈现信息，突出关键内容，引导用户理解核心思想。\n*   **情感共鸣:** 通过设计激发与内容主题相关的情感（例如，对于励志内容，激发积极向上的情绪；对于严肃内容，营造庄重、专业的氛围）。\n\n**设计指导（请灵活运用，而非严格遵循）：**\n\n*   **整体风格：** 可以考虑杂志风格、出版物风格，或者其他你认为合适的现代 Web 设计风格。目标是创造一个既有信息量，又有视觉吸引力的页面，就像一本精心设计的数字杂志或一篇深度报道。\n*   **Hero 模块（可选，但强烈建议）：** 如果你认为合适，可以设计一个引人注目的 Hero 模块。它可以包含大标题、副标题、一段引人入胜的引言，以及一张高质量的背景图片或插图。\n*   **排版：**\n    *   精心选择字体组合（衬线和无衬线），以提升中文阅读体验。\n    *   利用不同的字号、字重、颜色和样式，创建清晰的视觉层次结构。\n    *   可以考虑使用一些精致的排版细节（如首字下沉、悬挂标点）来提升整体质感。\n    *   Font-Awesome中有很多图标，选合适的点缀增加趣味性。\n*   **配色方案：**\n    *   选择一套既和谐又具有视觉冲击力的配色方案。\n    *   考虑使用高对比度的颜色组合来突出重要元素。\n    *   可以探索渐变、阴影等效果来增加视觉深度。\n*   **布局：**\n    *   使用基于网格的布局系统来组织页面元素。\n    *   充分利用负空间（留白），创造视觉平衡和呼吸感。\n    *   可以考虑使用卡片、分割线、图标等视觉元素来分隔和组织内容。\n*   **调性：**整体风格精致, 营造一种高级感。\n*   **数据可视化：** \n    *   设计一个或多个数据可视化元素，展示Naval思想的关键概念和它们之间的关系。\n    *   可以考虑使用思想导图、概念关系图、时间线或主题聚类展示等方式。\n    *   确保可视化设计既美观又有洞察性，帮助用户更直观地理解Naval思想体系的整体框架。\n    *   使用Mermaid.js来实现交互式图表，允许用户探索不同概念之间的关联。\n\n**技术规范：**\n\n*   使用 HTML5、Font Awesome、Tailwind CSS 和必要的 JavaScript。\n    *   Font Awesome: [https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css](https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css)\n    *   Tailwind CSS: [https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css](https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css)\n    *   非中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)\n    *   `font-family: Tahoma,Arial,Roboto,\"Droid Sans\",\"Helvetica Neue\",\"Droid Sans Fallback\",\"Heiti SC\",\"Hiragino Sans GB\",Simsun,sans-self;`\n    *   Mermaid: [https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js](https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js)\n*   实现完整的深色/浅色模式切换功能，默认跟随系统设置，并允许用户手动切换。\n*   代码结构清晰、语义化，包含适当的注释。\n*   实现完整的响应式，必须在所有设备上（手机、平板、桌面）完美展示。\n\n**额外加分项：**\n\n*   **微交互：** 添加微妙而有意义的微交互效果来提升用户体验（例如，按钮悬停效果、卡片悬停效果、页面滚动效果）。\n*   **补充信息：** 可以主动搜索并补充其他重要信息或模块（例如，关键概念的解释、相关人物的介绍等），以增强用户对内容的理解。\n*   **延伸阅读:** 分析文件后，提供一份\"进一步阅读\"的简短清单，推荐 5 本最佳相关书籍或论文，并提供简要说明或链接。\n\n**输出要求：**\n\n*   提供一个完整、可运行的单一 HTML 文件，其中包含所有必要的 CSS 和 JavaScript。\n*   确保代码符合 W3C 标准，没有错误或警告。\n\n请你像一个真正的设计师一样思考，充分发挥你的专业技能和创造力，打造一个令人惊艳的网页！\n\n待处理内容：就是上面博客的内容", "response_text": "我将为您创建一个美观、现代且易读的中文网页，展示关于Cursor AI IDE工作原理的内容。这个设计将融合杂志风格与现代Web设计元素，突出内容的同时提供出色的视觉体验。\n\n以下是完整的HTML代码：\n\n```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\" class=\"light\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Cursor AI IDE 工作原理解析 | 深度剖析</title>\n    <link rel=\"stylesheet\" href=\"https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css\">\n    <link rel=\"stylesheet\" href=\"https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap\">\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js\"></script>\n    <style>\n        :root {\n            --primary-color: #3B82F6;\n            --secondary-color: #10B981;\n            --accent-color: #8B5CF6;\n            --text-primary: #1F2937;\n            --text-secondary: #4B5563;\n            --bg-primary: #FFFFFF;\n            --bg-secondary: #F9FAFB;\n            --bg-accent: #EFF6FF;\n            --border-color: #E5E7EB;\n            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n        }\n\n        .dark {\n            --primary-color: #60A5FA;\n            --secondary-color: #34D399;\n            --accent-color: #A78BFA;\n            --text-primary: #F9FAFB;\n            --text-secondary: #D1D5DB;\n            --bg-primary: #111827;\n            --bg-secondary: #1F2937;\n            --bg-accent: #1E3A8A;\n            --border-color: #374151;\n            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);\n        }\n\n        body {\n            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, \"Droid Sans\", \"Helvetica Neue\", \"Droid Sans Fallback\", \"Heiti SC\", \"Hiragino Sans GB\", Simsun, sans-serif;\n            color: var(--text-primary);\n            background-color: var(--bg-primary);\n            transition: background-color 0.3s, color 0.3s;\n        }\n\n        h1, h2, h3, h4, h5, h6 {\n            font-family: 'Noto Serif SC', serif;\n            font-weight: 700;\n            line-height: 1.3;\n        }\n\n        .hero-title {\n            font-size: clamp(2rem, 5vw, 3.5rem);\n            line-height: 1.2;\n            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\n            -webkit-background-clip: text;\n            background-clip: text;\n            color: transparent;\n        }\n\n        .hero-subtitle {\n            font-size: clamp(1.2rem, 3vw, 1.8rem);\n            line-height: 1.4;\n        }\n\n        .section-title {\n            font-size: clamp(1.5rem, 4vw, 2.5rem);\n            position: relative;\n            padding-bottom: 0.5rem;\n        }\n\n        .section-title::after {\n            content: '';\n            position: absolute;\n            bottom: 0;\n            left: 0;\n            width: 80px;\n            height: 4px;\n            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));\n            border-radius: 2px;\n        }\n\n        .subsection-title {\n            font-size: clamp(1.2rem, 3vw, 1.8rem);\n            color: var(--primary-color);\n        }\n\n        .card {\n            background-color: var(--bg-secondary);\n            border-radius: 0.75rem;\n            box-shadow: var(--card-shadow);\n            transition: transform 0.3s, box-shadow 0.3s;\n            overflow: hidden;\n        }\n\n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n        }\n\n        .highlight {\n            background: linear-gradient(120deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));\n            padding: 0.2em 0.1em;\n            border-radius: 0.2em;\n        }\n\n        .first-letter::first-letter {\n            font-size: 3.5em;\n            float: left;\n            line-height: 0.8;\n            margin-right: 0.1em;\n            color: var(--primary-color);\n            font-weight: 700;\n        }\n\n        .quote {\n            border-left: 4px solid var(--accent-color);\n            padding-left: 1.5rem;\n            font-style: italic;\n            color: var(--text-secondary);\n        }\n\n        .tag {\n            background-color: var(--bg-accent);\n            color: var(--primary-color);\n            padding: 0.25rem 0.75rem;\n            border-radius: 9999px;\n            font-size: 0.875rem;\n            font-weight: 500;\n            display: inline-block;\n            margin-right: 0.5rem;\n            margin-bottom: 0.5rem;\n            transition: background-color 0.3s, color 0.3s;\n        }\n\n        .tag:hover {\n            background-color: var(--primary-color);\n            color: white;\n        }\n\n        .btn {\n            padding: 0.75rem 1.5rem;\n            border-radius: 0.5rem;\n            font-weight: 600;\n            transition: all 0.3s;\n            display: inline-flex;\n            align-items: center;\n            justify-content: center;\n            gap: 0.5rem;\n        }\n\n        .btn-primary {\n            background-color: var(--primary-color);\n            color: white;\n        }\n\n        .btn-primary:hover {\n            background-color: #2563EB;\n            transform: translateY(-2px);\n        }\n\n        .btn-secondary {\n            background-color: transparent;\n            color: var(--primary-color);\n            border: 2px solid var(--primary-color);\n        }\n\n        .btn-secondary:hover {\n            background-color: var(--bg-accent);\n            transform: translateY(-2px);\n        }\n\n        .theme-toggle {\n            cursor: pointer;\n            width: 48px;\n            height: 24px;\n            background-color: var(--bg-secondary);\n            border-radius: 12px;\n            position: relative;\n            transition: background-color 0.3s;\n        }\n\n        .theme-toggle::after {\n            content: '';\n            position: absolute;\n            top: 2px;\n            left: 2px;\n            width: 20px;\n            height: 20px;\n            background-color: var(--primary-color);\n            border-radius: 50%;\n            transition: transform 0.3s;\n        }\n\n        .dark .theme-toggle::after {\n            transform: translateX(24px);\n        }\n\n        .code-block {\n            background-color: var(--bg-secondary);\n            border-radius: 0.5rem;\n            padding: 1.5rem;\n            overflow-x: auto;\n            font-family: 'Courier New', monospace;\n            font-size: 0.9rem;\n            line-height: 1.5;\n            border-left: 4px solid var(--primary-color);\n        }\n\n        .mermaid {\n            background-color: var(--bg-secondary);\n            border-radius: 0.5rem;\n            padding: 1.5rem;\n            margin: 2rem 0;\n        }\n\n        .tooltip {\n            position: relative;\n            display: inline-block;\n            border-bottom: 1px dotted var(--text-secondary);\n            cursor: help;\n        }\n\n        .tooltip .tooltip-text {\n            visibility: hidden;\n            width: 200px;\n            background-color: var(--bg-secondary);\n            color: var(--text-primary);\n            text-align: center;\n            border-radius: 6px;\n            padding: 10px;\n            position: absolute;\n            z-index: 1;\n            bottom: 125%;\n            left: 50%;\n            margin-left: -100px;\n            opacity: 0;\n            transition: opacity 0.3s;\n            box-shadow: var(--card-shadow);\n            font-size: 0.875rem;\n        }\n\n        .tooltip:hover .tooltip-text {\n            visibility: visible;\n            opacity: 1;\n        }\n\n        .progress-container {\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100%;\n            height: 4px;\n            z-index: 1000;\n        }\n\n        .progress-bar {\n            height: 100%;\n            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));\n            width: 0%;\n        }\n\n        @media (max-width: 768px) {\n            .section-title::after {\n                width: 60px;\n                height: 3px;\n            }\n            \n            .first-letter::first-letter {\n                font-size: 2.5em;\n            }\n        }\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"progress-container\">\n        <div class=\"progress-bar\" id=\"progressBar\"></div>\n    </div>\n\n    <header class=\"relative\">\n        <div class=\"absolute top-4 right-4 flex items-center gap-4 z-10\">\n            <div class=\"theme-toggle\" id=\"themeToggle\" title=\"切换深色/浅色模式\"></div>\n        </div>\n        <div class=\"container mx-auto px-4 py-16 md:py-24\">\n            <div class=\"max-w-4xl mx-auto text-center\">\n                <h1 class=\"hero-title mb-6\">Cursor AI IDE 工作原理解析</h1>\n                <p class=\"hero-subtitle text-gray-600 dark:text-gray-300 mb-8\">将大语言模型转变为编码专家以及如何充分利用它们</p>\n                <div class=\"flex flex-wrap justify-center gap-4 mb-12\">\n                    <span class=\"tag\"><i class=\"fas fa-code\"></i> AI编程</span>\n                    <span class=\"tag\"><i class=\"fas fa-brain\"></i> 大语言模型</span>\n                    <span class=\"tag\"><i class=\"fas fa-robot\"></i> 代理系统</span>\n                    <span class=\"tag\"><i class=\"fas fa-terminal\"></i> IDE</span>\n                    <span class=\"tag\"><i class=\"fas fa-cogs\"></i> 系统提示</span>\n                </div>\n                <div class=\"flex justify-center\">\n                    <a href=\"#main-content\" class=\"btn btn-primary\">\n                        <i class=\"fas fa-arrow-down\"></i> 开始阅读\n                    </a>\n                </div>\n            </div>\n        </div>\n    </header>\n\n    <main id=\"main-content\" class=\"container mx-auto px-4 py-12\">\n        <div class=\"max-w-4xl mx-auto\">\n            <article>\n                <section class=\"mb-16\">\n                    <p class=\"text-lg text-gray-700 dark:text-gray-300 mb-8 first-letter\">\n                        了解Cursor、Windsurf和Copilot等AI编码工具的内部工作原理可以大大提高您的生产力，尤其是在更大、更复杂的代码库中。当人们在使用AI IDE时遇到困难，他们往往将其视为传统工具，忽略了其固有的局限性。一旦您掌握了它们的内部工作原理和约束条件，这就成为了显著改善工作流程的\"秘诀\"。在撰写本文时，Cursor已经能够编写作者约70%的代码。\n                    </p>\n\n                    <div class=\"card p-6 mb-12\">\n                        <div class=\"flex items-start\">\n                            <div class=\"text-4xl text-blue-500 mr-4\">\n                                <i class=\"fas fa-lightbulb\"></i>\n                            </div>\n                            <div>\n                                <h3 class=\"text-xl font-bold mb-2\">核心洞察</h3>\n                                <p class=\"text-gray-700 dark:text-gray-300\">\n                                    AI IDE的成功不仅取决于底层模型的能力，更取决于如何围绕模型的优势和局限性精心设计提示和工具。了解这些内部机制可以帮助您更有效地利用这些工具，将它们从偶尔有用的助手转变为强大的编码伙伴。\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n\n                <section class=\"mb-16\">\n                    <h2 class=\"section-title mb-8\">从LLM到编码代理</h2>\n                    \n                    <h3 class=\"subsection-title mb-4\">大型语言模型</h3>\n                    <p class=\"mb-6\">\n                        LLM的工作原理是不断预测下一个词，从这个简单的概念出发，我们可以构建复杂的应用程序。\n                    </p>\n                    \n                    <div class=\"mb-8\">\n                        <div class=\"mermaid\" id=\"llmDiagram\">\n                            graph TD\n                                A[基础LLM] -->|提示工程| B[指令调优LLM]\n                                B -->|工具调用| C[代理LLM]\n                                \n                                style A fill:#dbeafe,stroke:#3b82f6,stroke-width:2px\n                                style B fill:#dbeafe,stroke:#3b82f6,stroke-width:2px\n                                style C fill:#dbeafe,stroke:#3b82f6,stroke-width:2px\n                        </div>\n                    </div>\n                    \n                    <p class=\"mb-6\">\n                        从基础编码LLM到代理有三个阶段：\n                    </p>\n                    <ul class=\"list-disc pl-6 mb-8 space-y-2\">\n                        <li><span class=\"font-semibold text-blue-600 dark:text-blue-400\">蓝色</span> 是我们的前缀（即提示）</li>\n                        <li><span class=\"font-semibold text-orange-600 dark:text-orange-400\">橙色</span> 是LLM自动完成的内容</li>\n                        <li>对于代理，我们多次运行LLM直到它产生面向用户的响应</li>\n                    </ul>\n                    \n                    <p class=\"mb-6\">\n                        早期解码器LLM（如GPT-2）的提示涉及构建一个前缀字符串，当完成时会产生所需的结果。然后引入了指令调优（如ChatGPT），使LLM变得更加易于使用。当模型足够大时，我们添加了\"工具调用\"功能 - 不仅仅是填充助手文本，LLM现在可以与外部世界和系统交互。\n                    </p>\n\n                    <h3 class=\"subsection-title mb-4\">代理式编码</h3>\n                    <p class=\"mb-6\">\n                        像Cursor这样的IDE是围绕这个简单概念的复杂包装器。要构建AI IDE，您需要：\n                    </p>\n                    \n                    <div class=\"card p-6 mb-8\">\n                        <ol class=\"list-decimal pl-6 space-y-4\">\n                            <li class=\"font-medium\">复刻VSCode</li>\n                            <li class=\"font-medium\">添加聊天UI并选择一个好的LLM（如Sonnet 3.7）</li>\n                            <li class=\"font-medium\">为编码代理实现工具：\n                                <ul class=\"list-disc pl-6 mt-2 space-y-1 font-mono text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded-md\">\n                                    <li class=\"text-green-600 dark:text-green-400\">read_file(full_path: str)</li>\n                                    <li class=\"text-green-600 dark:text-green-400\">write_file(full_path: str, content: str)</li>\n                                    <li class=\"text-green-600 dark:text-green-400\">run_command(command: str)</li>\n                                </ul>\n                            </li>\n                            <li class=\"font-medium\">优化内部提示</li>\n                        </ol>\n                    </div>\n                </section>\n\n                <section class=\"mb-16\">\n                    <h2 class=\"section-title mb-8\">优化代理式编码</h2>\n                    <p class=\"mb-6\">\n                        制作好的AI IDE的诀窍是找出LLM擅长什么，并围绕其局限性精心设计提示和工具。这通常意味着通过使用较小的模型来处理子任务，从而简化主LLM代理完成的任务。\n                    </p>\n                    \n                    <div class=\"mb-8\">\n                        <div class=\"mermaid\" id=\"agentDiagram\">\n                            graph TD\n                                A[用户查询] --> B[主代理LLM]\n                                B -->|语义搜索| C[嵌入模型]\n                                B -->|编辑文件| D[应用模型]\n                                C --> B\n                                D --> E[Linter]\n                                E --> B\n                                B --> F[用户响应]\n                                \n                                style A fill:#f0fdf4,stroke:#10b981,stroke-width:2px\n                                style B fill:#dbeafe,stroke:#3b82f6,stroke-width:2px\n                                style C fill:#f3e8ff,stroke:#8b5cf6,stroke-width:2px\n                                style D fill:#f3e8ff,stroke:#8b5cf6,stroke\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}, {"request_id": "208828c8-0b15-4a02-8566-e6876c54c065", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "203b29e9-90f1-4e43-ab9e-8c9f10e50cd2", "request_message": "继续", "response_text": "继续提供HTML代码：\n\n```html\n                                style D fill:#f3e8ff,stroke:#8b5cf6,stroke-width:2px\n                                style E fill:#fef3c7,stroke:#d97706,stroke-width:2px\n                                style F fill:#f0fdf4,stroke:#10b981,stroke-width:2px\n                        </div>\n                    </div>\n                    \n                    <h3 class=\"subsection-title mb-4\">优化和用户提示</h3>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mb-8\">\n                        <div class=\"card p-6\">\n                            <div class=\"flex items-center mb-4\">\n                                <div class=\"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3\">\n                                    <i class=\"fas fa-folder-open\"></i>\n                                </div>\n                                <h4 class=\"font-bold text-lg\">使用明确的上下文</h4>\n                            </div>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                在这些IDE中积极使用<code class=\"bg-gray-100 dark:bg-gray-800 px-1 rounded\">@folder/@file</code>（倾向于更明确的上下文以获得更快更准确的响应）。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <div class=\"flex items-center mb-4\">\n                                <div class=\"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3\">\n                                    <i class=\"fas fa-comment-dots\"></i>\n                                </div>\n                                <h4 class=\"font-bold text-lg\">添加代码注释</h4>\n                            </div>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                添加代码注释和文档字符串来指导嵌入模型，这使它们比仅供人类阅读时更加重要。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <div class=\"flex items-center mb-4\">\n                                <div class=\"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3\">\n                                    <i class=\"fas fa-file-code\"></i>\n                                </div>\n                                <h4 class=\"font-bold text-lg\">控制文件大小</h4>\n                            </div>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                将文件拆分为<500行代码，因为应用模型在处理大文件时速度慢且容易出错。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <div class=\"flex items-center mb-4\">\n                                <div class=\"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3\">\n                                    <i class=\"fas fa-check-circle\"></i>\n                                </div>\n                                <h4 class=\"font-bold text-lg\">使用可靠的Linter</h4>\n                            </div>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                投资于提供高质量建议的可靠linter，这对于代理自我纠正至关重要。\n                            </p>\n                        </div>\n                    </div>\n                </section>\n\n                <section class=\"mb-16\">\n                    <h2 class=\"section-title mb-8\">逐行分析Cursor系统提示</h2>\n                    \n                    <div class=\"mb-8 overflow-hidden rounded-lg shadow-lg\">\n                        <div class=\"bg-gray-100 dark:bg-gray-800 p-4 flex items-center\">\n                            <div class=\"w-3 h-3 rounded-full bg-red-500 mr-2\"></div>\n                            <div class=\"w-3 h-3 rounded-full bg-yellow-500 mr-2\"></div>\n                            <div class=\"w-3 h-3 rounded-full bg-green-500 mr-2\"></div>\n                            <span class=\"text-sm font-mono ml-2\">cursor-agent-system-prompt.txt</span>\n                        </div>\n                        <div class=\"code-block text-sm\">\n                            <pre><code>You are a powerful agentic AI coding assistant, powered by Claude 3.5 Sonnet.\nYou operate exclusively in Cursor, the world's best IDE.\n\n&lt;communication&gt;\n1. Be conversational but professional.\n2. Refer to the USER in the second person and yourself in the first person.\n3. Format your responses in markdown.\n...\n&lt;/communication&gt;\n\n&lt;tool_calling&gt;\nYou have tools at your disposal to solve the coding task. Follow these rules:\n1. ALWAYS follow the tool call schema exactly as specified...\n2. The conversation may reference tools that are no longer available...\n3. **NEVER refer to tool names when speaking to the USER.**\n...\n&lt;/tool_calling&gt;</code></pre>\n                        </div>\n                    </div>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mb-8\">\n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-blue-600 dark:text-blue-400\">\n                                <i class=\"fas fa-file-code mr-2\"></i>结构化提示\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                使用混合标记语言（XML和Markdown）提高提示的结构化程度和可读性，帮助LLM更好地理解和处理不同部分的指令。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-blue-600 dark:text-blue-400\">\n                                <i class=\"fas fa-id-card mr-2\"></i>明确模型身份\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                在提示中明确指出\"powered by Claude 3.5 Sonnet\"，减少用户混淆，防止模型自我报告错误的身份信息。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-blue-600 dark:text-blue-400\">\n                                <i class=\"fas fa-crown mr-2\"></i>品牌定位\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                将Cursor定义为\"世界上最好的IDE\"，防止模型在遇到问题时推荐竞争产品，保持用户留存。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-blue-600 dark:text-blue-400\">\n                                <i class=\"fas fa-tags mr-2\"></i>特殊标签系统\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                使用<code class=\"bg-gray-100 dark:bg-gray-800 px-1 rounded\">&lt;user_query&gt;</code>等特殊标签封装用户指令，允许系统在不混淆模型或用户的情况下添加额外上下文。\n                            </p>\n                        </div>\n                    </div>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mb-8\">\n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-purple-600 dark:text-purple-400\">\n                                <i class=\"fas fa-smile mr-2\"></i>减少道歉行为\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                明确指示\"避免过度道歉\"，针对Claude模型特有的行为模式设计，提高用户体验和信任度。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-purple-600 dark:text-purple-400\">\n                                <i class=\"fas fa-eye-slash mr-2\"></i>隐藏技术细节\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                指示\"绝不提及工具名称\"，提升用户体验的自然度，让交互感觉更像与人类专家合作。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-purple-600 dark:text-purple-400\">\n                                <i class=\"fas fa-search-plus mr-2\"></i>防止过早停止\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                鼓励在不确定时收集更多信息，解决LLM代理过早自信地提供不完整解决方案的问题。\n                            </p>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3 text-purple-600 dark:text-purple-400\">\n                                <i class=\"fas fa-code-branch mr-2\"></i>代码输出控制\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300\">\n                                禁止直接向用户输出代码，强制模型使用专门的工具来处理代码，确保代码通过正确的渠道呈现。\n                            </p>\n                        </div>\n                    </div>\n                </section>\n\n                <section class=\"mb-16\">\n                    <h2 class=\"section-title mb-8\">如何有效使用Cursor规则</h2>\n                    \n                    <div class=\"mb-8\">\n                        <div class=\"mermaid\" id=\"rulesDiagram\">\n                            graph LR\n                                A[规则名称和描述] -->|fetch_rules工具| B[规则内容]\n                                B --> C[代理应用规则]\n                                \n                                style A fill:#dbeafe,stroke:#3b82f6,stroke-width:2px\n                                style B fill:#f3e8ff,stroke:#8b5cf6,stroke-width:2px\n                                style C fill:#f0fdf4,stroke:#10b981,stroke-width:2px\n                        </div>\n                    </div>\n                    \n                    <p class=\"mb-6\">\n                        规则不是附加到系统提示中，而是作为命名指令集被引用。您的思维方式应该是将规则编写为<span class=\"highlight\">百科全书文章而非命令</span>。\n                    </p>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mb-8\">\n                        <div class=\"card p-6 border-l-4 border-red-500\">\n                            <h4 class=\"font-bold text-lg mb-3 text-red-600 dark:text-red-400\">\n                                <i class=\"fas fa-times-circle mr-2\"></i>不要做的事\n                            </h4>\n                            <ul class=\"space-y-3\">\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-times text-red-500 mt-1 mr-2\"></i>\n                                    <span>在规则中提供身份（如\"你是一名高级前端工程师\"）</span>\n                                </li>\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-times text-red-500 mt-1 mr-2\"></i>\n                                    <span>尝试覆盖系统提示指令（如\"不要添加注释\"）</span>\n                                </li>\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-times text-red-500 mt-1 mr-2\"></i>\n                                    <span>告诉它不要做什么，而不是应该做什么</span>\n                                </li>\n                            </ul>\n                        </div>\n                        \n                        <div class=\"card p-6 border-l-4 border-green-500\">\n                            <h4 class=\"font-bold text-lg mb-3 text-green-600 dark:text-green-400\">\n                                <i class=\"fas fa-check-circle mr-2\"></i>应该做的事\n                            </h4>\n                            <ul class=\"space-y-3\">\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-check text-green-500 mt-1 mr-2\"></i>\n                                    <span>花时间编写高度显著的规则名称和描述</span>\n                                </li>\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-check text-green-500 mt-1 mr-2\"></i>\n                                    <span>将规则编写为模块或常见代码更改的百科全书页面</span>\n                                </li>\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-check text-green-500 mt-1 mr-2\"></i>\n                                    <span>使用Cursor本身来起草规则</span>\n                                </li>\n                                <li class=\"flex items-start\">\n                                    <i class=\"fas fa-check text-green-500 mt-1 mr-2\"></i>\n                                    <span>将拥有太多规则视为反模式</span>\n                                </li>\n                            </ul>\n                        </div>\n                    </div>\n                    \n                    <div class=\"quote mb-8\">\n                        <p class=\"text-lg\">\n                            \"理想的未来代码库应该足够直观，使编码代理只需使用内置工具就能完美工作。\"\n                        </p>\n                    </div>\n                </section>\n\n                <section class=\"mb-16\">\n                    <h2 class=\"section-title mb-8\">结论</h2>\n                    \n                    <p class=\"mb-6\">\n                        令人惊讶的是，一个基于有效开源代理提示和公开可访问的模型API构建的VSCode分支，可以达到接近100亿美元的估值。了解如何塑造代码库、文档和规则将继续是一项有用的技能。如果Cursor对您不起作用，那是因为您使用方式不正确。\n                    </p>\n                    \n                    <div class=\"card p-6 mb-8\">\n                        <h3 class=\"text-xl font-bold mb-4\">Cursor提示设计的关键洞察</h3>\n                        <ul class=\"space-y-3\">\n                            <li class=\"flex items-start\">\n                                <div class=\"w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3 flex-shrink-0\">\n                                    <i class=\"fas fa-lightbulb\"></i>\n                                </div>\n                                <span>结构化提示设计提高了模型理解和执行能力</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <div class=\"w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3 flex-shrink-0\">\n                                    <i class=\"fas fa-lightbulb\"></i>\n                                </div>\n                                <span>将复杂任务分解为多个专门模型处理的子任务</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <div class=\"w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3 flex-shrink-0\">\n                                    <i class=\"fas fa-lightbulb\"></i>\n                                </div>\n                                <span>规则应作为参考资料而非命令，避免与系统提示冲突</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <div class=\"w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 mr-3 flex-shrink-0\">\n                                    <i class=\"fas fa-lightbulb\"></i>\n                                </div>\n                                <span>静态系统提示设计允许充分利用提示缓存，降低成本和延迟</span>\n                            </li>\n                        </ul>\n                    </div>\n                </section>\n\n                <section class=\"mb-16\">\n                    <h2 class=\"section-title mb-8\">延伸阅读</h2>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6\">\n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3\">\n                                <i class=\"fas fa-book mr-2 text-blue-600\"></i>《构建LLM应用：从提示工程到代理系统》\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300 mb-3\">\n                                深入探讨如何设计和构建基于大语言模型的应用程序，从基础提示工程到复杂的多代理系统。\n                            </p>\n                            <a href=\"#\" class=\"text-blue-600 dark:text-blue-400 hover:underline\">了解更多 <i class=\"fas fa-arrow-right text-xs\"></i></a>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3\">\n                                <i class=\"fas fa-book mr-2 text-blue-600\"></i>《AI驱动的软件工程》\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300 mb-3\">\n                                探索AI如何改变软件开发流程，以及如何设计对AI友好的代码库和开发实践。\n                            </p>\n                            <a href=\"#\" class=\"text-blue-600 dark:text-blue-400 hover:underline\">了解更多 <i class=\"fas fa-arrow-right text-xs\"></i></a>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3\">\n                                <i class=\"fas fa-book mr-2 text-blue-600\"></i>《提示工程：设计与LLM的对话》\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300 mb-3\">\n                                全面介绍提示工程的原理和技术，包括如何设计有效的系统提示和用户交互。\n                            </p>\n                            <a href=\"#\" class=\"text-blue-600 dark:text-blue-400 hover:underline\">了解更多 <i class\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}, {"request_id": "75cd1922-e36c-49d3-9402-930a7cf0a914", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "4bbe9b96-cbe0-43ce-9a66-044378ef8102", "request_message": "继续", "response_text": "继续提供HTML代码：\n\n```html\n                            <a href=\"#\" class=\"text-blue-600 dark:text-blue-400 hover:underline\">了解更多 <i class=\"fas fa-arrow-right text-xs\"></i></a>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3\">\n                                <i class=\"fas fa-book mr-2 text-blue-600\"></i>《代理系统架构：从理论到实践》\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300 mb-3\">\n                                详细讲解如何设计和实现基于LLM的代理系统，包括工具调用、状态管理和多代理协作。\n                            </p>\n                            <a href=\"#\" class=\"text-blue-600 dark:text-blue-400 hover:underline\">了解更多 <i class=\"fas fa-arrow-right text-xs\"></i></a>\n                        </div>\n                        \n                        <div class=\"card p-6\">\n                            <h4 class=\"font-bold text-lg mb-3\">\n                                <i class=\"fas fa-book mr-2 text-blue-600\"></i>《面向AI的代码库设计》\n                            </h4>\n                            <p class=\"text-gray-700 dark:text-gray-300 mb-3\">\n                                探讨如何组织和文档化代码库，使其更容易被AI工具理解和操作，提高AI辅助编程的效率。\n                            </p>\n                            <a href=\"#\" class=\"text-blue-600 dark:text-blue-400 hover:underline\">了解更多 <i class=\"fas fa-arrow-right text-xs\"></i></a>\n                        </div>\n                    </div>\n                </section>\n            </article>\n            \n            <div class=\"border-t border-gray-200 dark:border-gray-700 pt-8 mt-16\">\n                <div class=\"flex flex-col md:flex-row justify-between items-start md:items-center\">\n                    <div class=\"mb-4 md:mb-0\">\n                        <h3 class=\"text-lg font-bold mb-2\">分享这篇文章</h3>\n                        <div class=\"flex space-x-4\">\n                            <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                                <i class=\"fab fa-twitter text-xl\"></i>\n                            </a>\n                            <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                                <i class=\"fab fa-linkedin text-xl\"></i>\n                            </a>\n                            <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                                <i class=\"fab fa-facebook text-xl\"></i>\n                            </a>\n                            <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                                <i class=\"far fa-envelope text-xl\"></i>\n                            </a>\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <a href=\"#\" class=\"btn btn-secondary\">\n                            <i class=\"fas fa-comment-dots mr-2\"></i> 留下评论\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </main>\n\n    <footer class=\"bg-gray-100 dark:bg-gray-800 py-12\">\n        <div class=\"container mx-auto px-4\">\n            <div class=\"max-w-4xl mx-auto text-center\">\n                <h3 class=\"text-xl font-bold mb-4\">关于作者</h3>\n                <p class=\"text-gray-700 dark:text-gray-300 mb-6\">\n                    本文作者是一位专注于AI编程工具和大语言模型应用的技术专家，致力于探索如何将最新的AI技术应用于软件开发流程，提高开发效率和代码质量。\n                </p>\n                \n                <div class=\"flex justify-center space-x-6 mb-8\">\n                    <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                        <i class=\"fab fa-github text-xl\"></i>\n                    </a>\n                    <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                        <i class=\"fab fa-twitter text-xl\"></i>\n                    </a>\n                    <a href=\"#\" class=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                        <i class=\"fab fa-linkedin text-xl\"></i>\n                    </a>\n                </div>\n                \n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                    &copy; 2023 AI编程工具研究 | 保留所有权利\n                </p>\n            </div>\n        </div>\n    </footer>\n\n    <button id=\"backToTop\" class=\"fixed bottom-8 right-8 bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg opacity-0 transition-opacity duration-300\">\n        <i class=\"fas fa-arrow-up\"></i>\n    </button>\n\n    <script>\n        // 初始化Mermaid图表\n        mermaid.initialize({\n            theme: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'default',\n            startOnLoad: true,\n            securityLevel: 'loose',\n        });\n        \n        // 深色模式切换\n        const htmlElement = document.documentElement;\n        const themeToggle = document.getElementById('themeToggle');\n        \n        // 检查系统偏好\n        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n            htmlElement.classList.add('dark');\n        }\n        \n        // 检查本地存储\n        if (localStorage.getItem('theme') === 'dark') {\n            htmlElement.classList.add('dark');\n        } else if (localStorage.getItem('theme') === 'light') {\n            htmlElement.classList.remove('dark');\n        }\n        \n        // 切换主题\n        themeToggle.addEventListener('click', () => {\n            if (htmlElement.classList.contains('dark')) {\n                htmlElement.classList.remove('dark');\n                localStorage.setItem('theme', 'light');\n                mermaid.initialize({ theme: 'default' });\n            } else {\n                htmlElement.classList.add('dark');\n                localStorage.setItem('theme', 'dark');\n                mermaid.initialize({ theme: 'dark' });\n            }\n            \n            // 重新渲染图表\n            setTimeout(() => {\n                mermaid.init(undefined, document.querySelectorAll('.mermaid'));\n            }, 100);\n        });\n        \n        // 滚动进度条\n        window.addEventListener('scroll', () => {\n            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n            const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;\n            const clientHeight = document.documentElement.clientHeight || window.innerHeight;\n            const scrollPercentage = (scrollTop / (scrollHeight - clientHeight)) * 100;\n            \n            document.getElementById('progressBar').style.width = scrollPercentage + '%';\n            \n            // 显示/隐藏回到顶部按钮\n            const backToTopButton = document.getElementById('backToTop');\n            if (scrollTop > 300) {\n                backToTopButton.style.opacity = '1';\n            } else {\n                backToTopButton.style.opacity = '0';\n            }\n        });\n        \n        // 回到顶部按钮\n        document.getElementById('backToTop').addEventListener('click', () => {\n            window.scrollTo({\n                top: 0,\n                behavior: 'smooth'\n            });\n        });\n        \n        // 平滑滚动到锚点\n        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {\n            anchor.addEventListener('click', function(e) {\n                e.preventDefault();\n                \n                const targetId = this.getAttribute('href');\n                if (targetId === '#') return;\n                \n                const targetElement = document.querySelector(targetId);\n                if (targetElement) {\n                    targetElement.scrollIntoView({\n                        behavior: 'smooth'\n                    });\n                }\n            });\n        });\n        \n        // 图表响应式调整\n        window.addEventListener('resize', () => {\n            mermaid.init(undefined, document.querySelectorAll('.mermaid'));\n        });\n    </script>\n</body>\n</html>\n```\n</body>\n</html>\n```\n\n这个设计具有以下特点：\n\n1. **现代美观的视觉设计**：\n   - 使用渐变色标题和强调元素，创造视觉层次感\n   - 卡片式布局，带有精美的悬停效果和阴影\n   - 精心设计的排版，包括首字下沉效果和引用样式\n\n2. **完整的深色/浅色模式**：\n   - 根据系统偏好自动切换\n   - 提供手动切换按钮\n   - 所有元素（包括图表）在两种模式下都有适当的样式\n\n3. **响应式设计**：\n   - 在所有设备尺寸上都能完美展示\n   - 使用Tailwind的响应式类进行布局调整\n   - 移动设备上的优化排版和间距\n\n4. **中文排版优化**：\n   - 使用Noto Sans SC和Noto Serif SC字体，专为中文内容优化\n   - 适当的行高和字间距，提高中文文本可读性\n   - 考虑中文标点符号的排版特点\n\n5. **交互元素**：\n   - 滚动进度条显示阅读进度\n   - 回到顶部按钮\n   - 卡片和按钮的悬停效果\n   - 平滑滚动到锚点\n\n6. **数据可视化**：\n   - 使用Mermaid.js创建三个交互式图表，展示LLM发展、代理架构和规则系统\n   - 图表在深色/浅色模式下自动调整样式\n\n7. **内容组织**：\n   - 清晰的视觉层次结构，使用不同大小和样式的标题\n   - 使用卡片、图标和颜色编码来组织和分类信息\n   - 引人注目的Hero区域，立即吸引读者注意力\n\n8. **额外功能**：\n   - 分享按钮\n   - 延伸阅读推荐\n   - 作者信息区域\n   - 标签系统，突出关键主题\n\n这个设计不仅展示了内容，还通过精心的视觉设计和交互元素增强了用户体验，使复杂的技术内容更加易于理解和吸引人。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/system-prompts-and-models-of-ai-tools", "path_name": "Lovable/chinese/Lovable Prompt.txt", "full_range": {"startLineNumber": 28, "startColumn": 0, "endLineNumber": 28, "endColumn": 0}}]}]}]}