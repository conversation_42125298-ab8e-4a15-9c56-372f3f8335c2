{"name": "cofounder", "path": "/Users/<USER>/workspace/codeworkspace/cofounder", "workspace_id": "d16159525ae03f640f898ef6020313d1", "conversations": [{"id": "1743a01f-6903-4491-a239-5d56b42f64fd", "name": "", "created_at": "2025-04-18T06:35:50.729Z", "last_interacted_at": "2025-04-18T07:01:00.285Z", "messages": [{"request_id": "f4039f98-e3ca-4489-be8a-41db0e63b0f5", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "47ebefdc-119d-4ecb-9005-69d7cc3f5ca0", "request_message": "@cofounder/api/system/structure/sequences/projectInit.yaml  分析这个yaml 文件，梳理出其中的依赖关系【包含输入输出】，对于每个节点，检索 @functions 目录，每个角色对应其系统的提示词，分析每个角色的作用", "response_text": "I'll analyze the `projectInit.yaml` file and trace the dependencies between nodes, including inputs and outputs. Then I'll examine each role by looking at the corresponding system prompts in the `@functions` directory.\n\nLet me start by retrieving the content of the projectInit.yaml file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/structure/nodes/db/postgres.yaml", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "0bec8eae-d1e2-4a6a-a8ca-6bc0ad7defbd", "request_message": "", "response_text": "Now, let me search for the functions directory to understand the roles and system prompts:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/structure/nodes/db/postgres.yaml", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "ae24a2b9-787b-43b1-9b3b-9fc447d7afac", "request_message": "", "response_text": "Let me continue searching for more information about the functions directory and roles:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d34b7e78-3263-4213-ab4b-1078b43a9d42", "request_message": "", "response_text": "Let me continue searching for more information about the PM roles:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d0172bb1-47a4-43c1-a29b-4ea539d67a2f", "request_message": "", "response_text": "Let me search for information about the DB and BACKEND roles:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "e74b6ea9-b789-4211-bbee-4c62059351f7", "request_message": "", "response_text": "Let me search for information about the WEBAPP roles:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "71f5a120-5df3-4742-84db-329f0808c5c9", "request_message": "", "response_text": "Based on the information I've gathered, I'll now analyze the `projectInit.yaml` file and trace the dependencies between nodes, including inputs and outputs, and examine each role's function.\n\n# Analysis of projectInit.yaml\n\n## Dependency Structure Overview\n\nThe `projectInit.yaml` file defines a sequence called \"seq:project:init:v1\" which outlines the process of creating a project from initial details through to app generation. The sequence is organized into several layers:\n\n1. Project Setup\n2. Product Management Layer\n3. Database Layer\n4. Backend Layer\n5. UX Structure Layer\n6. Webapp & Designer Layer\n\n## Detailed Node Analysis and Dependencies\n\n### 1. Project Setup\n- **op:PROJECT::STATE:SETUP**\n  - **Role**: Initializes app folders and project state\n  - **Inputs**: User-provided project details\n  - **Outputs**: Project structure and initial state\n\n### 2. Product Management Layer (First Phase)\n\n- **PM:PRD::ANALYSIS**\n  - **Role**: Creates Product Requirements Document\n  - **Inputs**: User-provided project details\n  - **Outputs**: PRD document\n  - **System Prompt**: Acts as an expert product manager and product designer to create a comprehensive PRD document covering all required features for the web app\n  - **Dependencies**: op:PROJECT::STATE:SETUP\n\n- **PM:FRD::ANALYSIS**\n  - **Role**: Creates Features Requirements Document\n  - **Inputs**: PRD, user details\n  - **Outputs**: FRD document\n  - **System Prompt**: Acts as an expert product manager and product designer to create a Features Requirements Document focusing on user-facing features and journeys\n  - **Dependencies**: PM:PRD::ANALYSIS\n\n- **PM:DRD::ANALYSIS**\n  - **Role**: Creates Database Requirements Document\n  - **Inputs**: PRD, FRD, user details\n  - **Outputs**: DRD document\n  - **System Prompt**: Acts as an expert product manager and database designer to create a comprehensive database requirements document covering all data needs\n  - **Dependencies**: PM:FRD::ANALYSIS\n\n- **PM:UXSMD::ANALYSIS**\n  - **Role**: Creates UX Sitemap Document\n  - **Inputs**: PRD, FRD\n  - **Outputs**: UXSMD document\n  - **System Prompt**: Acts as an expert product manager and app designer to create a comprehensive UX Sitemap Document\n  - **Dependencies**: PM:FRD::ANALYSIS\n\n### 3. Database Layer\n\n- **DB:SCHEMAS::GENERATE**\n  - **Role**: Generates database schemas\n  - **Inputs**: DRD\n  - **Outputs**: Database schemas\n  - **System Prompt**: Acts as a genius Product Manager and DB designer to create comprehensive database schemas for the app MVP\n  - **Dependencies**: PM:DRD::ANALYSIS\n\n- **DB:POSTGRES::GENERATE**\n  - **Role**: Generates PostgreSQL commands\n  - **Inputs**: DRD, DB schemas\n  - **Outputs**: PostgreSQL commands for table creation and seed data\n  - **System Prompt**: Acts as a genius PostgreSQL expert to create database commands\n  - **Dependencies**: DB:SCHEMAS::GENERATE\n\n### 4. Product Management Layer (Second Phase)\n\n- **PM:BRD::ANALYSIS**\n  - **Role**: Creates Backend Requirements Document\n  - **Inputs**: DRD, DB schemas, PostgreSQL commands\n  - **Outputs**: BRD document, backend requirements\n  - **System Prompt**: Acts as an expert product manager, software architect, and API designer to determine backend specifications\n  - **Dependencies**: DB:POSTGRES::GENERATE\n\n### 5. Backend Layer\n\n- **BACKEND:OPENAPI::DEFINE**\n  - **Role**: Defines OpenAPI specifications for REST API\n  - **Inputs**: BRD, DB schemas\n  - **Outputs**: OpenAPI specifications\n  - **System Prompt**: Acts as a genius Product Manager, Software Architect, and API designer to create OpenAPI specs for the user-facing API\n  - **Dependencies**: PM:BRD::ANALYSIS\n  - **Conditional**: Only runs if backend.requirements?.restApi?.required is true\n\n- **BACKEND:ASYNCAPI::DEFINE**\n  - **Role**: Defines AsyncAPI specifications for WebSockets\n  - **Inputs**: BRD, DB schemas\n  - **Outputs**: AsyncAPI specifications\n  - **System Prompt**: Acts as a genius Product Manager, Software Architect, and Backend designer to create AsyncAPI specs for realtime features\n  - **Dependencies**: PM:BRD::ANALYSIS\n  - **Conditional**: Only runs if backend.requirements?.realtimeWebsockets?.required is true\n\n- **BACKEND:SERVER::GENERATE**\n  - **Role**: Generates backend server code\n  - **Inputs**: OpenAPI specs, AsyncAPI specs\n  - **Outputs**: Server code, dependencies, environment variables\n  - **System Prompt**: Acts as a genius backend dev expert to generate a full nodejs script based on specifications\n  - **Dependencies**: BACKEND:OPENAPI::DEFINE, BACKEND:ASYNCAPI::DEFINE\n\n### 6. Product Management Layer (Third Phase)\n\n- **PM:UXDMD::ANALYSIS**\n  - **Role**: Creates UX Datamap Document\n  - **Inputs**: UXSMD, backend server\n  - **Outputs**: UXDMD document\n  - **System Prompt**: Acts as a genius Product Manager and Software Architect to conduct analysis for frontend app architecture\n  - **Dependencies**: PM:UXSMD::ANALYSIS, BACKEND:SERVER::GENERATE\n\n### 7. UX Structure Layer\n\n- **UX:SITEMAP::STRUCTURE**\n  - **Role**: Defines UX sitemap structure\n  - **Inputs**: UXSMD\n  - **Outputs**: UX sitemap structure\n  - **System Prompt**: Determines views (normal, specialized, shared)\n  - **Dependencies**: PM:UXSMD::ANALYSIS\n\n- **UX:DATAMAP::STRUCTURE**\n  - **Role**: Defines UX datamap structure\n  - **Inputs**: UXDMD\n  - **Outputs**: App routes, slugs, params, views\n  - **System Prompt**: Acts as a genius Product Manager and Software Architect to define the app's data architecture\n  - **Dependencies**: PM:UXDMD::ANALYSIS\n\n- **UX:DATAMAP::VIEWS**\n  - **Role**: Defines detailed view structures\n  - **Inputs**: UX sitemap structure, UX datamap structure\n  - **Outputs**: Detailed view specifications with props and schemas\n  - **System Prompt**: Acts as a genius Product Manager and App Architect to detail the frontend app architecture structure\n  - **Dependencies**: UX:SITEMAP::STRUCTURE, UX:DATAMAP::STRUCTURE\n\n### 8. Webapp & Designer Layer\n\n- **WEBAPP:STORE::GENERATE**\n  - **Role**: Generates Redux store\n  - **Inputs**: UX datamap views\n  - **Outputs**: Redux store code\n  - **System Prompt**: Acts as an expert web app and react senior dev to write the code for the Redux store\n  - **Dependencies**: UX:DATAMAP::VIEWS\n\n- **WEBAPP:ROOT::GENERATE**\n  - **Role**: Generates root React component\n  - **Inputs**: Redux store\n  - **Outputs**: App.tsx component\n  - **System Prompt**: Acts as an expert web app and react senior dev to write the code for the root React component\n  - **Dependencies**: WEBAPP:STORE::GENERATE\n\n- **WEBAPP:VIEW::GENERATE:MULTI**\n  - **Role**: Generates all React views\n  - **Inputs**: Root React component\n  - **Outputs**: View components\n  - **System Prompt**: Acts as an expert web app and react senior dev to write the code for each view component\n  - **Dependencies**: WEBAPP:ROOT::GENERATE\n  - **Note**: May call designer if process.env.DESIGNER is enabled\n\n## Key Insights from the Analysis\n\n1. **Sequential Dependency Chain**: The system follows a clear sequential flow from requirements gathering to implementation, with each step building on the previous ones.\n\n2. **Role-Based Architecture**: Each node represents a specific role (PM, DB designer, Backend developer, etc.) with specialized system prompts tailored to that role.\n\n3. **Conditional Execution**: Some nodes (like BACKEND:OPENAPI::DEFINE and BACKEND:ASYNCAPI::DEFINE) only execute if certain conditions are met, making the system adaptable to different project requirements.\n\n4. **Layered Approach**: The system is organized into distinct layers (PM, DB, Backend, UX, Webapp) that correspond to different aspects of application development.\n\n5. **Document-Driven Development**: The process is heavily document-driven, with each phase producing documents that inform subsequent phases.\n\n6. **AI-Powered Generation**: Each node uses AI (via op:LLM::GEN) with specialized system prompts to generate its outputs.\n\n7. **Comprehensive Coverage**: The system covers all aspects of application development from requirements to implementation, ensuring a complete end-to-end solution.\n\nThis architecture enables the system to generate a complete web application from initial requirements, with each component being informed by and consistent with the others through the carefully designed dependency chain.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "34791b7d-16ca-4171-bf2a-17add375c980", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "4623b3ee-809b-411a-90da-7d287bf07e5f", "request_message": "使用中文回复", "response_text": "# projectInit.yaml 文件分析\n\n根据我对 `projectInit.yaml` 文件的分析，我将梳理出其中的依赖关系（包括输入输出）并分析每个角色的作用。\n\n## 整体依赖结构概览\n\n`projectInit.yaml` 文件定义了一个名为 \"seq:project:init:v1\" 的序列，该序列概述了从初始项目详情到应用程序生成的完整过程。该序列分为几个层次：\n\n1. 项目设置\n2. 产品管理层\n3. 数据库层\n4. 后端层\n5. UX 结构层\n6. Web应用和设计层\n\n## 节点详细分析及依赖关系\n\n### 1. 项目设置\n- **op:PROJECT::STATE:SETUP**\n  - **角色**：初始化应用文件夹和项目状态\n  - **输入**：用户提供的项目详情\n  - **输出**：项目结构和初始状态\n\n### 2. 产品管理层（第一阶段）\n\n- **PM:PRD::ANALYSIS**\n  - **角色**：创建产品需求文档\n  - **输入**：用户提供的项目详情\n  - **输出**：PRD文档\n  - **系统提示词**：作为专业产品经理和产品设计师，创建全面的PRD文档，涵盖Web应用所需的所有功能\n  - **依赖**：op:PROJECT::STATE:SETUP\n\n- **PM:FRD::ANALYSIS**\n  - **角色**：创建功能需求文档\n  - **输入**：PRD、用户详情\n  - **输出**：FRD文档\n  - **系统提示词**：作为专业产品经理和产品设计师，创建功能需求文档，专注于面向用户的功能和用户旅程\n  - **依赖**：PM:PRD::ANALYSIS\n\n- **PM:DRD::ANALYSIS**\n  - **角色**：创建数据库需求文档\n  - **输入**：PRD、FRD、用户详情\n  - **输出**：DRD文档\n  - **系统提示词**：作为专业产品经理和数据库设计师，创建全面的数据库需求文档，涵盖所有数据需求\n  - **依赖**：PM:FRD::ANALYSIS\n\n- **PM:UXSMD::ANALYSIS**\n  - **角色**：创建UX站点地图文档\n  - **输入**：PRD、FRD\n  - **输出**：UXSMD文档\n  - **系统提示词**：作为专业产品经理和应用设计师，创建全面的UX站点地图文档\n  - **依赖**：PM:FRD::ANALYSIS\n\n### 3. 数据库层\n\n- **DB:SCHEMAS::GENERATE**\n  - **角色**：生成数据库模式\n  - **输入**：DRD\n  - **输出**：数据库模式\n  - **系统提示词**：作为天才产品经理和数据库设计师，为应用MVP创建全面的数据库模式\n  - **依赖**：PM:DRD::ANALYSIS\n\n- **DB:POSTGRES::GENERATE**\n  - **角色**：生成PostgreSQL命令\n  - **输入**：DRD、数据库模式\n  - **输出**：用于表创建和种子数据的PostgreSQL命令\n  - **系统提示词**：作为天才PostgreSQL专家，创建数据库命令\n  - **依赖**：DB:SCHEMAS::GENERATE\n\n### 4. 产品管理层（第二阶段）\n\n- **PM:BRD::ANALYSIS**\n  - **角色**：创建后端需求文档\n  - **输入**：DRD、数据库模式、PostgreSQL命令\n  - **输出**：BRD文档、后端需求\n  - **系统提示词**：作为专业产品经理、软件架构师和API设计师，确定后端规格\n  - **依赖**：DB:POSTGRES::GENERATE\n\n### 5. 后端层\n\n- **BACKEND:OPENAPI::DEFINE**\n  - **角色**：为REST API定义OpenAPI规格\n  - **输入**：BRD、数据库模式\n  - **输出**：OpenAPI规格\n  - **系统提示词**：作为天才产品经理、软件架构师和API设计师，为面向用户的API创建OpenAPI规格\n  - **依赖**：PM:BRD::ANALYSIS\n  - **条件**：仅当backend.requirements?.restApi?.required为true时运行\n\n- **BACKEND:ASYNCAPI::DEFINE**\n  - **角色**：为WebSockets定义AsyncAPI规格\n  - **输入**：BRD、数据库模式\n  - **输出**：AsyncAPI规格\n  - **系统提示词**：作为天才产品经理、软件架构师和后端设计师，为实时功能创建AsyncAPI规格\n  - **依赖**：PM:BRD::ANALYSIS\n  - **条件**：仅当backend.requirements?.realtimeWebsockets?.required为true时运行\n\n- **BACKEND:SERVER::GENERATE**\n  - **角色**：生成后端服务器代码\n  - **输入**：OpenAPI规格、AsyncAPI规格\n  - **输出**：服务器代码、依赖项、环境变量\n  - **系统提示词**：作为天才后端开发专家，根据规格生成完整的nodejs脚本\n  - **依赖**：BACKEND:OPENAPI::DEFINE, BACKEND:ASYNCAPI::DEFINE\n\n### 6. 产品管理层（第三阶段）\n\n- **PM:UXDMD::ANALYSIS**\n  - **角色**：创建UX数据地图文档\n  - **输入**：UXSMD、后端服务器\n  - **输出**：UXDMD文档\n  - **系统提示词**：作为天才产品经理和软件架构师，为前端应用架构进行分析\n  - **依赖**：PM:UXSMD::ANALYSIS, BACKEND:SERVER::GENERATE\n\n### 7. UX结构层\n\n- **UX:SITEMAP::STRUCTURE**\n  - **角色**：定义UX站点地图结构\n  - **输入**：UXSMD\n  - **输出**：UX站点地图结构\n  - **系统提示词**：确定视图（普通、专业、共享）\n  - **依赖**：PM:UXSMD::ANALYSIS\n\n- **UX:DATAMAP::STRUCTURE**\n  - **角色**：定义UX数据地图结构\n  - **输入**：UXDMD\n  - **输出**：应用路由、URL参数、视图\n  - **系统提示词**：作为天才产品经理和软件架构师，定义应用的数据架构\n  - **依赖**：PM:UXDMD::ANALYSIS\n\n- **UX:DATAMAP::VIEWS**\n  - **角色**：定义详细的视图结构\n  - **输入**：UX站点地图结构、UX数据地图结构\n  - **输出**：带有属性和模式的详细视图规格\n  - **系统提示词**：作为天才产品经理和应用架构师，详细说明前端应用架构结构\n  - **依赖**：UX:SITEMAP::STRUCTURE, UX:DATAMAP::STRUCTURE\n\n### 8. Web应用和设计层\n\n- **WEBAPP:STORE::GENERATE**\n  - **角色**：生成Redux存储\n  - **输入**：UX数据地图视图\n  - **输出**：Redux存储代码\n  - **系统提示词**：作为专业Web应用和React高级开发人员，编写Redux存储的代码\n  - **依赖**：UX:DATAMAP::VIEWS\n\n- **WEBAPP:ROOT::GENERATE**\n  - **角色**：生成根React组件\n  - **输入**：Redux存储\n  - **输出**：App.tsx组件\n  - **系统提示词**：作为专业Web应用和React高级开发人员，编写根React组件的代码\n  - **依赖**：WEBAPP:STORE::GENERATE\n\n- **WEBAPP:VIEW::GENERATE:MULTI**\n  - **角色**：生成所有React视图\n  - **输入**：根React组件\n  - **输出**：视图组件\n  - **系统提示词**：作为专业Web应用和React高级开发人员，为每个视图组件编写代码\n  - **依赖**：WEBAPP:ROOT::GENERATE\n  - **注意**：如果启用了process.env.DESIGNER，可能会调用设计器\n\n## 分析的关键见解\n\n1. **顺序依赖链**：系统遵循从需求收集到实现的清晰顺序流程，每一步都建立在前一步的基础上。\n\n2. **基于角色的架构**：每个节点代表一个特定角色（产品经理、数据库设计师、后端开发人员等），并配有针对该角色定制的系统提示词。\n\n3. **条件执行**：某些节点（如BACKEND:OPENAPI::DEFINE和BACKEND:ASYNCAPI::DEFINE）仅在满足特定条件时执行，使系统能够适应不同的项目需求。\n\n4. **分层方法**：系统分为不同的层（产品管理、数据库、后端、UX、Web应用），对应于应用程序开发的不同方面。\n\n5. **文档驱动开发**：该过程主要由文档驱动，每个阶段产生的文档为后续阶段提供信息。\n\n6. **AI驱动生成**：每个节点使用AI（通过op:LLM::GEN）和专门的系统提示词来生成其输出。\n\n7. **全面覆盖**：系统涵盖了从需求到实现的应用程序开发的所有方面，确保了一个完整的端到端解决方案。\n\n这种架构使系统能够从初始需求生成完整的Web应用程序，通过精心设计的依赖链，每个组件都与其他组件保持一致和相互关联。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "b421754a-7c3f-448b-bab6-1f00c578e7a4", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "c666f71e-d12d-49a4-80ee-3c23aebf85e7", "request_message": "使用通俗易懂的语言为我解释每个角色的作用，预期产出的结果是什么？", "response_text": "# 项目初始化流程中各角色的通俗解释\n\n## 1. 项目设置 (op:PROJECT::STATE:SETUP)\n**角色作用**：就像是项目的\"搭建工地\"阶段。\n**通俗解释**：相当于在开始建房子前，先规划好地基，准备好工具和材料。\n**预期产出**：一个有组织的项目文件夹结构，为后续工作做好准备。\n\n## 2. 产品管理层角色\n\n### 产品需求文档分析师 (PM:PRD::ANALYSIS)\n**角色作用**：相当于\"需求收集员\"。\n**通俗解释**：就像是和客户交谈，了解他们想要什么样的房子，有几个卧室，需要什么风格等基本需求。\n**预期产出**：一份详细的产品需求文档(PRD)，描述应用的目标、功能和用户需求。\n\n### 功能需求文档分析师 (PM:FRD::ANALYSIS)\n**角色作用**：相当于\"功能规划师\"。\n**通俗解释**：在知道客户想要什么样的房子后，进一步规划每个房间的具体功能，比如厨房需要什么设备，客厅如何布置等。\n**预期产出**：一份功能需求文档(FRD)，详细描述应用的每个功能点和用户旅程。\n\n### 数据库需求文档分析师 (PM:DRD::ANALYSIS)\n**角色作用**：相当于\"数据规划师\"。\n**通俗解释**：决定房子里需要存放哪些物品，以及如何组织这些物品，比如书放在书架上，衣服放在衣柜里。\n**预期产出**：一份数据库需求文档(DRD)，描述应用需要存储哪些数据以及数据之间的关系。\n\n### UX站点地图文档分析师 (PM:UXSMD::ANALYSIS)\n**角色作用**：相当于\"房屋布局规划师\"。\n**通俗解释**：规划房子的整体布局，哪里是客厅，哪里是卧室，各个房间如何连接等。\n**预期产出**：一份UX站点地图文档(UXSMD)，描述应用的页面结构和导航流程。\n\n### 后端需求文档分析师 (PM:BRD::ANALYSIS)\n**角色作用**：相当于\"房屋系统规划师\"。\n**通俗解释**：规划房子的水电系统、暖气系统等基础设施，这些系统虽然用户看不见，但对房子的正常运行至关重要。\n**预期产出**：一份后端需求文档(BRD)，描述应用的后端服务需求，包括是否需要REST API或WebSocket等。\n\n### UX数据地图文档分析师 (PM:UXDMD::ANALYSIS)\n**角色作用**：相当于\"用户体验数据规划师\"。\n**通俗解释**：规划用户在使用房子时会接触到的各种数据和状态，比如开关灯的状态，温度控制的数据等。\n**预期产出**：一份UX数据地图文档(UXDMD)，描述用户界面中的数据流和状态管理。\n\n## 3. 数据库层角色\n\n### 数据库模式生成器 (DB:SCHEMAS::GENERATE)\n**角色作用**：相当于\"储物规划师\"。\n**通俗解释**：设计具体的储物方案，比如书架的大小和格式，衣柜的分区等。\n**预期产出**：数据库表结构定义，包括字段、类型、关系等。\n\n### PostgreSQL命令生成器 (DB:POSTGRES::GENERATE)\n**角色作用**：相当于\"储物安装工\"。\n**通俗解释**：根据储物规划，提供具体的安装指南，告诉工人如何搭建书架和衣柜。\n**预期产出**：可执行的PostgreSQL命令，用于创建数据库表和插入初始数据。\n\n## 4. 后端层角色\n\n### OpenAPI定义器 (BACKEND:OPENAPI::DEFINE)\n**角色作用**：相当于\"门铃系统设计师\"。\n**通俗解释**：设计房子的对外接口，比如门铃、信箱等，让外部世界能够与房子交互。\n**预期产出**：OpenAPI规范文档，定义REST API的端点、参数和响应。\n\n### AsyncAPI定义器 (BACKEND:ASYNCAPI::DEFINE)\n**角色作用**：相当于\"对讲机系统设计师\"。\n**通俗解释**：设计房子内部的实时通信系统，比如对讲机，让房子的不同部分能够实时通信。\n**预期产出**：AsyncAPI规范文档，定义WebSocket等实时通信接口。\n\n### 后端服务器生成器 (BACKEND:SERVER::GENERATE)\n**角色作用**：相当于\"房屋系统安装工\"。\n**通俗解释**：根据设计图纸，实际安装房子的水电系统、暖气系统等。\n**预期产出**：完整的后端服务器代码，包括API实现、数据库连接等。\n\n## 5. UX结构层角色\n\n### UX站点地图结构生成器 (UX:SITEMAP::STRUCTURE)\n**角色作用**：相当于\"房间布局师\"。\n**通俗解释**：根据整体规划，确定每个房间的具体位置和大小。\n**预期产出**：详细的应用页面结构，包括页面类型、层次关系等。\n\n### UX数据地图结构生成器 (UX:DATAMAP::STRUCTURE)\n**角色作用**：相当于\"房间功能规划师\"。\n**通俗解释**：规划每个房间需要哪些功能设备，比如客厅需要电视、沙发等。\n**预期产出**：应用的数据结构定义，包括全局状态、路由参数等。\n\n### UX数据地图视图生成器 (UX:DATAMAP::VIEWS)\n**角色作用**：相当于\"房间细节设计师\"。\n**通俗解释**：设计每个房间的具体细节，比如沙发的款式、电视的尺寸等。\n**预期产出**：详细的视图规格，包括每个视图的状态变量、动作和交互。\n\n## 6. Web应用和设计层角色\n\n### Redux存储生成器 (WEBAPP:STORE::GENERATE)\n**角色作用**：相当于\"中央控制系统安装工\"。\n**通俗解释**：安装房子的中央控制系统，管理整个房子的状态，比如电源、温度等。\n**预期产出**：Redux存储代码，管理应用的全局状态。\n\n### 根React组件生成器 (WEBAPP:ROOT::GENERATE)\n**角色作用**：相当于\"房屋框架搭建工\"。\n**通俗解释**：搭建房子的主体框架，确定整体结构。\n**预期产出**：App.tsx组件代码，作为应用的入口点和主框架。\n\n### React视图生成器 (WEBAPP:VIEW::GENERATE:MULTI)\n**角色作用**：相当于\"房间装修工\"。\n**通俗解释**：根据设计，装修每个房间，安装家具、电器等。\n**预期产出**：所有视图组件的代码，实现应用的各个页面和功能。\n\n## 整体流程的最终产出\n\n整个流程完成后，你将得到一个功能完整的Web应用程序，包括：\n\n1. **完整的需求和设计文档**：从产品需求到具体实现的全套文档。\n2. **数据库结构**：可直接使用的数据库表结构和初始数据。\n3. **后端服务**：实现了所有必要API的后端服务器代码。\n4. **前端应用**：包含所有页面和功能的React应用。\n\n这就像是从一张草图开始，最终得到一座可以入住的完整房子，每个角色都负责房子建设过程中的一个关键环节。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "49f89c35-926c-40df-ac17-2c81f797e5c5", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "e66e7c34-e040-4f3f-85fd-3929d055dce7", "request_message": "为我详细解释前端领域需要依赖什么文档，逻辑关系是什么？解决了什么问题", "response_text": "# 前端领域的文档依赖与逻辑关系\n\n## 前端开发所依赖的关键文档\n\n在这个项目初始化流程中，前端开发主要依赖以下几个关键文档：\n\n1. **UX站点地图文档(UXSMD)**\n2. **UX数据地图文档(UXDMD)**\n3. **UX站点地图结构(UX:SITEMAP::STRUCTURE)**\n4. **UX数据地图结构(UX:DATAMAP::STRUCTURE)**\n5. **UX数据地图视图(UX:DATAMAP::VIEWS)**\n6. **后端API规范(OpenAPI和AsyncAPI)**\n\n## 这些文档之间的逻辑关系\n\n### 1. 从用户需求到界面结构\n\n**逻辑链路**：PRD → FRD → UXSMD → UX:SITEMAP::STRUCTURE\n\n**关系解释**：\n- 首先，产品需求文档(PRD)定义了产品的整体目标和功能。\n- 功能需求文档(FRD)进一步细化这些功能，描述用户旅程。\n- UX站点地图文档(UXSMD)基于这些功能需求，规划出应用的页面结构和导航流程。\n- UX站点地图结构(UX:SITEMAP::STRUCTURE)将UXSMD转化为具体的技术规格，定义页面类型和层次关系。\n\n**解决的问题**：\n- 确保前端界面结构与产品需求一致\n- 提供清晰的导航路径和用户流程\n- 定义哪些是独特视图(UV_*)，哪些是共享视图(GV_*)\n- 建立页面之间的关系和跳转逻辑\n\n### 2. 从数据需求到界面数据流\n\n**逻辑链路**：DRD → BRD → BACKEND:SERVER::GENERATE → UXDMD → UX:DATAMAP::STRUCTURE\n\n**关系解释**：\n- 数据库需求文档(DRD)定义了应用需要存储的数据。\n- 后端需求文档(BRD)基于DRD，确定后端服务的需求。\n- 后端服务器生成(BACKEND:SERVER::GENERATE)实现了这些需求。\n- UX数据地图文档(UXDMD)分析前端如何与这些数据交互。\n- UX数据地图结构(UX:DATAMAP::STRUCTURE)将UXDMD转化为技术规格，定义应用的路由、参数和全局状态。\n\n**解决的问题**：\n- 确保前端能够正确获取和处理后端数据\n- 定义应用的路由结构和URL参数\n- 规划全局状态管理需求\n- 建立前端与后端API的连接点\n\n### 3. 从页面结构和数据流到具体视图\n\n**逻辑链路**：UX:SITEMAP::STRUCTURE + UX:DATAMAP::STRUCTURE → UX:DATAMAP::VIEWS\n\n**关系解释**：\n- UX站点地图结构定义了页面的类型和层次。\n- UX数据地图结构定义了数据流和路由。\n- UX数据地图视图(UX:DATAMAP::VIEWS)结合这两者，为每个视图定义详细规格，包括状态变量、动作和交互。\n\n**解决的问题**：\n- 详细定义每个视图的功能和行为\n- 规划视图内的状态管理\n- 确定视图如何响应用户交互\n- 定义视图与后端API的交互方式\n\n### 4. 从视图规格到实际代码\n\n**逻辑链路**：UX:DATAMAP::VIEWS → WEBAPP:STORE::GENERATE → WEBAPP:ROOT::GENERATE → WEBAPP:VIEW::GENERATE:MULTI\n\n**关系解释**：\n- UX数据地图视图提供了详细的视图规格。\n- Redux存储生成(WEBAPP:STORE::GENERATE)基于这些规格创建全局状态管理。\n- 根React组件生成(WEBAPP:ROOT::GENERATE)创建应用的主框架。\n- React视图生成(WEBAPP:VIEW::GENERATE:MULTI)实现每个具体视图。\n\n**解决的问题**：\n- 将设计规格转化为可执行代码\n- 确保代码实现与设计规格一致\n- 建立统一的状态管理系统\n- 创建一致的用户界面和交互体验\n\n## 前端文档依赖解决的核心问题\n\n### 1. 一致性问题\n\n**问题**：如何确保前端实现与产品需求、用户体验设计和后端服务保持一致？\n\n**解决方案**：通过建立清晰的文档依赖链，从PRD到具体的视图规格，每一步都基于前一步的输出，确保信息的连贯性和一致性。例如，UX:DATAMAP::VIEWS直接依赖于UX:SITEMAP::STRUCTURE和UX:DATAMAP::STRUCTURE，确保视图设计与整体结构和数据流一致。\n\n### 2. 前后端协作问题\n\n**问题**：前端如何知道后端提供哪些API，以及如何与这些API交互？\n\n**解决方案**：通过将后端API规范(OpenAPI和AsyncAPI)作为UXDMD的输入，确保前端设计考虑了后端能力。UX数据地图视图明确定义了每个视图如何与后端API交互，包括调用哪些端点、发送什么参数、如何处理响应等。\n\n### 3. 状态管理问题\n\n**问题**：如何管理复杂应用中的状态，确保数据流的清晰和可维护？\n\n**解决方案**：UX:DATAMAP::STRUCTURE定义了应用的全局状态需求，UX:DATAMAP::VIEWS定义了每个视图的局部状态需求。这些规格直接指导WEBAPP:STORE::GENERATE创建合适的Redux存储，建立清晰的状态管理架构。\n\n### 4. 路由和导航问题\n\n**问题**：如何组织应用的路由结构，确保用户能够顺畅导航？\n\n**解决方案**：UX:SITEMAP::STRUCTURE定义了页面层次和关系，UX:DATAMAP::STRUCTURE定义了具体的路由路径、参数和跳转逻辑。这些规格直接指导WEBAPP:ROOT::GENERATE创建应用的路由系统。\n\n### 5. 组件复用问题\n\n**问题**：如何识别和设计可复用的组件，避免代码重复？\n\n**解决方案**：UX:SITEMAP::STRUCTURE明确区分了独特视图(UV_*)和共享视图(GV_*)，UX:DATAMAP::VIEWS为每种视图提供了详细规格。这使得WEBAPP:VIEW::GENERATE:MULTI能够识别共享组件并实现适当的复用。\n\n### 6. 用户体验一致性问题\n\n**问题**：如何确保整个应用的用户体验保持一致？\n\n**解决方案**：通过统一的文档链和生成过程，确保所有视图遵循相同的设计原则和交互模式。WEBAPP:VIEW::GENERATE:MULTI基于统一的规格生成所有视图，确保视觉和交互的一致性。\n\n## 实际案例说明\n\n以一个用户登录功能为例，说明文档依赖链如何工作：\n\n1. **PRD**定义了需要用户登录功能。\n2. **FRD**详细描述了登录流程，包括输入验证、错误处理等。\n3. **UXSMD**规划了登录页面在整个应用中的位置和与其他页面的关系。\n4. **DRD**定义了用户数据的存储结构。\n5. **BRD**确定了需要REST API来处理登录请求。\n6. **OpenAPI**定义了登录API的端点、参数和响应。\n7. **UX:SITEMAP::STRUCTURE**将登录页面定义为独特视图(UV_Login)。\n8. **UX:DATAMAP::STRUCTURE**定义了登录页面的路由(/login)和全局认证状态。\n9. **UX:DATAMAP::VIEWS**详细定义了登录视图的状态变量(用户名、密码)、动作(提交表单)和与后端API的交互。\n10. **WEBAPP:STORE::GENERATE**创建了管理认证状态的Redux存储。\n11. **WEBAPP:ROOT::GENERATE**在路由系统中添加了登录路由。\n12. **WEBAPP:VIEW::GENERATE:MULTI**实现了登录视图组件，包括表单、验证和API调用。\n\n通过这个完整的依赖链，确保了登录功能从需求到实现的一致性和完整性。\n\n## 总结\n\n前端开发依赖的文档形成了一个连贯的链条，从用户需求到具体实现，每一步都建立在前一步的基础上。这种结构化的方法解决了前端开发中的一致性、协作、状态管理、路由、组件复用和用户体验等核心问题，确保了最终产品的质量和用户满意度。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "9fbeaa95-5464-4b0b-80f9-57c4e5dd27fc", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "b91a0275-6ae0-4a99-974f-b562040d4c32", "request_message": "为我将每个角色的系统提示词提炼出来，翻译成中文，输出到同级目录单独的文件中，命名：角色.prompt ", "response_text": "我将为您提取每个角色的系统提示词，翻译成中文，并准备保存到单独的文件中。让我先检索每个角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "80d6a0d7-58c7-49b7-bc98-8bbd1f02661e", "request_message": "", "response_text": "现在我将继续检索其他角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d8a1d6cf-4902-4d44-b889-3b0bfd8521d8", "request_message": "", "response_text": "让我继续检索其他角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "121cbc3b-5991-4377-88d5-60857516fa19", "request_message": "", "response_text": "让我继续检索其他角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "27ba6e2b-db23-4b8d-86bc-5f6f415a2e8e", "request_message": "", "response_text": "让我继续检索其他角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "654cf802-e61b-4e09-b35b-450f87e31cc3", "request_message": "", "response_text": "让我继续检索其他角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "91e70753-9f71-4443-8b38-2cbd186d8b9d", "request_message": "", "response_text": "现在我将检索数据库和后端角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "331e9aeb-947b-4082-b20a-2bc34c85dc82", "request_message": "", "response_text": "让我继续检索后端角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "3e464bfa-9d52-4f2d-bb79-2866efc1b72b", "request_message": "", "response_text": "让我继续检索UX和WEBAPP角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "5436204d-342a-4cbd-89ce-d582cdcec768", "request_message": "", "response_text": "让我继续检索WEBAPP角色的系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "12902064-1ddc-4b7b-8aa8-9dd30469f174", "request_message": "", "response_text": "现在我已经收集了所有角色的系统提示词，我将为每个角色创建一个单独的文件，翻译成中文，并保存到同级目录中。\n\n让我开始创建这些文件：\n\n1. 首先，创建PM:PRD::ANALYSIS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "84c1d247-59c4-4b89-83fa-fa0ab85d9b7e", "request_message": "", "response_text": "2. 创建PM:FRD::ANALYSIS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "82d4d09c-b0ce-495f-bfd9-6564856b7dba", "request_message": "", "response_text": "3. 创建PM:DRD::ANALYSIS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "57a1811d-c345-4f9e-adbe-b7efc5447add", "request_message": "", "response_text": "4. 创建PM:UXSMD::ANALYSIS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/ux/datamap.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "7664c3df-14c1-47c6-9687-82345055ee7f", "request_message": "", "response_text": "5. 创建PM:BRD::ANALYSIS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d57f3147-18d8-473e-a72f-8b781539698b", "request_message": "", "response_text": "6. 创建PM:UXDMD::ANALYSIS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "2e3c9141-ae78-45db-9463-870422d14d05", "request_message": "", "response_text": "7. 创建DB:SCHEMAS::GENERATE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "4a748c4e-dc22-43f9-8eeb-d461b97445ad", "request_message": "", "response_text": "8. 创建DB:POSTGRES::GENERATE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "fa58626e-314e-4f26-a825-0b35a02c9154", "request_message": "", "response_text": "9. 创建BACKEND:OPENAPI::DEFINE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.js", "full_range": {"startLineNumber": 49, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}]}, {"request_id": "140fbf57-8d77-4ecc-904e-46661504aac1", "request_message": "", "response_text": "10. 创建BACKEND:ASYNCAPI::DEFINE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.js", "full_range": {"startLineNumber": 31, "startColumn": 0, "endLineNumber": 74, "endColumn": 15}}]}, {"request_id": "ca1e12e6-0df8-4382-b23a-2f0d59d05c54", "request_message": "", "response_text": "11. 创建BACKEND:SERVER::GENERATE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.js", "full_range": {"startLineNumber": 31, "startColumn": 0, "endLineNumber": 74, "endColumn": 15}}]}, {"request_id": "da77e457-e026-4e1f-b1cb-0dbb4b603eea", "request_message": "", "response_text": "12. 创建UX:SITEMAP::STRUCTURE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/frd.js", "full_range": {"startLineNumber": 13, "startColumn": 0, "endLineNumber": 50, "endColumn": 42}}]}, {"request_id": "3639c801-097c-4720-acd8-5d27c5eef2da", "request_message": "", "response_text": "13. 创建UX:DATAMAP::STRUCTURE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/uxdmd.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "ed759aea-6761-4744-a0e1-5bb049be4888", "request_message": "", "response_text": "14. 创建UX:DATAMAP::VIEWS角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/uxdmd.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "6dfa808d-907f-4a3e-875d-07e6cf8562b2", "request_message": "", "response_text": "15. 创建WEBAPP:STORE::GENERATE角色的提示词文件：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/uxdmd.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "edd98414-88da-46d3-bc2a-e1a14dd28e6b", "request_message": "", "response_text": "16. 创建WEBAPP:ROOT::GENERATE角色的提示词文件：", "timestamp": "", "workspace_files": []}]}, {"id": "c1708322-b749-40f6-bd83-5f23d62dcfb9", "name": "I'll help you analyze the system prompts in the codebase and create Chinese translations. Let me start by retrieving the content of the specified file.\n", "created_at": "2025-04-18T07:01:17.414Z", "last_interacted_at": "2025-04-21T01:47:04.766Z", "messages": [{"request_id": "ecb7e731-d221-43bb-ba1d-7037077f81fd", "request_message": "@cofounder/api/system/functions/op/ 下面包含了 对应的角色文件夹，每个文件夹里面有对应的 .js 文件，其中主要包含了 系统提示词的动态拼接，请你帮我检索对应的系统提示词，也就是 messages， role = system 即为系统提示词，user 代表用户提示词，请你先帮我查看 @cofounder/api/system/functions/pm/prd.js 你先按照叙述的分析一波，我的目的是将其系统提示词完整翻译成中文，对于动态变量采用 {{xxx}} 的方式标注，标注完成之后并给与说明，请说出你的规划，输出的翻译之后的内容在同级目录创建新的文件来承载", "response_text": "I'll help you analyze the system prompts in the codebase, specifically focusing on the `@cofounder/api/system/functions/pm/prd.js` file, and then create a Chinese translation of the system prompts with dynamic variables marked as {{xxx}}. Let me break this down into steps.\n\n## Understanding Your Request\n1. You want me to examine the system prompts in the codebase\n2. Specifically look at `@cofounder/api/system/functions/pm/prd.js`\n3. Translate the system prompts (where role=\"system\") into Chinese\n4. Mark dynamic variables with {{xxx}} format\n5. Create a new file in the same directory to store the translated content\n\nLet me start by retrieving the relevant information from the codebase.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 15, "startColumn": 0, "endLineNumber": 15, "endColumn": 20}}]}, {"request_id": "992e008b-0b98-4b45-b9e8-cb866b56ea1c", "request_message": "", "response_text": "Now I need to look at the structure of the system prompts in the `prd.js` file. Let me examine the file in more detail to understand the system prompt structure.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 15, "startColumn": 0, "endLineNumber": 15, "endColumn": 20}}]}, {"request_id": "cf2818f1-13df-4fe5-b7a5-bb4d5e93d243", "request_message": "", "response_text": "Now I have a clear understanding of the system prompt in the `prd.js` file. Let me create a plan for translating this into Chinese:\n\n## Plan for Translation\n\n1. Extract the system prompt from the `prd.js` file (lines 31-76)\n2. Translate the content into Chinese, preserving the formatting\n3. Mark any dynamic variables with {{xxx}} format\n4. Create a new file in the same directory to store the translated content\n5. Provide explanations for the dynamic variables\n\nLet me now create the Chinese translation of the system prompt:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 15, "startColumn": 0, "endLineNumber": 15, "endColumn": 20}}]}, {"request_id": "8810f1e2-eb16-4987-8ab5-977a1f5cd827", "request_message": "", "response_text": "## Translation Analysis and Explanation\n\nI've successfully translated the system prompt from the `prd.js` file into Chinese and created a new file called `prd.zh.js` in the same directory. Here's an explanation of the translation:\n\n### Dynamic Variables\nIn the translation, I identified the following dynamic variables:\n1. `${text}` - This is a variable that contains the user-provided text description of the app project. I've preserved this as is since it's a code variable that will be populated at runtime.\n\n### Translation Approach\n1. I maintained the exact same structure and formatting as the original file\n2. I translated all English text to Chinese while preserving the meaning and intent\n3. I kept all code elements, function names, and variable names unchanged\n4. I translated the comments to provide context in Chinese\n5. I preserved all markdown formatting instructions\n\n### Key Points in the Translation\n1. The system prompt instructs the AI to act as a product manager and product designer\n2. It emphasizes creating a comprehensive PRD (Product Requirements Document) that covers 100% of the requirements\n3. It includes a series of self-check questions to ensure thoroughness\n4. It requests an \"Additional Analysis\" section for self-criticism\n5. It specifies formatting requirements (markdown) and focus areas (MVP features)\n\nThe translation preserves all these key elements while making them accessible to Chinese-speaking users.\n\nWould you like me to translate any other system prompts from the codebase? I noticed there are several other files in the `@cofounder/api/system/functions/pm/` directory that contain similar system prompts.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/db/postgres.js", "full_range": {"startLineNumber": 15, "startColumn": 0, "endLineNumber": 15, "endColumn": 20}}]}, {"request_id": "7361da98-1ccd-4a5a-bddc-245a09b83213", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "903a8b01-ee61-402e-8310-bc0998f96e3f", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "7b605d7e-809f-46c3-ae1c-d78fabaf83ee", "request_message": "OK 按照上述方式完成其他的所有文件的翻译 ", "response_text": "I'll translate all the other files in the `@cofounder/api/system/functions/pm/` directory following the same approach. Let me first identify all the files in that directory that need translation.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d970f297-fb6a-42c9-91b4-bbaa82bc59f2", "request_message": "", "response_text": "Now I'll examine each file to understand its structure and system prompts, then translate them one by one. Let's start with `frd.js` since it's one of the shorter files.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "993d5896-c40a-4635-9d50-96aae5581456", "request_message": "", "response_text": "Now I'll create the Chinese translation for `frd.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "ecb5ad01-1546-4c9d-b0fa-583cbab93ec1", "request_message": "", "response_text": "Now let's translate the `fjmd.js` file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "03b04cff-d0a1-4766-8fbe-67e889e0b4da", "request_message": "", "response_text": "Now I'll create the Chinese translation for `fjmd.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "0a18b0c3-d321-465d-a335-207d04d2b4dc", "request_message": "", "response_text": "Now let's translate the `drd.js` file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "e47be536-696e-4192-824c-62b797d5230a", "request_message": "", "response_text": "Now I'll create the Chinese translation for `drd.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "9cccbd20-c921-482e-ad6b-8887040db623", "request_message": "", "response_text": "Now let's translate the `uxsmd.js` file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "14192ac2-86f6-4e20-be81-8b723d44f8e9", "request_message": "", "response_text": "Now I'll create the Chinese translation for `uxsmd.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d43969ce-7cea-4cee-8947-13754b88a929", "request_message": "", "response_text": "Now let's translate the `uxdmd.js` file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "10d70cf9-4e1e-48c6-bbf6-403f49beed45", "request_message": "", "response_text": "Now I'll create the Chinese translation for `uxdmd.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "7ed15b8c-fbbd-44eb-a9b1-c551cb428628", "request_message": "", "response_text": "Finally, let's translate the `brd.js` file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "22da2d73-9d6f-4926-a3e0-40864429a160", "request_message": "", "response_text": "Now I'll create the Chinese translation for `brd.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "e8f0bad3-4832-44b2-beec-9da2bf3198ef", "request_message": "", "response_text": "## Summary of Completed Translations\n\nI have successfully translated all the system prompts from the `@cofounder/api/system/functions/pm/` directory into Chinese. Here's a summary of the files I've translated:\n\n1. `prd.js` → `prd.zh.js` - Product Requirements Document system prompt\n2. `frd.js` → `frd.zh.js` - Features Requirements Document system prompt\n3. `fjmd.js` → `fjmd.zh.js` - Feature Journeys Maps Document system prompt\n4. `drd.js` → `drd.zh.js` - Database Requirements Document system prompt\n5. `uxsmd.js` → `uxsmd.zh.js` - UX Sitemap Document system prompt\n6. `uxdmd.js` → `uxdmd.zh.js` - UX Datamap Document system prompt\n7. `brd.js` → `brd.zh.js` - Backend Requirements Document system prompt\n\nFor each file, I:\n1. Maintained the exact same structure and functionality as the original\n2. Translated all English text to Chinese while preserving the meaning and intent\n3. Kept all code elements, function names, and variable names unchanged\n4. Translated the comments to provide context in Chinese\n5. Preserved all formatting instructions and dynamic variables\n\nAll the translated files have been created in the same directory as their English counterparts, with the `.zh.js` extension to indicate they are Chinese versions.\n\nThe translations maintain the specialized terminology used in product management and software development, ensuring that Chinese-speaking users can understand and use these system prompts effectively.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/pm/prd.zh.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "126d1d0a-260c-4685-a78a-cb4786e650cc", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "293f3efa-4c02-462a-98d6-40b381319a8d", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "705e3f04-4393-4141-a656-cb555dd35f57", "request_message": "@cofounder/api/system/functions/ux/ 继续翻译这个目录下面的内容 ", "response_text": "I'll continue by translating the system prompts in the `@cofounder/api/system/functions/ux/` directory. Let me first check what files are in this directory.\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d7194fc5-2e09-4ca0-93dd-a646c8edd7d6", "request_message": "", "response_text": "Now I'll examine each file to understand its structure and system prompts, then translate them one by one. Let's start with `sitemap.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "a46540a0-7a7d-420b-b1af-5dcc24d6d35b", "request_message": "", "response_text": "Now let's look at the `datamap.js` file:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "b99cd4b3-ad96-4349-a235-ea5dab609476", "request_message": "", "response_text": "Now I'll create the Chinese translations for both files. Let's start with `sitemap.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "e775a39c-d944-451d-b840-14eb425bb826", "request_message": "", "response_text": "Now let's create the Chinese translation for `datamap.js`:\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "d38793b1-50c1-4859-a43d-d689d31a2514", "request_message": "", "response_text": "## Summary of Completed Translations\n\nI have successfully translated the system prompts from the `@cofounder/api/system/functions/ux/` directory into Chinese. Here's a summary of the files I've translated:\n\n1. `sitemap.js` → `sitemap.zh.js` - UX Sitemap structure system prompt\n2. `datamap.js` → `datamap.zh.js` - UX Datamap structure system prompt\n\nFor each file, I:\n1. Maintained the exact same structure and functionality as the original\n2. Translated all English text to Chinese while preserving the meaning and intent\n3. Kept all code elements, function names, and variable names unchanged\n4. Translated the comments to provide context in Chinese\n5. Preserved all formatting instructions and dynamic variables\n\nThe translations maintain the specialized terminology used in UX design and software architecture, ensuring that Chinese-speaking users can understand and use these system prompts effectively.\n\nThese translations, combined with the previously translated PM (Product Manager) files, provide a comprehensive set of Chinese system prompts for the core functionality of the system.\n\nWould you like me to translate any other files or directories in the codebase?\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "44e5347d-4f85-44ed-b87b-a3c28f0f147a", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "25203fe6-da66-457e-aa4f-8e047658d856", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "a476a9d7-b0f0-4f75-81ac-5f52c055c070", "request_message": "分析 @cofounder/api/system/functions/pm/ 下的每个角色的 关键系统提示词", "response_text": "我将分析 `@cofounder/api/system/functions/pm/` 目录下每个角色的关键系统提示词。这个目录包含了与产品管理相关的不同角色和文档生成的系统提示词。\n\n## 分析 PM 目录下的关键系统提示词\n\n让我首先列出这个目录中的所有文件，然后分析每个文件中的关键系统提示词：\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "25f67a53-6dc8-471d-b95a-2f6b1853ecad", "request_message": "", "response_text": "现在我将分析每个文件中的关键系统提示词，了解每个角色的主要职责和功能：\n\n### 1. PRD (产品需求文档) - prd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "215486a6-1f04-4b35-b922-ac779a01a4f5", "request_message": "", "response_text": "### 2. F<PERSON> (功能需求文档) - frd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "95f9a0ae-1a2d-465f-b592-f3f6733dd327", "request_message": "", "response_text": "### 3. <PERSON><PERSON><PERSON> (功能旅程地图文档) - fjmd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "daa4d72e-43a5-40b2-83e5-7bffc2dc4d4d", "request_message": "", "response_text": "### 4. DR<PERSON> (数据库需求文档) - drd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "a6204b04-d7a0-4a7f-88ab-b78bd272f4e0", "request_message": "", "response_text": "### 5. UXSMD (UX站点地图文档) - uxsmd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "ec099a0e-fc10-4b24-aaa1-64148939be27", "request_message": "", "response_text": "### 6. UXDMD (UX数据地图文档) - uxdmd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "676e5505-2221-4ec8-9ac2-c22dae4070d9", "request_message": "", "response_text": "### 7. <PERSON><PERSON> (后端需求文档) - brd.js\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "fcd63441-49e2-4221-9ce5-3412187d058a", "request_message": "", "response_text": "## PM 目录下的关键系统提示词分析\n\n基于对 `@cofounder/api/system/functions/pm/` 目录下文件的分析，我总结了每个角色的关键系统提示词：\n\n### 1. 产品经理和产品设计师 (PRD - 产品需求文档)\n\n**关键职责**：\n- 对网页应用项目进行全面分析，创建详细的产品需求文档\n- 确保分析涵盖应用的所有预期功能和细节\n- 考虑用户视角，确保不遗漏任何重要功能\n\n**关键提示词**：\n- \"你是一位专业的产品经理和产品设计师\"\n- \"你的分析非常详细、全面，涵盖了网页应用所需的绝对100%的所有内容\"\n- \"应用的详细描述是什么，以及它所有预期的功能是什么？\"\n- \"用户角色是什么？他们的用户故事是什么？\"\n- \"用户旅程怎么样？我是否涵盖了所有用户的所有可能旅程？\"\n- \"我是否确保我在PRD中详述的内容绝对100%全面，并且可以直接投入开发？\"\n\n**输出格式**：Markdown格式的PRD文档\n\n### 2. 功能需求专家 (FRD - 功能需求文档)\n\n**关键职责**：\n- 参考PRD，创建功能需求文档\n- 重点关注面向用户的功能\n- 确保涵盖所有用户视角的预期功能\n\n**关键提示词**：\n- \"你的工作是参考提供的网页应用详情和分析PRD，为其创建一份功能需求文档(FRD)\"\n- \"重点是面向用户的功能\"\n- \"我是否涵盖了应用所需的目的和功能？\"\n- \"我是否从所有用户的角度涵盖了预期功能？甚至是小细节？\"\n- \"强调面向用户的功能和核心应用MVP功能\"\n\n**输出格式**：Markdown格式的FRD文档\n\n### 3. 用户旅程地图专家 (FJMD - 功能旅程地图文档)\n\n**关键职责**：\n- 参考PRD和FRD，创建功能旅程地图文档\n- 详细描述所有用户旅程和场景\n- 确保涵盖所有可能的用户旅程\n\n**关键提示词**：\n- \"你的工作是参考提供的网页应用详情、分析、PRD和FRD，为其创建一份全面完整的功能旅程地图文档(FJMD)\"\n- \"你的分析应涵盖所有旅程情况（应用MVP的）\"\n- \"我是否100%涵盖了用户旅程？\"\n- \"从不同场景中不同视角思考许多不同的核心旅程\"\n- \"全面涵盖所有内容\"\n\n**输出格式**：YAML格式的FJMD文档，包含详细的旅程步骤、参与者、前置条件、后置条件等\n\n### 4. 数据库设计专家 (DRD - 数据库需求文档)\n\n**关键职责**：\n- 设计应用所需的数据库结构\n- 确保数据库模式涵盖所有功能需求\n- 从多个角色视角思考数据需求\n\n**关键提示词**：\n- \"你是一位专业的产品经理和数据库设计师\"\n- \"数据库模式分析应全面，涵盖应用MVP所需的一切，不多也不少\"\n- \"使用应用的关键角色有哪些？\"\n- \"用户期望看到的功能所需的所有模式是什么？\"\n- \"内部需要哪些模式来涵盖所有功能工作流？\"\n- \"在分析的模式部分，只使用基本原语，如数字、字符串、json等\"\n- \"使用snake_case进行任何命名\"\n\n**输出格式**：Markdown格式的DRD文档\n\n### 5. UX站点地图专家 (UXSMD - UX站点地图文档)\n\n**关键职责**：\n- 创建应用的UX站点地图\n- 定义所有UI视图（独特视图和共享全局视图）\n- 建立视图之间的导航关系\n\n**关键提示词**：\n- \"你是一位专业的产品经理和应用设计师\"\n- \"我是否在分析中涵盖了共享全局UI视图（即顶部导航、页脚等）？\"\n- \"我是否涵盖了所有需要的独特UI视图；对于所有所需的功能？\"\n- \"我是否为它们分配了独特且表达性的首字母大写ID（格式为'UV_{...}'或'GV_{...}'）？\"\n- \"我是否涵盖了预期在这个网页应用中出现的所有内容？\"\n- \"我能否为不同视图之间的所有交叉链接分析制作一个表格，以建立应用内导航关系？\"\n- \"GV_*共享视图独立于UV_*！它们只是共享屏幕空间！\"\n\n**输出格式**：Markdown格式的UXSMD文档\n\n### 6. 前端架构专家 (UXDMD - UX数据地图文档)\n\n**关键职责**：\n- 设计前端应用架构\n- 分析应用路由、视图、状态和数据关系\n- 确保架构满足所有功能需求\n\n**关键提示词**：\n- \"你是一位天才产品经理和软件架构师\"\n- \"你的角色是进行分析，为提供的项目详情设计前端应用架构\"\n- \"前端应用涉及哪些旅程？\"\n- \"前端中用户预期看到的功能所需的所有路由、视图、slugs、属性、URL参数、身份验证限制是什么？\"\n- \"确定有状态变量、操作/动态函数、参数\"\n- \"全局应用状态结构，由所有视图访问\"\n- \"使用snake_case进行任何命名\"\n- \"确保与DB字段名称和提供的规范名称完全一致\"\n\n**输出格式**：Markdown格式的UXDMD文档\n\n### 7. 后端架构专家 (BRD - 后端需求文档)\n\n**关键职责**：\n- 确定后端需求（REST API和/或实时Websockets）\n- 设计用户面向的服务器功能\n- 分析API端点和/或实时事件\n\n**关键提示词**：\n- \"你是一位专业的产品经理、软件架构师、后端、服务器和API设计师\"\n- \"确定是否，是或否，为了实现应用MVP的核心功能，后端需要RESTful API和/或实时Websockets\"\n- \"面向用户的服务器涉及哪些功能，以及前端调用的是什么？\"\n- \"如果需要服务器API，前端中用户预期看到的功能所需的所有路由是什么？\"\n- \"如果需要实时功能，前端中用户预期看到的功能所需的所有事件是什么？\"\n- \"应该只涵盖与数据相关的任务！\"\n- \"使用snake_case进行任何命名\"\n\n**输出格式**：\n- 第一部分：YAML格式的后端需求确认（是否需要REST API和/或Websockets）\n- 第二部分：Markdown格式的BRD文档，详细描述API端点和/或实时事件\n\n## 总结\n\n`@cofounder/api/system/functions/pm/` 目录下的系统提示词展示了一个完整的产品开发流程，从产品需求到前后端架构设计。每个角色都有明确的职责和关注点：\n\n1. **产品经理和产品设计师**：负责整体产品需求和功能定义\n2. **功能需求专家**：细化产品功能，确保面向用户的功能完整\n3. **用户旅程地图专家**：定义用户如何与产品交互的完整旅程\n4. **数据库设计专家**：设计支持产品功能的数据结构\n5. **UX站点地图专家**：规划产品的UI视图和导航结构\n6. **前端架构专家**：设计前端应用架构，包括路由、状态和数据流\n7. **后端架构专家**：确定后端需求并设计API和/或实时功能\n\n这些角色共同协作，确保产品从概念到实现的每个环节都得到全面考虑，最终创建出一个完整、连贯的产品。每个角色的系统提示词都强调全面性、用户中心和MVP优先级，确保产品开发聚焦于最重要的功能。\n", "timestamp": "", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/codeworkspace/cofounder", "path_name": "cofounder/api/system/functions/op/project.js", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}]}]}