{"name": "git-prompt", "path": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "workspace_id": "4c4c9e4f07c159bf8fbf500423298286", "conversations": [{"id": "869c1f73-b60e-4160-9bd2-fba5297a73ff", "name": "", "created_at": "2025-05-07T01:53:17.407Z", "last_interacted_at": "2025-05-07T01:53:17.407Z", "messages": []}, {"id": "1faafd5e-ca8e-47ad-9397-471a6e9fc5ff", "name": "", "created_at": "2025-05-07T01:53:17.434Z", "last_interacted_at": "2025-05-07T02:03:55.495Z", "messages": [{"request_id": "f3596b1e-43b0-4ecf-9804-b29dc7b1a0d0", "request_message": "Provide a short summary of this project", "response_text": "# GitPrompt/Prompt Hub Summary\n\nThis project is an AI prompt sharing platform with two names (GitPrompt in Chinese documentation, Prompt Hub in English) that allows users to create, manage, share, and discover high-quality AI prompts.\n\n## Key Features\n- Create, edit, and organize AI prompts\n- Share prompts with the community or specific users\n- Browse prompts by categories and tags\n- Social features: star, fork, and comment on prompts\n- User profiles with activity tracking\n- Logical deletion of prompts (using state field)\n\n## Tech Stack\n- Frontend: React, TypeScript, Tailwind CSS, shadcn/ui\n- Build Tool: Vite\n- Backend: Supabase (Authentication, Database, Storage)\n- State Management: TanStack Query\n- Routing: React Router\n\nThe platform is designed to be a central repository for effective AI prompts, helping users improve their interactions with AI tools like ChatGPT and Claude while building a collaborative community around prompt engineering.\n", "timestamp": "2025-05-07T01:53:23.939Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 80, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 92, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/ARCHITECTURE.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 8, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/ARCHITECTURE.md", "full_range": {"startLineNumber": 30, "startColumn": 0, "endLineNumber": 55, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/ARCHITECTURE.md", "full_range": {"startLineNumber": 86, "startColumn": 0, "endLineNumber": 109, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/ARCHITECTURE.md", "full_range": {"startLineNumber": 125, "startColumn": 0, "endLineNumber": 144, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/ARCHITECTURE.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 8, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/ARCHITECTURE.md", "full_range": {"startLineNumber": 109, "startColumn": 0, "endLineNumber": 183, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/PRD.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 14, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/PRD.md", "full_range": {"startLineNumber": 99, "startColumn": 0, "endLineNumber": 123, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/PRD.md", "full_range": {"startLineNumber": 139, "startColumn": 0, "endLineNumber": 168, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 8, "startColumn": 0, "endLineNumber": 43, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/PRD.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 36, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/PRD.md", "full_range": {"startLineNumber": 92, "startColumn": 0, "endLineNumber": 139, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "vite.config.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 22, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/ROADMAP.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 49, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/ROADMAP.md", "full_range": {"startLineNumber": 181, "startColumn": 0, "endLineNumber": 216, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Index.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 18, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 39, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/docs/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/docs/database-structure.md", "full_range": {"startLineNumber": 161, "startColumn": 0, "endLineNumber": 196, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/USER_GUIDE.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 21, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "index.html", "full_range": {"startLineNumber": 18, "startColumn": 0, "endLineNumber": 26, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "package.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 12, "endColumn": 0}}]}, {"request_id": "5c232568-e0ac-4ea5-b3a1-f5f9f55d3ad2", "request_message": "现在你能读取 supabase 中有多少张表吗？", "response_text": "", "timestamp": "2025-05-07T02:03:39.366Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/integrations/supabase/types.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 312, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Categories.tsx", "full_range": {"startLineNumber": 49, "startColumn": 0, "endLineNumber": 137, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Profile.tsx", "full_range": {"startLineNumber": 13, "startColumn": 0, "endLineNumber": 57, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "README.md", "full_range": {"startLineNumber": 10, "startColumn": 0, "endLineNumber": 30, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "README.md", "full_range": {"startLineNumber": 74, "startColumn": 0, "endLineNumber": 92, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/components/prompts/PromptList.tsx", "full_range": {"startLineNumber": 17, "startColumn": 0, "endLineNumber": 99, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/hooks/useHomeSearch.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 34, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/integrations/supabase/client.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 12, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/hooks/usePublicPrompts.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 77, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/hooks/useCategoryStats.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 30, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 39, "endColumn": 0}}]}, {"request_id": "b815d7cf-fc44-4768-9728-e4db3e6bfa8f", "request_message": "", "response_text": "", "timestamp": "2025-05-07T02:03:55.497Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/integrations/supabase/types.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 46, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/integrations/supabase/types.ts", "full_range": {"startLineNumber": 76, "startColumn": 0, "endLineNumber": 312, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Categories.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 11, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Categories.tsx", "full_range": {"startLineNumber": 49, "startColumn": 0, "endLineNumber": 137, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "README.md", "full_range": {"startLineNumber": 10, "startColumn": 0, "endLineNumber": 30, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "README.md", "full_range": {"startLineNumber": 74, "startColumn": 0, "endLineNumber": 92, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Profile.tsx", "full_range": {"startLineNumber": 13, "startColumn": 0, "endLineNumber": 57, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/integrations/supabase/client.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 12, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/hooks/useHomeSearch.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 79, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/components/prompts/PromptList.tsx", "full_range": {"startLineNumber": 17, "startColumn": 0, "endLineNumber": 99, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/hooks/usePublicPrompts.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 77, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/hooks/useCategoryStats.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 64, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 79, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/docs/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "docs/zh/database-structure.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 42, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "supabase/config.toml", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 0, "endColumn": 35}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "src/pages/Index.tsx", "full_range": {"startLineNumber": 30, "startColumn": 0, "endLineNumber": 56, "endColumn": 21}}, {"repo_root": "/Users/<USER>/workspace/aliyunworkspace/git-prompt", "path_name": "CHANGELOG.md", "full_range": {"startLineNumber": 46, "startColumn": 0, "endLineNumber": 53, "endColumn": 0}}]}]}, {"id": "556d1750-edaf-4d3f-a0e2-f94f2e44bdee", "name": "", "created_at": "2025-05-07T02:04:05.897Z", "last_interacted_at": "2025-05-07T02:04:05.897Z", "messages": [{"request_id": "f6d008ac-ddd3-41d0-9c3e-49e38c0acaef", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}]}]}