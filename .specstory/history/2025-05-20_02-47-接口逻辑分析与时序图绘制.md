<!-- Generated by SpecStory -->

# 接口逻辑分析与时序图绘制 (2025-05-20 10:47:31)

_**User**_

帮我梳理 @server.py 这个文件的整体接口逻辑，画出 两个核心接口的时序图等

---

_**Assistant**_



---

# server.py 文件接口逻辑分析

## 整体功能

这是一个Flask服务器，用于提取和展示Cursor IDE的聊天数据。它能从Cursor的存储位置(SQLite数据库)中读取聊天历史，并通过Web接口提供给前端访问。

## 主要组件

1. **数据定位**：根据操作系统找到Cursor应用数据存储位置
2. **数据提取**：从SQLite数据库中提取聊天数据
3. **数据格式化**：将原始数据格式化为前端所需格式
4. **RESTful API**：提供HTTP接口访问聊天数据
5. **前端服务**：提供静态文件服务，展示React前端应用

## 核心API接口

服务器提供三个主要API接口：

1. `/api/chats`：获取所有聊天会话
2. `/api/chat/<session_id>`：获取指定ID的聊天会话
3. `/api/chat/<session_id>/export`：将指定聊天会话导出为HTML文件

## 接口时序图

### 1. 获取所有聊天会话 `/api/chats`

```
客户端                   Flask服务器                   SQLite数据库
   |                        |                           |
   |--- GET /api/chats ---->|                           |
   |                        |                           |
   |                        |--- 调用extract_chats() -->|
   |                        |                           |
   |                        |<-- 返回原始聊天数据 ------|
   |                        |                           |
   |                        |--- 循环处理每个聊天 ----->|
   |                        |    format_chat_for_frontend()
   |                        |                           |
   |<-- 返回格式化聊天数据 -|                           |
   |                        |                           |
```

### 2. 导出聊天会话 `/api/chat/<session_id>/export`

```
客户端                   Flask服务器                   SQLite数据库
   |                        |                           |
   |--- GET /api/chat/ID/export -->|                    |
   |                        |                           |
   |                        |--- 调用extract_chats() -->|
   |                        |                           |
   |                        |<-- 返回所有聊天数据 ------|
   |                        |                           |
   |                        |--- 查找指定ID的聊天 ----->|
   |                        |                           |
   |                        |--- format_chat_for_frontend() -->|
   |                        |                           |
   |                        |--- generate_standalone_html() -->|
   |                        |    生成HTML内容           |
   |                        |                           |
   |<-- 返回HTML文件下载 ---|                           |
   |                        |                           |
```

## 数据提取流程

1. **定位存储**：通过`cursor_root()`确定Cursor应用数据位置
2. **提取工作区**：遍历`workspaceStorage`目录找到SQLite数据库
3. **提取全局数据**：从`globalStorage`读取全局聊天数据
4. **组合数据**：将工作区和全局数据结合，创建完整聊天历史
5. **格式化**：将原始数据转换为前端友好格式

每个聊天会话包含项目信息、消息历史、日期时间等元数据，最终以JSON格式提供给前端。

---

