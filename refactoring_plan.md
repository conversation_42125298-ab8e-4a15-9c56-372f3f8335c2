# AI 编码助手聊天历史提取系统重构计划

## 项目概述

将现有的 Cursor 聊天历史提取系统重构为一个模块化、可扩展的系统，能够支持多个 AI 编码助手，包括 Augment Code、Cline、Roocode 和 Windsurf。为了确保不影响现有功能，我们将在新的目录中开发重构后的代码，并在完成后逐步迁移。

## 目标

1. 创建一个可扩展的架构，支持多个 AI 编码助手
2. 实现插件/适配器模式，便于添加新的助手支持
3. 统一数据模型，适应不同助手的聊天格式
4. 更新 API 端点，支持多种助手类型
5. 保持与现有 Cursor 功能的向后兼容性
6. 确保重构过程不影响现有功能

## 高级架构设计

```
+----------------------------------+
|           客户端界面             |
+----------------------------------+
                |
+----------------------------------+
|            API 层                |
|   (统一的聊天历史访问接口)       |
+----------------------------------+
                |
+----------------------------------+
|         适配器管理器             |
|   (协调不同助手的数据提取)       |
+----------------------------------+
        /       |        \
+--------+  +--------+  +--------+
| Cursor |  | Augment|  | 其他   |
| 适配器 |  | 适配器 |  | 适配器 |
+--------+  +--------+  +--------+
    |           |           |
+--------+  +--------+  +--------+
| Cursor |  | Augment|  | 其他   |
| 存储   |  | 存储   |  | 存储   |
+--------+  +--------+  +--------+
```

## 文件结构

为了不影响现有代码，我们将在新的 `backend` 目录中开发重构后的代码：

```
/
├── extract_cursor_chat.py       # 现有代码 (保持不变)
├── cursor_chat_finder.py        # 现有代码 (保持不变)
├── server.py                    # 现有代码 (保持不变)
├── backend/                     # 新的重构代码目录
│   ├── __init__.py
│   ├── adapters/
│   │   ├── __init__.py
│   │   ├── base.py              # 定义适配器接口
│   │   ├── manager.py           # 适配器管理器
│   │   ├── cursor_adapter.py    # Cursor 适配器
│   │   ├── augment_adapter.py   # Augment 适配器
│   │   ├── cline_adapter.py     # Cline 适配器
│   │   ├── roocode_adapter.py   # Roocode 适配器
│   │   └── windsurf_adapter.py  # Windsurf 适配器
│   ├── models/
│   │   ├── __init__.py
│   │   ├── chat_message.py      # 聊天消息模型
│   │   ├── chat_session.py      # 聊天会话模型
│   │   └── project_info.py      # 项目信息模型
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── locator.py           # 存储定位器接口
│   │   ├── reader.py            # 存储读取器接口
│   │   ├── cursor_storage.py    # Cursor 存储实现
│   │   ├── augment_storage.py   # Augment 存储实现
│   │   └── ...                  # 其他助手的存储实现
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes.py            # API 路由
│   │   └── formatters.py        # 数据格式化工具
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── logging.py           # 日志工具
│   │   └── compatibility.py     # 向后兼容性工具
│   ├── server.py                # 新的服务器文件
│   └── config.py                # 配置文件
└── server_multi.py              # 新的入口点，使用重构后的代码
```

## 实现计划

### 阶段 0: 准备工作 [待完成]

#### 0.1 创建新的目录结构 [待完成]

- [ ] 创建 `backend` 主目录
- [ ] 创建子目录 `adapters`, `models`, `storage`, `api`, `utils`
- [ ] 创建必要的 `__init__.py` 文件

#### 0.2 设置开发环境 [待完成]

- [ ] 创建 `requirements.txt` 文件，列出所有依赖
- [ ] 创建 `setup.py` 文件，便于开发模式安装
- [ ] 创建 `README.md` 文件，说明新系统的用法

### 阶段 1: 重构核心组件 [待完成]

#### 1.1 创建基础数据模型 [待完成]

- [ ] 实现 `ChatMessage` 类 (backend/models/chat_message.py)
- [ ] 实现 `ChatSession` 类 (backend/models/chat_session.py)
- [ ] 实现 `ProjectInfo` 类 (backend/models/project_info.py)

#### 1.2 实现适配器接口和管理器 [待完成]

- [ ] 定义 `AssistantAdapter` 接口 (backend/adapters/base.py)
- [ ] 实现 `AdapterManager` 类 (backend/adapters/manager.py)

#### 1.3 实现存储接口 [待完成]

- [ ] 定义 `StorageLocator` 接口 (backend/storage/locator.py)
- [ ] 定义 `StorageReader` 接口 (backend/storage/reader.py)

#### 1.4 重构 Cursor 适配器 [待完成]

- [ ] 实现 `CursorAdapter` 类 (backend/adapters/cursor_adapter.py)
- [ ] 实现 `CursorStorageLocator` 类 (backend/storage/cursor_storage.py)
- [ ] 实现 `CursorStorageReader` 类 (backend/storage/cursor_storage.py)

### 阶段 2: 实现新的适配器 [待完成]

#### 2.1 Augment Code 适配器 [待完成]

- [ ] 研究 Augment Code 存储格式
- [ ] 实现 `AugmentAdapter` 类 (backend/adapters/augment_adapter.py)
- [ ] 实现 `AugmentStorageLocator` 类 (backend/storage/augment_storage.py)
- [ ] 实现 `AugmentStorageReader` 类 (backend/storage/augment_storage.py)

#### 2.2 Cline 适配器 [待完成]

- [ ] 研究 Cline 存储格式
- [ ] 实现 `ClineAdapter` 类 (backend/adapters/cline_adapter.py)
- [ ] 实现 `ClineStorageLocator` 类 (backend/storage/cline_storage.py)
- [ ] 实现 `ClineStorageReader` 类 (backend/storage/cline_storage.py)

#### 2.3 Roocode 适配器 [待完成]

- [ ] 研究 Roocode 存储格式
- [ ] 实现 `RoocodeAdapter` 类 (backend/adapters/roocode_adapter.py)
- [ ] 实现 `RoocodeStorageLocator` 类 (backend/storage/roocode_storage.py)
- [ ] 实现 `RoocodeStorageReader` 类 (backend/storage/roocode_storage.py)

#### 2.4 Windsurf 适配器 [待完成]

- [ ] 研究 Windsurf 存储格式
- [ ] 实现 `WindsurfAdapter` 类 (backend/adapters/windsurf_adapter.py)
- [ ] 实现 `WindsurfStorageLocator` 类 (backend/storage/windsurf_storage.py)
- [ ] 实现 `WindsurfStorageReader` 类 (backend/storage/windsurf_storage.py)

### 阶段 3: 创建新的 API 层 [待完成]

#### 3.1 实现 API 路由 [待完成]

- [ ] 创建 API 路由模块 (backend/api/routes.py)
- [ ] 实现 `/api/assistants` 端点
- [ ] 实现 `/api/chats` 端点
- [ ] 实现 `/api/chat/<session_id>` 端点
- [ ] 实现 `/api/chat/<session_id>/export` 端点

#### 3.2 实现数据格式化工具 [待完成]

- [ ] 创建数据格式化模块 (backend/api/formatters.py)
- [ ] 实现 `format_chat_for_frontend` 函数

#### 3.3 实现向后兼容性工具 [待完成]

- [ ] 创建向后兼容性模块 (backend/utils/compatibility.py)
- [ ] 实现数据模型转换函数

#### 3.4 创建新的服务器入口点 [待完成]

- [ ] 实现 `server_multi.py` 作为新系统的入口点
- [ ] 确保新服务器可以与现有服务器并行运行（使用不同端口）

### 阶段 4: 测试和集成 [待完成]

#### 4.1 单元测试 [待完成]

- [ ] 为数据模型编写测试
- [ ] 为适配器接口编写测试
- [ ] 为 Cursor 适配器编写测试
- [ ] 为新的适配器编写测试

#### 4.2 集成测试 [待完成]

- [ ] 测试完整的数据提取流程
- [ ] 测试 API 端点
- [ ] 测试与前端的集成

#### 4.3 性能测试 [待完成]

- [ ] 测试大量聊天历史的处理性能
- [ ] 测试多个助手同时提取的性能

### 阶段 5: 更新前端 [待完成]

#### 5.1 更新前端组件 [待完成]

- [ ] 添加助手类型选择器
- [ ] 更新聊天列表组件
- [ ] 更新聊天详情组件

#### 5.2 更新前端 API 调用 [待完成]

- [ ] 更新获取助手类型的 API 调用
- [ ] 更新获取聊天列表的 API 调用
- [ ] 更新获取聊天详情的 API 调用

### 阶段 6: 部署和迁移 [待完成]

#### 6.1 准备部署 [待完成]

- [ ] 创建部署文档
- [ ] 准备部署脚本

#### 6.2 渐进式迁移 [待完成]

- [ ] 在现有服务器中添加对新系统的引用
- [ ] 实现功能标志，允许用户选择使用新系统或旧系统
- [ ] 收集用户反馈并进行调整

#### 6.3 完全迁移 [待完成]

- [ ] 将所有用户迁移到新系统
- [ ] 弃用旧系统
- [ ] 清理不再需要的代码

## 详细设计

### 数据模型

#### ChatMessage

```python
@dataclass
class ChatMessage:
    """表示单个聊天消息的通用模型"""
    role: str  # 'user' 或 'assistant'
    content: str  # 消息内容
    timestamp: Optional[datetime.datetime] = None  # 消息时间戳
    metadata: Dict[str, Any] = field(default_factory=dict)  # 助手特定的元数据
```

#### ChatSession

```python
@dataclass
class ChatSession:
    """表示完整聊天会话的通用模型"""
    id: str  # 会话唯一标识符
    assistant_type: str  # 助手类型 (例如 'cursor', 'augment')
    project: ProjectInfo  # 项目信息
    messages: List[ChatMessage]  # 消息列表
    created_at: Optional[datetime.datetime] = None  # 会话创建时间
    updated_at: Optional[datetime.datetime] = None  # 会话最后更新时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 助手特定的元数据

    def to_dict(self) -> Dict[str, Any]:
        """将会话转换为字典格式"""
        return {
            "id": self.id,
            "assistant_type": self.assistant_type,
            "project": self.project.to_dict(),
            "messages": [msg.__dict__ for msg in self.messages],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "metadata": self.metadata
        }
```

#### ProjectInfo

```python
@dataclass
class ProjectInfo:
    """表示项目信息的通用模型"""
    name: str  # 项目名称
    root_path: str  # 项目根路径
    workspace_id: Optional[str] = None  # 工作区 ID
    metadata: Dict[str, Any] = field(default_factory=dict)  # 项目特定的元数据

    def to_dict(self) -> Dict[str, Any]:
        """将项目信息转换为字典格式"""
        return {
            "name": self.name,
            "rootPath": self.root_path,
            "workspace_id": self.workspace_id,
            "metadata": self.metadata
        }
```

### 适配器接口

#### AssistantAdapter

```python
class AssistantAdapter(ABC):
    """所有 AI 助手适配器必须实现的接口"""

    @property
    @abstractmethod
    def name(self) -> str:
        """返回助手的唯一标识符"""
        pass

    @property
    @abstractmethod
    def display_name(self) -> str:
        """返回助手的显示名称"""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """返回助手的描述"""
        pass

    @property
    @abstractmethod
    def icon_url(self) -> str:
        """返回助手的图标 URL"""
        pass

    @abstractmethod
    def get_storage_locations(self) -> List[pathlib.Path]:
        """返回此助手存储聊天历史的所有可能位置"""
        pass

    @abstractmethod
    def extract_chat_sessions(self) -> List[ChatSession]:
        """提取并返回此助手的所有聊天会话"""
        pass

    @abstractmethod
    def supports_file(self, file_path: pathlib.Path) -> bool:
        """检查给定文件是否可能包含此助手的聊天数据"""
        pass

    @abstractmethod
    def extract_from_file(self, file_path: pathlib.Path) -> List[ChatSession]:
        """从特定文件提取聊天会话"""
        pass
```

#### AdapterManager

```python
class AdapterManager:
    """管理和协调所有已注册的助手适配器"""

    def __init__(self):
        self.adapters: Dict[str, AssistantAdapter] = {}

    def register_adapter(self, adapter: AssistantAdapter):
        """注册一个新的助手适配器"""
        self.adapters[adapter.name] = adapter

    def get_adapter(self, name: str) -> Optional[AssistantAdapter]:
        """获取指定名称的适配器"""
        return self.adapters.get(name)

    def get_all_adapters(self) -> List[AssistantAdapter]:
        """获取所有已注册的适配器"""
        return list(self.adapters.values())

    def extract_all_chats(self) -> Dict[str, List[ChatSession]]:
        """从所有已注册的适配器提取聊天会话"""
        result = {}
        for name, adapter in self.adapters.items():
            try:
                result[name] = adapter.extract_chat_sessions()
            except Exception as e:
                logger.error(f"Error extracting chats from {name}: {e}")
                result[name] = []
        return result
```

### 存储接口

#### StorageLocator

```python
class StorageLocator(ABC):
    """负责定位不同助手的存储位置"""

    @abstractmethod
    def get_workspace_storage_paths(self) -> List[pathlib.Path]:
        """获取工作区存储路径列表"""
        pass

    @abstractmethod
    def get_global_storage_paths(self) -> List[pathlib.Path]:
        """获取全局存储路径列表"""
        pass

    @abstractmethod
    def get_all_storage_paths(self) -> List[pathlib.Path]:
        """获取所有存储路径列表"""
        pass
```

#### StorageReader

```python
class StorageReader(ABC):
    """负责从存储中读取原始数据"""

    @abstractmethod
    def read_workspace_data(self, workspace_path: pathlib.Path) -> Dict[str, Any]:
        """读取工作区数据"""
        pass

    @abstractmethod
    def read_session_data(self, session_path: pathlib.Path) -> Dict[str, Any]:
        """读取会话数据"""
        pass

    @abstractmethod
    def extract_project_info(self, workspace_data: Dict[str, Any]) -> ProjectInfo:
        """从工作区数据中提取项目信息"""
        pass

    @abstractmethod
    def extract_messages(self, session_data: Dict[str, Any]) -> List[ChatMessage]:
        """从会话数据中提取消息"""
        pass
```

### API 端点

#### 获取所有助手类型

```python
@app.route('/api/assistants', methods=['GET'])
def get_assistants():
    """获取所有已注册的助手类型"""
    adapters = adapter_manager.get_all_adapters()
    return jsonify([{
        "name": adapter.name,
        "display_name": adapter.display_name,
        "description": adapter.description,
        "icon": adapter.icon_url
    } for adapter in adapters])
```

#### 获取所有聊天会话

```python
@app.route('/api/chats', methods=['GET'])
def get_chats():
    """获取所有聊天会话，可选择按助手类型过滤"""
    assistant_type = request.args.get('assistant_type')

    if assistant_type:
        adapter = adapter_manager.get_adapter(assistant_type)
        if not adapter:
            return jsonify({"error": f"Unknown assistant type: {assistant_type}"}), 404
        chats = adapter.extract_chat_sessions()
    else:
        # 获取所有助手的聊天
        all_chats = adapter_manager.extract_all_chats()
        chats = [chat for chat_list in all_chats.values() for chat in chat_list]

    # 格式化聊天数据
    formatted_chats = [format_chat_for_frontend(chat) for chat in chats]

    return jsonify(formatted_chats)
```

#### 获取特定聊天会话

```python
@app.route('/api/chat/<session_id>', methods=['GET'])
def get_chat(session_id):
    """获取特定的聊天会话"""
    assistant_type = request.args.get('assistant_type')

    if assistant_type:
        adapter = adapter_manager.get_adapter(assistant_type)
        if not adapter:
            return jsonify({"error": f"Unknown assistant type: {assistant_type}"}), 404
        chats = adapter.extract_chat_sessions()
    else:
        # 搜索所有助手的聊天
        all_chats = adapter_manager.extract_all_chats()
        chats = [chat for chat_list in all_chats.values() for chat in chat_list]

    # 查找匹配的会话
    for chat in chats:
        if chat.id == session_id:
            return jsonify(format_chat_for_frontend(chat))

    return jsonify({"error": "Chat not found"}), 404
```

#### 导出聊天会话

```python
@app.route('/api/chat/<session_id>/export', methods=['GET'])
def export_chat(session_id):
    """导出特定的聊天会话为独立的 HTML"""
    assistant_type = request.args.get('assistant_type')

    # 查找匹配的会话
    if assistant_type:
        adapter = adapter_manager.get_adapter(assistant_type)
        if not adapter:
            return jsonify({"error": f"Unknown assistant type: {assistant_type}"}), 404
        chats = adapter.extract_chat_sessions()
    else:
        # 搜索所有助手的聊天
        all_chats = adapter_manager.extract_all_chats()
        chats = [chat for chat_list in all_chats.values() for chat in chat_list]

    for chat in chats:
        if chat.id == session_id:
            formatted_chat = format_chat_for_frontend(chat)
            html_content = generate_standalone_html(formatted_chat)

            # 使用助手类型作为文件名前缀
            prefix = chat.assistant_type if chat.assistant_type else "ai"

            return Response(
                html_content,
                mimetype="text/html; charset=utf-8",
                headers={
                    "Content-Disposition": f'attachment; filename="{prefix}-chat-{session_id[:8]}.html"',
                    "Content-Length": str(len(html_content)),
                    "Cache-Control": "no-store",
                },
            )

    return jsonify({"error": "Chat not found"}), 404
```

### 向后兼容性

#### 格式化函数

```python
def format_chat_for_frontend(chat: ChatSession) -> Dict[str, Any]:
    """将新的 ChatSession 格式化为前端期望的格式"""
    # 基本信息
    result = {
        "project": chat.project.to_dict(),
        "messages": [{"role": msg.role, "content": msg.content} for msg in chat.messages],
        "date": int(chat.updated_at.timestamp()) if chat.updated_at else int(datetime.datetime.now().timestamp()),
        "session_id": chat.id,
        "assistant_type": chat.assistant_type,
        "workspace_id": chat.project.workspace_id or "unknown"
    }

    # 添加元数据
    if chat.metadata:
        result.update(chat.metadata)

    return result
```

## 实现示例

### Cursor 适配器示例

```python
class CursorAdapter(AssistantAdapter):
    """Cursor 聊天历史适配器"""

    @property
    def name(self) -> str:
        return "cursor"

    @property
    def display_name(self) -> str:
        return "Cursor"

    @property
    def description(self) -> str:
        return "Cursor AI 编码助手"

    @property
    def icon_url(self) -> str:
        return "/static/icons/cursor.png"

    def get_storage_locations(self) -> List[pathlib.Path]:
        """返回 Cursor 存储聊天历史的所有可能位置"""
        locator = CursorStorageLocator()
        return locator.get_all_storage_paths()

    def extract_chat_sessions(self) -> List[ChatSession]:
        """提取并返回 Cursor 的所有聊天会话"""
        all_sessions = []

        # 获取存储位置
        storage_paths = self.get_storage_locations()

        # 创建存储读取器
        reader = CursorStorageReader()

        # 处理每个工作区
        for workspace_path in storage_paths:
            try:
                # 读取工作区数据
                workspace_data = reader.read_workspace_data(workspace_path)

                # 提取项目信息
                project_info = reader.extract_project_info(workspace_data)

                # 获取关联的会话数据库
                session_paths = self._get_session_paths_for_workspace(workspace_path)

                # 处理每个会话
                for session_path in session_paths:
                    try:
                        # 读取会话数据
                        session_data = reader.read_session_data(session_path)

                        # 提取消息
                        messages = reader.extract_messages(session_data)

                        # 跳过没有消息的会话
                        if not messages:
                            continue

                        # 创建会话 ID
                        session_id = session_path.stem

                        # 获取会话时间戳
                        created_at = datetime.datetime.fromtimestamp(session_path.stat().st_mtime)

                        # 创建聊天会话
                        session = ChatSession(
                            id=session_id,
                            assistant_type=self.name,
                            project=project_info,
                            messages=messages,
                            created_at=created_at,
                            updated_at=created_at,
                            metadata={"db_path": str(session_path)}
                        )

                        all_sessions.append(session)
                    except Exception as e:
                        logger.error(f"Error extracting session from {session_path}: {e}")
            except Exception as e:
                logger.error(f"Error processing workspace {workspace_path}: {e}")

        return all_sessions

    def supports_file(self, file_path: pathlib.Path) -> bool:
        """检查给定文件是否可能包含 Cursor 的聊天数据"""
        # 检查文件扩展名
        if file_path.suffix.lower() in ['.vscdb', '.sqlite', '.db', '.sqlite3']:
            return True
        return False

    def extract_from_file(self, file_path: pathlib.Path) -> List[ChatSession]:
        """从特定文件提取聊天会话"""
        if not self.supports_file(file_path):
            return []

        all_sessions = []
        reader = CursorStorageReader()

        try:
            # 检查文件是工作区数据库还是会话数据库
            if self._is_workspace_db(file_path):
                # 工作区数据库
                workspace_data = reader.read_workspace_data(file_path)
                project_info = reader.extract_project_info(workspace_data)

                # 尝试查找关联的会话数据库
                session_paths = self._get_session_paths_for_workspace(file_path.parent)

                for session_path in session_paths:
                    try:
                        session_data = reader.read_session_data(session_path)
                        messages = reader.extract_messages(session_data)

                        if not messages:
                            continue

                        session_id = session_path.stem
                        created_at = datetime.datetime.fromtimestamp(session_path.stat().st_mtime)

                        session = ChatSession(
                            id=session_id,
                            assistant_type=self.name,
                            project=project_info,
                            messages=messages,
                            created_at=created_at,
                            updated_at=created_at,
                            metadata={"db_path": str(session_path)}
                        )

                        all_sessions.append(session)
                    except Exception as e:
                        logger.error(f"Error extracting session from {session_path}: {e}")
            else:
                # 会话数据库
                session_data = reader.read_session_data(file_path)
                messages = reader.extract_messages(session_data)

                if messages:
                    # 创建一个默认的项目信息
                    project_info = ProjectInfo(
                        name="Unknown Project",
                        root_path="/",
                        workspace_id="unknown"
                    )

                    session_id = file_path.stem
                    created_at = datetime.datetime.fromtimestamp(file_path.stat().st_mtime)

                    session = ChatSession(
                        id=session_id,
                        assistant_type=self.name,
                        project=project_info,
                        messages=messages,
                        created_at=created_at,
                        updated_at=created_at,
                        metadata={"db_path": str(file_path)}
                    )

                    all_sessions.append(session)
        except Exception as e:
            logger.error(f"Error extracting from file {file_path}: {e}")

        return all_sessions

    def _is_workspace_db(self, file_path: pathlib.Path) -> bool:
        """检查文件是否是工作区数据库"""
        return file_path.name == "state.vscdb"

    def _get_session_paths_for_workspace(self, workspace_dir: pathlib.Path) -> List[pathlib.Path]:
        """获取与工作区关联的会话数据库路径"""
        # 获取 Cursor 根目录
        cursor_root = self._get_cursor_root()

        # 可能的会话数据库位置
        possible_locations = [
            cursor_root / "User" / "globalStorage" / "cursor.cursor",
            cursor_root / "User" / "globalStorage" / "cursor",
            cursor_root / "User" / "globalStorage"
        ]

        # 查找所有会话数据库
        session_paths = []
        for location in possible_locations:
            if location.exists():
                # 查找 .sqlite 文件
                session_paths.extend(location.glob("*.sqlite"))

                # 如果没有找到 .sqlite 文件，尝试其他扩展名
                if not session_paths:
                    for ext in [".db", ".sqlite3", ".vscdb"]:
                        session_paths.extend(location.glob(f"*{ext}"))

        return session_paths

    def _get_cursor_root(self) -> pathlib.Path:
        """获取 Cursor 根目录"""
        system = platform.system()
        home = pathlib.Path.home()

        if system == "Darwin":  # macOS
            return home / "Library" / "Application Support" / "Cursor"
        elif system == "Windows":
            return home / "AppData" / "Roaming" / "Cursor"
        elif system == "Linux":
            return home / ".config" / "Cursor"
        else:
            raise RuntimeError(f"Unsupported platform: {system}")
```

### Augment 适配器示例

```python
class AugmentAdapter(AssistantAdapter):
    """Augment Code 聊天历史适配器"""

    @property
    def name(self) -> str:
        return "augment"

    @property
    def display_name(self) -> str:
        return "Augment Code"

    @property
    def description(self) -> str:
        return "Augment Code AI 编码助手"

    @property
    def icon_url(self) -> str:
        return "/static/icons/augment.png"

    def get_storage_locations(self) -> List[pathlib.Path]:
        """返回 Augment Code 存储聊天历史的所有可能位置"""
        locator = AugmentStorageLocator()
        return locator.get_all_storage_paths()

    def extract_chat_sessions(self) -> List[ChatSession]:
        """提取并返回 Augment Code 的所有聊天会话"""
        # 实现 Augment Code 特定的提取逻辑
        # 这里需要根据 Augment Code 的存储格式进行具体实现
        pass

    def supports_file(self, file_path: pathlib.Path) -> bool:
        """检查给定文件是否可能包含 Augment Code 的聊天数据"""
        # 实现 Augment Code 特定的文件检查逻辑
        pass

    def extract_from_file(self, file_path: pathlib.Path) -> List[ChatSession]:
        """从特定文件提取聊天会话"""
        # 实现 Augment Code 特定的文件提取逻辑
        pass
```