<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Markdown 渲染示例</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.2.0/github-markdown.min.css">
    <style>
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }
    </style>
</head>
<body>
    <div id="markdown-container" class="markdown-body"></div>

    <script>
        async function renderMarkdown() {
            // 示例 Markdown 内容
            const markdownContent = `
# GitHub API Markdown 渲染示例

这是一个使用 GitHub API 渲染 Markdown 的示例。

## 特性

- 使用 CDN 引入 github-markdown.css
- 通过 API 获取 Markdown 内容
- 自动渲染为美观的 HTML

\`\`\`javascript
function hello() {
    console.log('Hello, Markdown!');
}
\`\`\`

# How to work with large language models

## How large language models work

[Large language models][Large language models Blog Post] are functions that map text to text. Given an input string of text, a large language model predicts the text that should come next.

The magic of large language models is that by being trained to minimize this prediction error over vast quantities of text, the models end up learning concepts useful for these predictions. For example, they learn:

- how to spell
- how grammar works
- how to paraphrase
- how to answer questions
- how to hold a conversation
- how to write in many languages
- how to code
- etc.

They do this by “reading” a large amount of existing text and learning how words tend to appear in context with other words, and uses what it has learned to predict the next most likely word that might appear in response to a user request, and each subsequent word after that.

GPT-3 and GPT-4 power [many software products][OpenAI Customer Stories], including productivity apps, education apps, games, and more.

## How to control a large language model

Of all the inputs to a large language model, by far the most influential is the text prompt.

Large language models can be prompted to produce output in a few ways:

- **Instruction**: Tell the model what you want
- **Completion**: Induce the model to complete the beginning of what you want
- **Scenario**: Give the model a situation to play out
- **Demonstration**: Show the model what you want, with either:
  - A few examples in the prompt
  - Many hundreds or thousands of examples in a fine-tuning training dataset

An example of each is shown below.

# OpenAI Cookbook\n\nThis project is a collection of practical examples, guides, and notebooks demonstrating how to use OpenAI's APIs and models for various applications. The repository includes:\n\n1. **Vector Database Integrations** - Examples of using OpenAI embeddings with various vector databases (Supabase, Elasticsearch, Redis, Zilliz, Cassandra/AstraDB, etc.)\n\n2. **Voice Solutions** - Implementations using OpenAI's Realtime API for speech applications, including a complete ESP32-based voice agent (ElatoAI)


`;

            // 使用 GitHub API 渲染 Markdown
            try {
                const response = await fetch('https://api.github.com/markdown', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: markdownContent,
                        mode: 'markdown'
                    })
                });

                if (!response.ok) {
                    throw new Error('Markdown 渲染失败');
                }

                const renderedHtml = await response.text();
                document.getElementById('markdown-container').innerHTML = renderedHtml;
            } catch (error) {
                console.error('渲染错误:', error);
                document.getElementById('markdown-container').innerHTML = 
                    `<p>渲染失败: ${error.message}</p>`;
            }
        }

        // 页面加载时渲染 Markdown
        renderMarkdown();
    </script>
</body>
</html>
