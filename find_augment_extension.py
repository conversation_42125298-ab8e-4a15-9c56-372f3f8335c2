#!/usr/bin/env python3
import os
import json
import re
import argparse
import sqlite3
import logging
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def extract_augment_data(augment_dir):
    """Extract chat data from Augment extension directory."""
    conversations = []
    logger = logging.getLogger(__name__)

    # Look for state.vscdb file
    state_db_path = augment_dir.parent / "state.vscdb"
    if not state_db_path.exists():
        logger.warning(f"state.vscdb not found at {state_db_path}")
        return conversations

    try:
        # Connect to the SQLite database
        logger.info(f"Connecting to database: {state_db_path}")
        conn = sqlite3.connect(state_db_path)
        cursor = conn.cursor()

        # Get table names for debugging
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        logger.info(f"Tables in database: {[t[0] for t in tables]}")

        # Query for Augment chat data
        cursor.execute("SELECT key, value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
        result = cursor.fetchone()

        if result:
            # Parse the JSON data
            logger.info("Found Augment chat data in database")
            data = json.loads(result[1])
            webview_state = json.loads(data.get("webviewState", "{}"))
            conversations_data = webview_state.get("conversations", {})

            logger.info(f"Found {len(conversations_data)} conversations")

            # Convert to list of conversations
            for conv_id, conv_data in conversations_data.items():
                if "chatHistory" in conv_data and conv_data["chatHistory"]:
                    # Add conversation ID if not present
                    if "id" not in conv_data:
                        conv_data["id"] = conv_id
                    conversations.append(conv_data)
                    logger.debug(f"Added conversation: {conv_id} with {len(conv_data['chatHistory'])} messages")

        conn.close()
    except Exception as e:
        logger.error(f"Error extracting Augment data: {e}", exc_info=True)

    logger.info(f"Extracted {len(conversations)} conversations")
    return conversations

def sanitize_filename(name):
    """Convert a string to a valid filename."""
    # Replace invalid characters with underscores
    name = re.sub(r'[\\/*?:"<>|]', "_", name)
    # Limit length and ensure it's not empty
    if not name or name.isspace():
        return "unnamed"
    return name[:100]  # Limit length to 100 chars

def export_to_markdown(workspace, conversations, output_dir):
    """Export conversations to Markdown files."""
    if not conversations:
        return

    # Create project directory
    project_name = os.path.basename(workspace['folder'])
    if not project_name or project_name == "Unknown":
        project_name = workspace['hash']

    project_dir = output_dir / sanitize_filename(project_name)
    project_dir.mkdir(exist_ok=True)

    # Process each conversation
    for conversation in conversations:
        # Skip conversations without messages
        if not conversation.get("chatHistory"):
            continue

        # Get conversation name
        conv_name = conversation.get("name", "")
        if not conv_name:
            # Use creation date if name is empty
            created_at = conversation.get("createdAtIso", "")
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                    conv_name = dt.strftime("%Y-%m-%d_%H-%M-%S")
                except:
                    conv_name = "unnamed_conversation"
            else:
                conv_name = "unnamed_conversation"

        # Create markdown file
        md_filename = sanitize_filename(conv_name) + ".md"
        md_path = project_dir / md_filename

        # Group messages by request
        message_groups = []
        current_group = []

        for message in conversation.get("chatHistory", []):
            # 如果是新的用户请求（request_message 不为空），开始一个新组
            if message.get("request_message"):
                if current_group:
                    message_groups.append(current_group)
                current_group = [message]
            # 否则，添加到当前组（AI 响应或续写）
            else:
                current_group.append(message)

        # Add the last group if not empty
        if current_group:
            message_groups.append(current_group)

        # Generate markdown content
        markdown_content = f"# {conv_name}\n\n"
        markdown_content += f"**项目**: {project_name}\n\n"

        # 格式化创建时间
        created_at = conversation.get('createdAtIso', '')
        formatted_created_at = "Unknown"
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                formatted_created_at = dt.strftime("%Y年%m月%d日 %H:%M")
            except:
                formatted_created_at = created_at

        # 格式化最后交互时间
        last_interaction = conversation.get('lastInteractedAtIso', '')
        formatted_last_interaction = "Unknown"
        if last_interaction:
            try:
                dt = datetime.fromisoformat(last_interaction.replace("Z", "+00:00"))
                formatted_last_interaction = dt.strftime("%Y年%m月%d日 %H:%M")
            except:
                formatted_last_interaction = last_interaction

        markdown_content += f"**创建时间**: {formatted_created_at}\n\n"
        markdown_content += f"**最后交互**: {formatted_last_interaction}\n\n"
        markdown_content += f"**对话ID**: {conversation.get('id', 'Unknown')}\n\n"
        markdown_content += "---\n\n"

        # Process each message group
        for i, group in enumerate(message_groups, 1):
            if not group:
                continue

            markdown_content += f"### 对话 {i}\n\n"

            # 处理用户请求
            user_messages = [msg for msg in group if msg.get("request_message")]
            for msg in user_messages:
                timestamp = msg.get("timestamp", "")
                formatted_time = ""
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                        formatted_time = dt.strftime("%Y年%m月%d日 %H:%M")
                    except:
                        formatted_time = timestamp
                time_str = f" ({formatted_time})" if formatted_time else ""
                markdown_content += f"> **👤 用户**{time_str}\n\n{msg.get('request_message')}\n\n"

            # 处理 AI 响应
            ai_messages = [msg for msg in group if msg.get("response_text")]
            combined_response = ""

            # 只获取第一条 AI 响应的时间戳
            first_timestamp = ""
            if ai_messages:
                first_timestamp = ai_messages[0].get("timestamp", "")
                formatted_time = ""
                if first_timestamp:
                    try:
                        dt = datetime.fromisoformat(first_timestamp.replace("Z", "+00:00"))
                        formatted_time = dt.strftime("%Y年%m月%d日 %H:%M")
                    except:
                        formatted_time = first_timestamp
                time_str = f" ({formatted_time})" if formatted_time else ""

                # 合并所有 AI 响应
                for msg in ai_messages:
                    combined_response += msg.get("response_text", "") + "\n\n"

                # 只显示一次 Augment AI 标题
                markdown_content += f"> **🤖 Augment**{time_str}\n\n{combined_response}"

            markdown_content += "---\n\n"

        # Write to file
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        print(f"Exported: {md_path}")

def find_augment_workspaces(storage_path, format=None):
    """Find all workspaces that contain the Augment.vscode-augment directory."""
    logger = logging.getLogger(__name__)
    storage_dir = Path(storage_path)

    if not storage_dir.exists() or not storage_dir.is_dir():
        logger.error(f"Directory {storage_path} does not exist or is not a directory")
        return

    logger.info(f"Searching for Augment workspaces in: {storage_path}")
    augment_workspaces = []

    for item in storage_dir.iterdir():
        if not item.is_dir() or item.name == 'ext-dev':
            continue

        augment_dir = item.joinpath('Augment.vscode-augment')

        if augment_dir.exists() and augment_dir.is_dir():
            logger.info(f"Found Augment directory: {augment_dir}")
            workspace_json = item.joinpath('workspace.json')
            folder_path = "Unknown"

            if workspace_json.exists():
                try:
                    with open(workspace_json, 'r') as f:
                        data = json.load(f)

                    folder = data.get('folder', '')
                    if folder:
                        # Remove the file:// prefix
                        if folder.startswith('file://'):
                            folder = folder[7:]
                        folder_path = folder
                        logger.info(f"Workspace folder: {folder_path}")
                except Exception as e:
                    logger.error(f"Error reading {workspace_json}: {e}", exc_info=True)

            # Add to list
            augment_workspaces.append({
                'hash': item.name,
                'folder': folder_path,
                'augment_path': str(augment_dir)
            })
            logger.info(f"Added workspace: {item.name} - {folder_path}")

    # Sort by folder path
    augment_workspaces.sort(key=lambda x: x['folder'])
    logger.info(f"Found {len(augment_workspaces)} Augment workspaces")

    # Export to Markdown if requested
    if format == "md":
        # Create output directory
        import builtins
        output_dir = Path(builtins.cmd_args.output) if hasattr(builtins, 'cmd_args') and builtins.cmd_args.output else Path("md")
        output_dir.mkdir(exist_ok=True)
        logger.info(f"Exporting to Markdown in {output_dir}...")

        # Process each workspace
        for workspace in augment_workspaces:
            augment_dir = Path(workspace['augment_path'])
            conversations = extract_augment_data(augment_dir)
            if conversations:
                export_to_markdown(workspace, conversations, output_dir)
                logger.info(f"Exported {len(conversations)} conversations for {workspace['folder']}")
            else:
                logger.warning(f"No conversations found for {workspace['folder']}")

    # Print the results
    if augment_workspaces:
        logger.info(f"Found {len(augment_workspaces)} workspaces with Augment.vscode-augment directory")
        print(f"\nFound {len(augment_workspaces)} workspaces with Augment.vscode-augment directory:\n")
        print("HASH                                 | PROJECT PATH")
        print("-" * 80)
        for workspace in augment_workspaces:
            print(f"{workspace['hash']} | {workspace['folder']}")

        # Print details of the Augment extension directories
        print("\nDetails of Augment.vscode-augment directories:")
        print("-" * 80)
        for workspace in augment_workspaces:
            print(f"\n{workspace['hash']} | {workspace['folder']}")
            augment_dir = Path(workspace['augment_path'])
            try:
                for item in augment_dir.iterdir():
                    if item.is_dir():
                        print(f"  📁 {item.name}")
                    else:
                        print(f"  📄 {item.name}")
            except Exception as e:
                logger.error(f"Error listing directory contents: {e}", exc_info=True)
                print(f"  Error listing directory contents: {e}")
    else:
        logger.warning("No workspaces with Augment.vscode-augment directory found")
        print("No workspaces with Augment.vscode-augment directory found.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Find and analyze Augment Code extension data in VS Code workspaces")
    parser.add_argument("--storage", "-s", default="/Users/<USER>/Library/Application Support/Code/User/workspaceStorage",
                        help="Path to VS Code workspaceStorage directory")
    parser.add_argument("--format", "-f", choices=["md"], help="Export format (md for Markdown)")
    parser.add_argument("--debug", "-d", action="store_true", help="Enable debug logging")
    parser.add_argument("--output", "-o", help="Output directory for exported files (default: ./md)")

    args = parser.parse_args()

    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logging.info("Debug logging enabled")

    # 设置输出目录
    if args.output:
        output_dir = Path(args.output)
        output_dir.mkdir(exist_ok=True)
        logging.info(f"Output directory set to: {output_dir}")

    # 将命令行参数保存到全局变量
    import builtins
    builtins.cmd_args = args

    find_augment_workspaces(args.storage, format=args.format)
