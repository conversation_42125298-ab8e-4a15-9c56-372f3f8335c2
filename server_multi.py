#!/usr/bin/env python3
"""
多 AI 助手聊天历史提取系统服务器。

这个脚本启动一个 Flask 服务器，提供多 AI 助手聊天历史的 API 接口。
"""

import argparse
import logging
import datetime
import os
from pathlib import Path
from flask import Flask, jsonify, request, Response, send_from_directory
from flask_cors import CORS

from backend.adapters.manager import AdapterManager
from backend.adapters.cursor_adapter import CursorAdapter
from backend.utils.compatibility import format_chat_for_frontend

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__, static_folder='frontend/build')
CORS(app)

# 创建适配器管理器
adapter_manager = AdapterManager()

# 注册适配器
adapter_manager.register_adapter(CursorAdapter())

# API 路由
@app.route('/api/assistants', methods=['GET'])
def get_assistants():
    """获取所有已注册的助手类型。"""
    adapters = adapter_manager.get_all_adapters()
    return jsonify([{
        "name": adapter.name,
        "display_name": adapter.display_name,
        "description": adapter.description,
        "icon": adapter.icon_url
    } for adapter in adapters])

@app.route('/api/chats', methods=['GET'])
def get_chats():
    """获取所有聊天会话，可选择按助手类型过滤。"""
    try:
        logger.info(f"Received request for chats from {request.remote_addr}")
        assistant_type = request.args.get('assistant_type')

        if assistant_type:
            adapter = adapter_manager.get_adapter(assistant_type)
            if not adapter:
                return jsonify({"error": f"Unknown assistant type: {assistant_type}"}), 404
            chats = adapter.extract_chat_sessions()
        else:
            # 获取所有助手的聊天
            all_chats = adapter_manager.extract_all_chats()
            chats = []
            for adapter_name, chat_list in all_chats.items():
                logger.info(f"Processing {len(chat_list)} chats from adapter: {adapter_name}")
                chats.extend(chat_list)

        logger.info(f"Retrieved {len(chats)} total chats before deduplication")

        # 使用字典来去除重复的会话，按ID分组
        unique_chats = {}
        for chat in chats:
            # 只保留每个会话ID的第一个实例
            if chat.id not in unique_chats:
                unique_chats[chat.id] = chat
            elif len(chat.messages) > len(unique_chats[chat.id].messages):
                # 如果有更多消息的相同ID会话，更新它
                unique_chats[chat.id] = chat

        # 将字典转回列表
        chats = list(unique_chats.values())
        logger.info(f"After deduplication: {len(chats)} unique chats")

        # 格式化聊天数据，使用兼容性函数
        formatted_chats = []
        for chat in chats:
            try:
                formatted_chat = format_chat_for_frontend(chat)
                formatted_chats.append(formatted_chat)
            except Exception as e:
                logger.error(f"Error formatting individual chat: {e}")
                # 跳过无法格式化的聊天
                continue

        # 按日期排序，最新的在前面 (与原始实现一致)
        formatted_chats.sort(key=lambda c: c.get('date', 0), reverse=True)

        logger.info(f"Returning {len(formatted_chats)} formatted chats")
        return jsonify(formatted_chats)
    except Exception as e:
        logger.error(f"Error in get_chats: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

@app.route('/api/chat/<session_id>', methods=['GET'])
def get_chat(session_id):
    """获取特定的聊天会话。"""
    try:
        logger.info(f"Received request for chat {session_id} from {request.remote_addr}")
        assistant_type = request.args.get('assistant_type')

        if assistant_type:
            adapter = adapter_manager.get_adapter(assistant_type)
            if not adapter:
                return jsonify({"error": f"Unknown assistant type: {assistant_type}"}), 404
            chats = adapter.extract_chat_sessions()
        else:
            # 搜索所有助手的聊天
            all_chats = adapter_manager.extract_all_chats()
            chats = [chat for chat_list in all_chats.values() for chat in chat_list]

        # 查找匹配的会话
        # 使用字典来去除重复的会话
        unique_chats = {}
        for chat in chats:
            # 只保留每个会话ID的第一个实例
            if chat.id not in unique_chats:
                unique_chats[chat.id] = chat

            # 也检查 composerId 是否匹配
            if hasattr(chat, 'metadata') and chat.metadata:
                for msg in chat.messages:
                    if msg.metadata and 'composer_id' in msg.metadata and msg.metadata['composer_id'] == session_id:
                        unique_chats[session_id] = chat
                        break

        # 查找匹配的会话
        if session_id in unique_chats:
            formatted_chat = format_chat_for_frontend(unique_chats[session_id])
            return jsonify(formatted_chat)

        logger.warning(f"Chat with ID {session_id} not found")
        return jsonify({"error": "Chat not found"}), 404
    except Exception as e:
        logger.error(f"Error in get_chat: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

def generate_standalone_html(chat):
    """生成聊天会话的独立 HTML 表示。

    Args:
        chat: 格式化后的聊天会话

    Returns:
        str: HTML 内容
    """
    logger.info(f"Generating HTML for session ID: {chat.get('session_id', 'N/A')}")
    try:
        # 格式化日期显示
        date_display = "Unknown date"
        if chat.get('date'):
            try:
                date_obj = datetime.datetime.fromtimestamp(chat['date'])
                date_display = date_obj.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                logger.warning(f"Error formatting date: {e}")

        # 获取项目信息
        project_name = chat.get('project', {}).get('name', 'Unknown Project')
        project_path = chat.get('project', {}).get('rootPath', 'Unknown Path')
        logger.info(f"Project: {project_name}, Path: {project_path}, Date: {date_display}")

        # 构建 HTML 内容
        messages_html = ""
        messages = chat.get('messages', [])
        logger.info(f"Found {len(messages)} messages for the chat.")

        if not messages:
            logger.warning("No messages found in the chat object to generate HTML.")
            messages_html = "<p>No messages found in this conversation.</p>"
        else:
            for i, msg in enumerate(messages):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')

                if not content or not isinstance(content, str):
                    logger.warning(f"Message {i+1} has invalid content: {content}")
                    content = "Content unavailable"

                # 简单的 HTML 转义
                escaped_content = content.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

                # 转换 markdown 代码块
                processed_content = ""
                in_code_block = False
                for line in escaped_content.split('\n'):
                    if line.strip().startswith("```"):
                        if not in_code_block:
                            processed_content += "<pre><code>"
                            in_code_block = True
                            # 移除第一个 ``` 标记
                            line = line.strip()[3:]
                        else:
                            processed_content += "</code></pre>\n"
                            in_code_block = False
                            line = ""  # 跳过结束的 ``` 行

                    if in_code_block:
                        # 在代码块内，保留空格并添加换行符
                        processed_content += line + "\n"
                    else:
                        # 在代码块外，使用 <br> 换行
                        processed_content += line + "<br>"

                # 关闭末尾未关闭的代码块
                if in_code_block:
                    processed_content += "</code></pre>"

                # 根据助手类型设置不同的名称
                assistant_type = chat.get('assistant_type', 'cursor')
                assistant_name = "Cursor Assistant"
                if assistant_type == "augment":
                    assistant_name = "Augment Code Assistant"
                elif assistant_type == "cline":
                    assistant_name = "Cline Assistant"
                elif assistant_type == "roocode":
                    assistant_name = "Roocode Assistant"
                elif assistant_type == "windsurf":
                    assistant_name = "Windsurf Assistant"

                avatar = "👤" if role == "user" else "🤖"
                name = "You" if role == "user" else assistant_name
                bg_color = "#f0f7ff" if role == "user" else "#f0fff7"
                border_color = "#3f51b5" if role == "user" else "#00796b"

                messages_html += f"""
                <div class="message" style="margin-bottom: 20px;">
                    <div class="message-header" style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div class="avatar" style="width: 32px; height: 32px; border-radius: 50%; background-color: {border_color}; color: white; display: flex; justify-content: center; align-items: center; margin-right: 10px;">
                            {avatar}
                        </div>
                        <div class="sender" style="font-weight: bold;">{name}</div>
                    </div>
                    <div class="message-content" style="padding: 15px; border-radius: 8px; background-color: {bg_color}; border-left: 4px solid {border_color}; margin-left: {0 if role == 'user' else '40px'}; margin-right: {0 if role == 'assistant' else '40px'};">
                        {processed_content}
                    </div>
                </div>
                """

        # 创建完整的 HTML 文档
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat - {project_name}</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 900px; margin: 20px auto; padding: 20px; border: 1px solid #eee; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        h1, h2, h3 {{ color: #2c3e50; }}
        .header {{ background: linear-gradient(90deg, #f0f7ff 0%, #f0fff7 100%); color: white; padding: 15px 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }}
        .chat-info {{ display: flex; flex-wrap: wrap; gap: 10px 20px; margin-bottom: 20px; background-color: #f9f9f9; padding: 12px 15px; border-radius: 8px; font-size: 0.9em; }}
        .info-item {{ display: flex; align-items: center; }}
        .info-label {{ font-weight: bold; margin-right: 5px; color: #555; }}
        pre {{ background-color: #eef; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #ddd; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; white-space: pre-wrap; word-wrap: break-word; }}
        code {{ background-color: transparent; padding: 0; border-radius: 0; font-family: inherit; }}
        .message-content pre code {{ background-color: transparent; }}
        .message-content {{ word-wrap: break-word; overflow-wrap: break-word; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AI Chat: {project_name}</h1>
    </div>
    <div class="chat-info">
        <div class="info-item"><span class="info-label">Project:</span> <span>{project_name}</span></div>
        <div class="info-item"><span class="info-label">Path:</span> <span>{project_path}</span></div>
        <div class="info-item"><span class="info-label">Date:</span> <span>{date_display}</span></div>
        <div class="info-item"><span class="info-label">Session ID:</span> <span>{chat.get('session_id', 'Unknown')}</span></div>
        <div class="info-item"><span class="info-label">Assistant:</span> <span>{chat.get('assistant_type', 'Unknown').capitalize()}</span></div>
    </div>
    <h2>Conversation History</h2>
    <div class="messages">
{messages_html}
    </div>
    <div style="margin-top: 30px; font-size: 12px; color: #999; text-align: center; border-top: 1px solid #eee; padding-top: 15px;">
        <a href="https://github.com/saharmor/cursor-view" target="_blank" rel="noopener noreferrer">Exported from Multi-Assistant Chat View</a>
    </div>
</body>
</html>"""

        logger.info(f"Finished generating HTML. Total length: {len(html)}")
        return html
    except Exception as e:
        logger.error(f"Error generating HTML for session {chat.get('session_id', 'N/A')}: {e}", exc_info=True)
        # 返回 HTML 格式的错误消息
        return f"<html><body><h1>Error generating chat export</h1><p>Error: {e}</p></body></html>"

@app.route('/api/chat/<session_id>/export', methods=['GET'])
def export_chat(session_id):
    """导出特定的聊天会话为独立的 HTML。"""
    try:
        logger.info(f"Received request to export chat {session_id} from {request.remote_addr}")
        assistant_type = request.args.get('assistant_type')

        if assistant_type:
            adapter = adapter_manager.get_adapter(assistant_type)
            if not adapter:
                return jsonify({"error": f"Unknown assistant type: {assistant_type}"}), 404
            chats = adapter.extract_chat_sessions()
        else:
            # 搜索所有助手的聊天
            all_chats = adapter_manager.extract_all_chats()
            chats = [chat for chat_list in all_chats.values() for chat in chat_list]

        # 查找匹配的会话
        for chat in chats:
            if chat.id == session_id:
                formatted_chat = format_chat_for_frontend(chat)
                html_content = generate_standalone_html(formatted_chat)

                # 使用助手类型作为文件名前缀
                prefix = chat.assistant_type if chat.assistant_type else "ai"

                return Response(
                    html_content,
                    mimetype="text/html; charset=utf-8",
                    headers={
                        "Content-Disposition": f'attachment; filename="{prefix}-chat-{session_id[:8]}.html"',
                        "Content-Length": str(len(html_content)),
                        "Cache-Control": "no-store",
                    },
                )

        logger.warning(f"Chat with ID {session_id} not found for export")
        return jsonify({"error": "Chat not found"}), 404
    except Exception as e:
        logger.error(f"Error in export_chat: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve_react(path):
    """提供 React 应用。"""
    if path and Path(app.static_folder, path).exists():
        return send_from_directory(app.static_folder, path)
    return send_from_directory(app.static_folder, 'index.html')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Run the Multi-Assistant Chat History server')
    parser.add_argument('--port', type=int, default=5001, help='Port to run the server on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    args = parser.parse_args()

    logger.info(f"Starting server on port {args.port}")
    app.run(debug=args.debug, port=args.port)
