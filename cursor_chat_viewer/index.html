<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor View</title>
    <style>
        :root {
            --bg-color: #1e1e1e;
            --text-color: #d4d4d4;
            --card-bg: #252526;
            --card-hover: #333333;
            --accent-color: #6E2CF4;
            --header-bg: #252526;
            --border-color: #444444;
            --card-shadow: 0 4px 6px rgba(12, 188, 255, 0.3);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background-color: var(--header-bg);
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .toggle-theme {
            background-color: transparent;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .workspace-title {
            font-size: 20px;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .conversation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .conversation-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 15px;
            box-shadow: var(--card-shadow);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
            height: 200px;
            display: flex;
            flex-direction: column;
        }
        
        .conversation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 10px rgba(12, 188, 255, 0.4);
            background-color: var(--card-hover);
        }
        
        .card-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--accent-color);
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .card-preview {
            font-size: 14px;
            color: var(--text-color);
            opacity: 0.9;
            flex-grow: 1;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 6;
            -webkit-box-orient: vertical;
        }
        
        .card-footer {
            margin-top: 10px;
            font-size: 12px;
            color: var(--text-color);
            opacity: 0.7;
            text-align: right;
        }
        
        .no-conversations {
            padding: 20px;
            text-align: center;
            background-color: var(--card-bg);
            border-radius: 8px;
            margin-bottom: 40px;
        }
        
        /* Light theme */
        body.light-theme {
            --bg-color: #f5f5f5;
            --text-color: #333333;
            --card-bg: #ffffff;
            --card-hover: #f0f0f0;
            --accent-color: #6E2CF4;
            --header-bg: #e0e0e0;
            --border-color: #dddddd;
            --card-shadow: 0 2px 5px rgba(12, 188, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Cursor View</h1>
            <button class="toggle-theme" onclick="toggleTheme()">Toggle Light/Dark</button>
        </div>
        
        <div class="workspace-title">Workspace: ea4da77c5e01c7da80db58aa84beae7c</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: dad94eff5e55a90da64e8a9b968c03d3</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: 88b37f1c1e20acaa9797382d4d2a123e</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: ff6e5790f423c7a83ad7528754aa8421</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: 2979e6fd3152b9d37864ce125bf9096c</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: e6075cfac54e62c8f00ab252e2507a10</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: 0ffdfbc3b86de59f0da4e0054e8243fd</div><div class="no-conversations">No conversations found in this workspace.</div><div class="workspace-title">Workspace: 04308cbb1ebcf23ca4b69b62d6d41d7e</div><div class="no-conversations">No conversations found in this workspace.</div>
    </div>

    <script>
        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            // Save the theme preference
            const isDark = !document.body.classList.contains('light-theme');
            localStorage.setItem('darkTheme', isDark);
        }
        
        // Apply saved theme preference
        document.addEventListener('DOMContentLoaded', function() {
            const darkTheme = localStorage.getItem('darkTheme') === 'true';
            if (!darkTheme) {
                document.body.classList.add('light-theme');
            }
        });
    </script>
</body>
</html>
