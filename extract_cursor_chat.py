
#!/usr/bin/env python3
"""
Extract Cursor chat history from both workspaceStorage *and* session‑specific
SQLite databases.

Expected inputs
--------------
1. `state.vscdb`   – lives in `workspaceStorage/***/state.vscdb`
   • Has ItemTable with `history.entries` (for project root, open files)
2. `state.sqlite` or similar  – lives in the extension folder
   • Has cursorDiskKV with per‑bubble chat data (`bubbleId:*` keys)

What we build
-------------
`ChatSession` dataclass with:

    project   – {'name': 'CommunityVibe', 'rootPath': '...'}
    messages  – [{'role': 'user'|'assistant', 'content': str}]

CLI usage
---------
python extract_cursor_chat.py --workspace state.vscdb --session state.sqlite --out chat.json
"""

from __future__ import annotations

import argparse
import json
import os
import pathlib
import sqlite3
import sys
import logging
from dataclasses import dataclass
from typing import Dict, List, Any, Iterable, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Enable detailed logging for debugging Cursor storage locations
DETAILED_LOGGING = True


# ------------------------------------------------------------
# Shared helpers
# ------------------------------------------------------------
def _load_json(cur: sqlite3.Cursor, table: str, key: str):
    cur.execute(f"SELECT value FROM {table} WHERE key=?", (key,))
    row = cur.fetchone()
    if not row:
        return None
    try:
        return json.loads(row[0])
    except Exception:
        return None


# ------------------------------------------------------------
# Project metadata (from workspace DB)
# ------------------------------------------------------------
def extract_project(workspace_db: pathlib.Path) -> Dict[str, str]:
    con = sqlite3.connect(workspace_db)
    cur = con.cursor()
    entries = _load_json(cur, "ItemTable", "history.entries") or []
    con.close()

    file_paths: List[str] = []
    for entry in entries:
        res = entry.get("editor", {}).get("resource", "")
        if res.startswith("file:///"):
            file_paths.append(res[len("file://"):])

    if not file_paths:
        return {}

    root = os.path.commonprefix(file_paths).rstrip("/")
    if "/" in root:
        root = root[: root.rfind("/")]

    return {"name": os.path.basename(root), "rootPath": "/" + root}


# ------------------------------------------------------------
# Messages from session DB (cursorDiskKV)
# ------------------------------------------------------------
def _iter_bubble_messages(session_db: pathlib.Path) -> Iterable[Tuple[int, Dict[str, str]]]:
    """Yield (rowid, msg_dict) for every bubble with non‑empty text."""
    if DETAILED_LOGGING:
        logger.info(f"Extracting bubble messages from: {session_db}")

    try:
        con = sqlite3.connect(session_db)
        cur = con.cursor()

        # Check if cursorDiskKV table exists
        cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
        if not cur.fetchone():
            if DETAILED_LOGGING:
                logger.warning(f"No cursorDiskKV table found in {session_db}")
            con.close()
            return

        # Count total bubbles
        if DETAILED_LOGGING:
            try:
                cur.execute("SELECT COUNT(*) FROM cursorDiskKV WHERE key LIKE 'bubbleId:%'")
                count = cur.fetchone()[0]
                logger.info(f"Found {count} bubbleId entries in database")
            except Exception as e:
                logger.error(f"Error counting bubbles: {e}")

        # Get all bubbles
        cur.execute("SELECT rowid, key, value FROM cursorDiskKV WHERE key LIKE 'bubbleId:%'")
        results = cur.fetchall()

        if DETAILED_LOGGING:
            logger.info(f"Processing {len(results)} bubble entries")

        message_count = 0
        for rowid, key, val in results:
            try:
                if DETAILED_LOGGING and message_count == 0:
                    logger.info(f"Sample key format: {key}")

                bubble = json.loads(val)

                # Log first bubble structure for debugging
                if DETAILED_LOGGING and message_count == 0:
                    logger.info(f"Sample bubble structure: {list(bubble.keys())}")
            except Exception as e:
                if DETAILED_LOGGING:
                    logger.warning(f"Failed to parse bubble JSON for key {key}: {e}")
                continue

            text = bubble.get("text", "").strip()
            if not text:
                continue

            role = "user" if bubble.get("type") == 1 else "assistant"

            if DETAILED_LOGGING and message_count < 2:  # Log only first two messages
                logger.info(f"Message {message_count+1}: role={role}, text_length={len(text)}")

            message_count += 1
            yield rowid, {"role": role, "content": text}

        if DETAILED_LOGGING:
            logger.info(f"Extracted {message_count} valid messages from {session_db}")

    except sqlite3.Error as e:
        if DETAILED_LOGGING:
            logger.error(f"SQLite error in {session_db}: {e}")
    except Exception as e:
        if DETAILED_LOGGING:
            logger.error(f"Unexpected error processing {session_db}: {e}")
    finally:
        if 'con' in locals():
            con.close()


def extract_messages(session_db: pathlib.Path) -> List[Dict[str, str]]:
    """Extract and sort messages from session database."""
    if DETAILED_LOGGING:
        logger.info(f"Extracting messages from session DB: {session_db}")

    # Sort by rowid (= insertion order)
    msgs = [msg for _, msg in sorted(_iter_bubble_messages(session_db), key=lambda t: t[0])]

    if DETAILED_LOGGING:
        logger.info(f"Total messages extracted and sorted: {len(msgs)}")
        if msgs:
            # Log distribution of user vs assistant messages
            user_msgs = sum(1 for msg in msgs if msg["role"] == "user")
            assistant_msgs = sum(1 for msg in msgs if msg["role"] == "assistant")
            logger.info(f"Message distribution - User: {user_msgs}, Assistant: {assistant_msgs}")

    return msgs


# ------------------------------------------------------------
# Dataclass wrapper
# ------------------------------------------------------------
@dataclass
class ChatSession:
    project: Dict[str, str]
    messages: List[Dict[str, str]]

    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary format."""
        return {"project": self.project, "messages": self.messages}


def load_chat_session(workspace_db: pathlib.Path, session_db: pathlib.Path) -> "ChatSession":
    """Load a complete chat session from workspace and session databases."""
    if DETAILED_LOGGING:
        logger.info(f"Loading chat session - Workspace DB: {workspace_db}, Session DB: {session_db}")

    # Extract project information from workspace database
    project = extract_project(workspace_db)

    if DETAILED_LOGGING:
        if project:
            logger.info(f"Project information extracted - Name: {project.get('name', 'Unknown')}, Path: {project.get('rootPath', 'Unknown')}")
        else:
            logger.warning("No project information found in workspace database")

    # Extract messages from session database
    messages = extract_messages(session_db)

    # Create and return the chat session
    session = ChatSession(project, messages)

    if DETAILED_LOGGING:
        logger.info(f"Chat session loaded - Project: {project.get('name', 'Unknown')}, Messages: {len(messages)}")

    return session
