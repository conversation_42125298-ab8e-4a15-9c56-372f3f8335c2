"""
项目信息模型模块。

这个模块定义了表示项目信息的数据模型。
"""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import Dict, Any, Optional


@dataclass
class ProjectInfo:
    """表示项目信息的通用模型。
    
    属性:
        name: 项目名称
        root_path: 项目根路径
        workspace_id: 工作区 ID，可选
        metadata: 项目特定的元数据，可选
    """
    name: str  # 项目名称
    root_path: str  # 项目根路径
    workspace_id: Optional[str] = None  # 工作区 ID
    metadata: Dict[str, Any] = field(default_factory=dict)  # 项目特定的元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """将项目信息转换为字典格式。
        
        Returns:
            Dict[str, Any]: 项目信息的字典表示
        """
        result = {
            "name": self.name,
            "rootPath": self.root_path
        }
        
        if self.workspace_id:
            result["workspace_id"] = self.workspace_id
            
        if self.metadata:
            result["metadata"] = self.metadata
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> ProjectInfo:
        """从字典创建项目信息对象。
        
        Args:
            data: 包含项目信息数据的字典
            
        Returns:
            ProjectInfo: 创建的项目信息对象
        """
        return cls(
            name=data.get("name", "Unknown Project"),
            root_path=data.get("rootPath", "/"),
            workspace_id=data.get("workspace_id"),
            metadata=data.get("metadata", {})
        )
