"""
聊天消息模型模块。

这个模块定义了表示单个聊天消息的数据模型。
"""

from __future__ import annotations

import datetime
from dataclasses import dataclass, field
from typing import Dict, Any, Optional


@dataclass
class ChatMessage:
    """表示单个聊天消息的通用模型。
    
    属性:
        role: 消息发送者的角色，'user' 或 'assistant'
        content: 消息内容
        timestamp: 消息时间戳，可选
        metadata: 助手特定的元数据，可选
    """
    role: str  # 'user' 或 'assistant'
    content: str  # 消息内容
    timestamp: Optional[datetime.datetime] = None  # 消息时间戳
    metadata: Dict[str, Any] = field(default_factory=dict)  # 助手特定的元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """将消息转换为字典格式。
        
        Returns:
            Dict[str, Any]: 消息的字典表示
        """
        result = {
            "role": self.role,
            "content": self.content
        }
        
        if self.timestamp:
            result["timestamp"] = self.timestamp.isoformat()
            
        if self.metadata:
            result["metadata"] = self.metadata
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> ChatMessage:
        """从字典创建消息对象。
        
        Args:
            data: 包含消息数据的字典
            
        Returns:
            ChatMessage: 创建的消息对象
        """
        timestamp = None
        if "timestamp" in data and data["timestamp"]:
            try:
                timestamp = datetime.datetime.fromisoformat(data["timestamp"])
            except (ValueError, TypeError):
                pass
                
        return cls(
            role=data["role"],
            content=data["content"],
            timestamp=timestamp,
            metadata=data.get("metadata", {})
        )
