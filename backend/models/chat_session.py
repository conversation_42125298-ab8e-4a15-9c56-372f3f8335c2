"""
聊天会话模型模块。

这个模块定义了表示完整聊天会话的数据模型。
"""

from __future__ import annotations

import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional

from .chat_message import ChatMessage
from .project_info import ProjectInfo


@dataclass
class ChatSession:
    """表示完整聊天会话的通用模型。
    
    属性:
        id: 会话唯一标识符
        assistant_type: 助手类型 (例如 'cursor', 'augment')
        project: 项目信息
        messages: 消息列表
        created_at: 会话创建时间，可选
        updated_at: 会话最后更新时间，可选
        metadata: 助手特定的元数据，可选
    """
    id: str  # 会话唯一标识符
    assistant_type: str  # 助手类型 (例如 'cursor', 'augment')
    project: ProjectInfo  # 项目信息
    messages: List[ChatMessage]  # 消息列表
    created_at: Optional[datetime.datetime] = None  # 会话创建时间
    updated_at: Optional[datetime.datetime] = None  # 会话最后更新时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 助手特定的元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """将会话转换为字典格式。
        
        Returns:
            Dict[str, Any]: 会话的字典表示
        """
        return {
            "id": self.id,
            "assistant_type": self.assistant_type,
            "project": self.project.to_dict(),
            "messages": [msg.to_dict() for msg in self.messages],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> ChatSession:
        """从字典创建会话对象。
        
        Args:
            data: 包含会话数据的字典
            
        Returns:
            ChatSession: 创建的会话对象
        """
        # 解析时间戳
        created_at = None
        if "created_at" in data and data["created_at"]:
            try:
                created_at = datetime.datetime.fromisoformat(data["created_at"])
            except (ValueError, TypeError):
                pass
                
        updated_at = None
        if "updated_at" in data and data["updated_at"]:
            try:
                updated_at = datetime.datetime.fromisoformat(data["updated_at"])
            except (ValueError, TypeError):
                pass
        
        # 创建项目信息
        project = ProjectInfo.from_dict(data.get("project", {}))
        
        # 创建消息列表
        messages = [
            ChatMessage.from_dict(msg_data)
            for msg_data in data.get("messages", [])
        ]
        
        return cls(
            id=data.get("id", ""),
            assistant_type=data.get("assistant_type", "unknown"),
            project=project,
            messages=messages,
            created_at=created_at,
            updated_at=updated_at,
            metadata=data.get("metadata", {})
        )
