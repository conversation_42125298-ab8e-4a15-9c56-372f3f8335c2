"""
存储读取器接口模块。

这个模块定义了负责从存储中读取原始数据的接口。
"""

import pathlib
from abc import ABC, abstractmethod
from typing import Dict, List, Any

from ..models.chat_message import ChatMessage
from ..models.project_info import ProjectInfo


class StorageReader(ABC):
    """负责从存储中读取原始数据。"""
    
    @abstractmethod
    def read_workspace_data(self, workspace_path: pathlib.Path) -> Dict[str, Any]:
        """读取工作区数据。
        
        Args:
            workspace_path: 工作区路径
            
        Returns:
            Dict[str, Any]: 工作区数据
        """
        pass
    
    @abstractmethod
    def read_session_data(self, session_path: pathlib.Path) -> Dict[str, Any]:
        """读取会话数据。
        
        Args:
            session_path: 会话路径
            
        Returns:
            Dict[str, Any]: 会话数据
        """
        pass
    
    @abstractmethod
    def extract_project_info(self, workspace_data: Dict[str, Any]) -> ProjectInfo:
        """从工作区数据中提取项目信息。
        
        Args:
            workspace_data: 工作区数据
            
        Returns:
            ProjectInfo: 项目信息
        """
        pass
    
    @abstractmethod
    def extract_messages(self, session_data: Dict[str, Any]) -> List[ChatMessage]:
        """从会话数据中提取消息。
        
        Args:
            session_data: 会话数据
            
        Returns:
            List[ChatMessage]: 消息列表
        """
        pass
