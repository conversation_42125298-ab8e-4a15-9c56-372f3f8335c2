"""
Cursor 存储实现模块。

这个模块实现了 Cursor 特定的存储定位器和读取器。
"""

import json
import logging
import os
import pathlib
import platform
import sqlite3
from typing import Dict, List, Any, Optional, Tuple

from .locator import StorageLocator
from .reader import StorageReader
from ..models.chat_message import ChatMessage
from ..models.project_info import ProjectInfo
from ..utils.project_utils import extract_project_name_from_path

# 配置日志
logger = logging.getLogger(__name__)


class CursorStorageLocator(StorageLocator):
    """Cursor 特定的存储定位器。"""

    def get_cursor_root(self) -> pathlib.Path:
        """获取 Cursor 根目录。

        Returns:
            pathlib.Path: Cursor 根目录
        """
        h = pathlib.Path.home()
        s = platform.system()

        if s == "Darwin":   # macOS
            return h / "Library" / "Application Support" / "Cursor"
        if s == "Windows":  # Windows
            return h / "AppData" / "Roaming" / "Cursor"
        if s == "Linux":    # Linux
            return h / ".config" / "Cursor"

        raise RuntimeError(f"Unsupported OS: {s}")

    def get_workspace_storage_paths(self) -> List[pathlib.Path]:
        """获取工作区存储路径列表。

        Returns:
            List[pathlib.Path]: 工作区存储路径列表
        """
        cursor_root = self.get_cursor_root()
        workspace_storage = cursor_root / "User" / "workspaceStorage"

        if not workspace_storage.exists():
            logger.warning(f"Workspace storage directory does not exist: {workspace_storage}")
            return []

        result = []
        for workspace_dir in workspace_storage.glob("*"):
            workspace_db = workspace_dir / "state.vscdb"
            if workspace_db.exists():
                result.append(workspace_db)
                
                # 新增：检查 backup 文件夹中的备份数据库
                backup_dir = workspace_dir / "backup"
                if backup_dir.exists() and backup_dir.is_dir():
                    for backup_db in backup_dir.glob("*.vscdb"):
                        result.append(backup_db)
            
            # 新增：检查会话目录中的数据库
            session_dir = workspace_dir / "sessions"
            if session_dir.exists() and session_dir.is_dir():
                for session_db in session_dir.glob("*.vscdb"):
                    result.append(session_db)

        return result

    def get_global_storage_paths(self) -> List[pathlib.Path]:
        """获取全局存储路径列表。

        Returns:
            List[pathlib.Path]: 全局存储路径列表
        """
        cursor_root = self.get_cursor_root()

        # 可能的全局存储位置
        possible_locations = [
            cursor_root / "User" / "globalStorage" / "state.vscdb",
            cursor_root / "User" / "globalStorage" / "cursor.cursor",
            cursor_root / "User" / "globalStorage" / "cursor",
            cursor_root / "User" / "globalStorage",
            # 新增位置
            cursor_root / "User" / "state.vscdb",
            cursor_root / "state.vscdb",
            cursor_root / "globalStorage",
            cursor_root / "Storage",
            cursor_root / "User" / "History",
            cursor_root / "logs"
        ]

        result = []
        for location in possible_locations:
            if location.exists():
                if location.is_file() and self._is_sqlite_db(location):
                    result.append(location)
                elif location.is_dir():
                    # 查找 SQLite 文件
                    for ext in [".sqlite", ".db", ".sqlite3", ".vscdb"]:
                        result.extend([f for f in location.glob(f"*{ext}") if self._is_sqlite_db(f)])
                    
                    # 递归查找最多两级子目录中的数据库文件
                    for subdir in location.glob("*"):
                        if subdir.is_dir():
                            for ext in [".sqlite", ".db", ".sqlite3", ".vscdb"]:
                                result.extend([f for f in subdir.glob(f"*{ext}") if self._is_sqlite_db(f)])
                            
                            # 再查找一级子目录
                            for subsubdir in subdir.glob("*"):
                                if subsubdir.is_dir():
                                    for ext in [".sqlite", ".db", ".sqlite3", ".vscdb"]:
                                        result.extend([f for f in subsubdir.glob(f"*{ext}") if self._is_sqlite_db(f)])

        return result
    
    def _is_sqlite_db(self, file_path: pathlib.Path) -> bool:
        """检查文件是否是有效的SQLite数据库。

        Args:
            file_path: 要检查的文件路径

        Returns:
            bool: 如果是有效的SQLite数据库则为True，否则为False
        """
        try:
            # 检查文件大小是否至少为100字节
            if file_path.stat().st_size < 100:
                return False
                
            # 尝试连接并验证是否是SQLite数据库
            con = sqlite3.connect(f"file:{file_path}?mode=ro", uri=True)
            cur = con.cursor()
            
            # 尝试执行一个简单的SQL语句
            cur.execute("PRAGMA integrity_check")
            result = cur.fetchone()
            con.close()
            
            # 如果能执行并得到有效结果，说明是有效的SQLite数据库
            return result is not None
        except Exception as e:
            # 如果发生异常，说明不是有效的SQLite数据库
            logger.debug(f"Not a valid SQLite database: {file_path}, error: {e}")
            return False

    def get_all_storage_paths(self) -> List[pathlib.Path]:
        """获取所有存储路径列表。

        Returns:
            List[pathlib.Path]: 所有存储路径列表
        """
        return self.get_workspace_storage_paths() + self.get_global_storage_paths()


class CursorStorageReader(StorageReader):
    """Cursor 特定的存储读取器。"""

    def get_cursor_root(self) -> pathlib.Path:
        """获取 Cursor 根目录。

        Returns:
            pathlib.Path: Cursor 根目录
        """
        h = pathlib.Path.home()
        s = platform.system()

        if s == "Darwin":   # macOS
            return h / "Library" / "Application Support" / "Cursor"
        if s == "Windows":  # Windows
            return h / "AppData" / "Roaming" / "Cursor"
        if s == "Linux":    # Linux
            return h / ".config" / "Cursor"

        raise RuntimeError(f"Unsupported OS: {s}")

    def _load_json(self, cur: sqlite3.Cursor, table: str, key: str) -> Optional[Any]:
        """从 SQLite 表中加载 JSON 数据。

        Args:
            cur: SQLite 游标
            table: 表名
            key: 键名

        Returns:
            Optional[Any]: 解析后的 JSON 数据，如果未找到则为 None
        """
        try:
            cur.execute(f"SELECT value FROM {table} WHERE key=?", (key,))
            row = cur.fetchone()
            if not row:
                return None

            return json.loads(row[0])
        except Exception as e:
            logger.error(f"Error loading JSON from {table}.{key}: {e}")
            return None

    def read_workspace_data(self, workspace_path: pathlib.Path) -> Dict[str, Any]:
        """读取工作区数据。

        Args:
            workspace_path: 工作区路径

        Returns:
            Dict[str, Any]: 工作区数据
        """
        result = {"entries": [], "composers": {}}

        try:
            con = sqlite3.connect(f"file:{workspace_path}?mode=ro", uri=True)
            cur = con.cursor()

            # 获取历史条目
            entries = self._load_json(cur, "ItemTable", "history.entries") or []
            result["entries"] = entries

            # 获取 composer 数据
            composer_data = self._load_json(cur, "ItemTable", "composer.composerData") or {}
            result["composers"] = composer_data.get("allComposers", [])

            con.close()
        except Exception as e:
            logger.error(f"Error reading workspace data from {workspace_path}: {e}")

        return result

    def _load_json_safe(self, value):
        """安全地加载 JSON 数据，处理各种错误情况。

        Args:
            value: 要解析的 JSON 字符串

        Returns:
            Any: 解析后的 JSON 数据，如果解析失败则为 None
        """
        if not value:
            return None

        try:
            if isinstance(value, (str, bytes, bytearray)):
                return json.loads(value)
            return None
        except Exception as e:
            logger.debug(f"Error parsing JSON: {e}")
            return None

    def read_session_data(self, session_path: pathlib.Path) -> Dict[str, Any]:
        """读取会话数据。

        Args:
            session_path: 会话路径

        Returns:
            Dict[str, Any]: 会话数据
        """
        result = {
            "bubbles": [],
            "chat_data": None,
            "composer_data": None,
            "ai_service_data": []
        }

        try:
            con = sqlite3.connect(f"file:{session_path}?mode=ro", uri=True)
            cur = con.cursor()

            # 1. 从 cursorDiskKV 表提取 bubbleId 条目
            cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
            if cur.fetchone():
                # 获取所有 bubbleId 条目
                cur.execute("SELECT key, value FROM cursorDiskKV WHERE key LIKE 'bubbleId:%'")
                bubbles = []

                for key, value in cur.fetchall():
                    try:
                        if value is None:
                            logger.warning(f"Null value found for key {key}")
                            continue

                        bubble = self._load_json_safe(value)
                        if bubble:
                            bubble["key"] = key
                            bubbles.append(bubble)
                    except Exception as e:
                        logger.warning(f"Failed to process bubble for key {key}: {e}")

                result["bubbles"] = bubbles
                
                # 1.2 获取所有 composerData 条目
                cur.execute("SELECT key, value FROM cursorDiskKV WHERE key LIKE 'composerData:%'")
                for key, value in cur.fetchall():
                    try:
                        if value is None:
                            continue
                            
                        composer_data = self._load_json_safe(value)
                        if composer_data:
                            composer_data["key"] = key
                            # 如果已经有composer_data，将其附加到现有数据
                            if result["composer_data"] is None:
                                result["composer_data"] = composer_data
                            else:
                                # 如果已经有一个composer_data，转换为列表
                                if not isinstance(result["composer_data"], list):
                                    result["composer_data"] = [result["composer_data"]]
                                result["composer_data"].append(composer_data)
                    except Exception as e:
                        logger.warning(f"Failed to process composer data for key {key}: {e}")

            # 2. 从 ItemTable 表提取聊天数据
            cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable'")
            if cur.fetchone():
                # 提取 workbench.panel.aichat.view.aichat.chatdata
                cur.execute("SELECT value FROM ItemTable WHERE key=?", ("workbench.panel.aichat.view.aichat.chatdata",))
                row = cur.fetchone()
                if row and row[0]:
                    chat_data = self._load_json_safe(row[0])
                    result["chat_data"] = chat_data

                # 提取 composer.composerData
                cur.execute("SELECT value FROM ItemTable WHERE key=?", ("composer.composerData",))
                row = cur.fetchone()
                if row and row[0]:
                    composer_data = self._load_json_safe(row[0])
                    # 如果已经有从cursorDiskKV表中获取的composer_data，保留它
                    if result["composer_data"] is None:
                        result["composer_data"] = composer_data
                
                # 提取 AI Service 相关数据
                for key_prefix in ["aiService.prompts", "aiService.generations"]:
                    try:
                        cur.execute("SELECT key, value FROM ItemTable WHERE key LIKE ?", (f"{key_prefix}%",))
                        for k, v in cur.fetchall():
                            try:
                                data = self._load_json_safe(v)
                                if data:
                                    ai_service_item = {
                                        "key": k,
                                        "data": data,
                                        "type": "prompt" if "prompts" in key_prefix else "generation"
                                    }
                                    result["ai_service_data"].append(ai_service_item)
                            except Exception as e:
                                logger.warning(f"Failed to process AI service data for key {k}: {e}")
                    except sqlite3.Error as e:
                        logger.warning(f"Error querying AI service data: {e}")

            con.close()
        except Exception as e:
            logger.error(f"Error reading session data from {session_path}: {e}")

        return result

    def extract_project_info(self, workspace_data: Dict[str, Any]) -> ProjectInfo:
        """从工作区数据中提取项目信息。

        Args:
            workspace_data: 工作区数据

        Returns:
            ProjectInfo: 项目信息
        """
        # 默认值
        name = "Unknown Project"
        root_path = "/"
        workspace_id = None
        
        try:
            entries = workspace_data.get("entries", [])
            
            # 提取文件路径
            file_paths = []
            for entry in entries:
                # 检查编辑器资源路径
                if isinstance(entry, dict) and "editor" in entry:
                    editor = entry.get("editor", {})
                    if isinstance(editor, dict) and "resource" in editor:
                        res = editor.get("resource", "")
                        if isinstance(res, str) and res.startswith("file:///"):
                            file_paths.append(res[8:])  # 移除 "file:///"
                
                # 同时检查explorer.editorViewState
                if isinstance(entry, dict) and "explorer" in entry:
                    explorer = entry.get("explorer", {})
                    if isinstance(explorer, dict) and "editorViewState" in explorer:
                        view_state = explorer.get("editorViewState", [])
                        if isinstance(view_state, list):
                            for view in view_state:
                                if isinstance(view, dict) and "resource" in view:
                                    res = view.get("resource", "")
                                    if isinstance(res, str) and res.startswith("file:///"):
                                        file_paths.append(res[8:])  # 移除 "file:///"
            
            # 如果有文件路径，尝试获取最长共同前缀
            if file_paths:
                # 尝试找到所有路径的共同前缀
                common_prefix = os.path.dirname(os.path.commonprefix(file_paths))
                
                if common_prefix and common_prefix != "/":
                    # 确保路径是合理的目录
                    if os.path.isdir(common_prefix):
                        root_path = common_prefix
                        # 从路径中提取项目名称
                        extracted_name = extract_project_name_from_path(root_path, debug=True)
                        if extracted_name and extracted_name != "Unknown Project":
                            name = extracted_name
                        else:
                            # 回退到最后一个目录名称
                            name = os.path.basename(root_path)
                            if not name:  # 处理根目录的情况
                                name = "Root Project"
            
            # 尝试获取当前工作区文件夹名称
            if workspace_data.get("folder"):
                folder = workspace_data.get("folder")
                if isinstance(folder, str) and folder.startswith("file:///"):
                    folder_path = folder[8:]  # 移除 "file:///"
                    if os.path.isdir(folder_path):
                        root_path = folder_path
                        # 从路径中提取项目名称
                        extracted_name = extract_project_name_from_path(root_path, debug=True)
                        if extracted_name and extracted_name != "Unknown Project":
                            name = extracted_name
                        else:
                            # 回退到最后一个目录名称
                            name = os.path.basename(root_path)
                            if not name:  # 处理根目录的情况
                                name = "Root Project"
            
            # 尝试从URI中提取workspace_id
            key = workspace_data.get("key", "")
            if isinstance(key, str) and "workspaceStorage" in key:
                parts = key.split("/")
                for i, part in enumerate(parts):
                    if part == "workspaceStorage" and i+1 < len(parts):
                        workspace_id = parts[i+1]
                        break
        
        except Exception as e:
            logger.error(f"Error extracting project info: {e}")
        
        return ProjectInfo(
            name=name,
            root_path=root_path,
            workspace_id=workspace_id
        )

    def extract_messages(self, session_data: Dict[str, Any]) -> List[ChatMessage]:
        """从会话数据中提取消息。

        Args:
            session_data: 会话数据

        Returns:
            List[ChatMessage]: 消息列表
        """
        messages = []

        # 1. 从 bubbles 提取消息
        bubbles = session_data.get("bubbles", [])
        for bubble in bubbles:
            # 尝试从多个可能的字段中提取文本
            text = ""
            if "text" in bubble and bubble["text"]:
                text = bubble["text"]
            elif "richText" in bubble and bubble["richText"]:
                text = bubble["richText"]
            elif "content" in bubble and bubble["content"]:
                text = bubble["content"]

            # 跳过没有文本的消息
            if not text or not isinstance(text, str):
                continue

            # 确定角色
            role = "user" if bubble.get("type") == 1 else "assistant"

            # 提取 composerId
            composer_id = "unknown"
            key = bubble.get("key", "")
            if key:
                key_parts = key.split(":")
                if len(key_parts) >= 2:
                    composer_id = key_parts[1]

            # 创建消息
            message = ChatMessage(
                role=role,
                content=text.strip(),
                metadata={"composer_id": composer_id}
            )

            messages.append(message)

        # 2. 从 chat_data 提取消息
        chat_data = session_data.get("chat_data")
        if chat_data and isinstance(chat_data, dict) and "tabs" in chat_data:
            for tab in chat_data.get("tabs", []):
                tab_id = tab.get("tabId", "unknown")
                for bubble in tab.get("bubbles", []):
                    bubble_type = bubble.get("type")
                    if not bubble_type:
                        continue

                    # 提取文本
                    text = ""
                    if "text" in bubble and bubble["text"]:
                        text = bubble["text"]
                    elif "content" in bubble and bubble["content"]:
                        text = bubble["content"]

                    if text and isinstance(text, str):
                        role = "user" if bubble_type == "user" else "assistant"
                        message = ChatMessage(
                            role=role,
                            content=text.strip(),
                            metadata={"tab_id": tab_id}
                        )
                        messages.append(message)

        # 3. 从 composer_data 提取消息
        composer_data = session_data.get("composer_data")
        if composer_data is not None:
            # 处理单个composer数据或composer数据列表
            if isinstance(composer_data, dict):
                composer_data_list = [composer_data]
            elif isinstance(composer_data, list):
                composer_data_list = composer_data
            else:
                composer_data_list = []
                
            for composer_data_item in composer_data_list:
                if not isinstance(composer_data_item, dict):
                    continue
                    
                # 3.1. 处理 allComposers
                for comp in composer_data_item.get("allComposers", []):
                    comp_id = comp.get("composerId", "unknown")
                    # 处理messages字段
                    for msg in comp.get("messages", []):
                        role = msg.get("role", "unknown")
                        content = msg.get("content", "")

                        if content and isinstance(content, str):
                            message = ChatMessage(
                                role=role,
                                content=content.strip(),
                                metadata={"composer_id": comp_id}
                            )
                            messages.append(message)
                    
                    # 处理 conversation 字段
                    conversation = comp.get("conversation", [])
                    if conversation and isinstance(conversation, list):
                        for msg in conversation:
                            msg_type = msg.get("type")
                            if msg_type is None:
                                continue
                            
                            # Type 1 = user, Type 2 = assistant
                            role = "user" if msg_type == 1 else "assistant"
                            content = msg.get("text", "")
                            if content and isinstance(content, str):
                                message = ChatMessage(
                                    role=role,
                                    content=content.strip(),
                                    metadata={"composer_id": comp_id}
                                )
                                messages.append(message)
                
                # 3.2. 直接处理 conversation 字段
                conversation = composer_data_item.get("conversation", [])
                if conversation and isinstance(conversation, list):
                    # 从键中提取 composer_id
                    composer_id = "unknown"
                    key = composer_data_item.get("key", "")
                    if isinstance(key, str) and ":" in key:
                        composer_id = key.split(":", 1)[1]
                    
                    for msg in conversation:
                        msg_type = msg.get("type")
                        if msg_type is None:
                            continue
                        
                        # Type 1 = user, Type 2 = assistant
                        role = "user" if msg_type == 1 else "assistant"
                        content = msg.get("text", "")
                        if content and isinstance(content, str):
                            message = ChatMessage(
                                role=role,
                                content=content.strip(),
                                metadata={"composer_id": composer_id}
                            )
                            messages.append(message)

        # 4. 从 ai_service_data 提取消息
        ai_service_data = session_data.get("ai_service_data", [])
        for item in ai_service_data:
            item_type = item.get("type")
            if not item_type:
                continue
                
            data = item.get("data")
            if not data:
                continue
                
            # 处理数组或单个项
            data_items = data if isinstance(data, list) else [data]
            
            for data_item in data_items:
                if not isinstance(data_item, dict):
                    continue
                    
                text = data_item.get("text", "")
                item_id = data_item.get("id", "unknown")
                
                if text and isinstance(text, str):
                    role = "user" if item_type == "prompt" else "assistant"
                    message = ChatMessage(
                        role=role,
                        content=text.strip(),
                        metadata={"ai_service_id": item_id}
                    )
                    messages.append(message)

        return messages
