"""
存储定位器接口模块。

这个模块定义了负责定位不同助手的存储位置的接口。
"""

import pathlib
from abc import ABC, abstractmethod
from typing import List


class StorageLocator(ABC):
    """负责定位不同助手的存储位置。"""
    
    @abstractmethod
    def get_workspace_storage_paths(self) -> List[pathlib.Path]:
        """获取工作区存储路径列表。
        
        Returns:
            List[pathlib.Path]: 工作区存储路径列表
        """
        pass
    
    @abstractmethod
    def get_global_storage_paths(self) -> List[pathlib.Path]:
        """获取全局存储路径列表。
        
        Returns:
            List[pathlib.Path]: 全局存储路径列表
        """
        pass
    
    @abstractmethod
    def get_all_storage_paths(self) -> List[pathlib.Path]:
        """获取所有存储路径列表。
        
        Returns:
            List[pathlib.Path]: 所有存储路径列表
        """
        pass
