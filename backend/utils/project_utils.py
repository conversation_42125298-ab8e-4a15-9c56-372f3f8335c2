"""
项目工具模块。

这个模块提供了与项目相关的工具函数。
"""

import os
import logging
import sqlite3
from pathlib import Path
from typing import Optional

# 配置日志
logger = logging.getLogger(__name__)

def extract_project_name_from_path(root_path, debug=False):
    """
    从路径中提取项目名称，跳过用户目录。
    
    Args:
        root_path: 项目根路径
        debug: 是否打印调试信息
        
    Returns:
        str: 提取的项目名称
    """
    if not root_path or root_path == '/':
        return "Root"

    # 如果路径是一个字符串，则分割它
    if isinstance(root_path, str):
        path_parts = [p for p in root_path.split('/') if p]
    # 如果路径是Path对象或类似对象，转换为字符串列表
    else:
        try:
            path_parts = list(root_path.parts)
            path_parts = [p for p in path_parts if p and p != '/']
        except:
            # 回退到字符串方法
            path_str = str(root_path)
            path_parts = [p for p in path_str.split('/') if p]

    # 跳过常见的用户目录模式
    project_name = None
    home_dir_patterns = ['Users', 'home']
    work_dir_patterns = ['workspace', 'workspaces', 'projects', 'codeworkspace']
    
    # 获取当前用户名以进行比较
    current_username = os.path.basename(os.path.expanduser('~'))
    
    # 在工作目录模式后查找项目名
    username_index = -1
    work_dir_index = -1
    
    # 首先查找用户目录
    for i, part in enumerate(path_parts):
        if part in home_dir_patterns:
            username_index = i
            if i+1 < len(path_parts) and path_parts[i+1] == current_username:
                username_index = i+1
                
    # 然后查找工作目录
    if username_index >= 0:
        for i in range(username_index+1, len(path_parts)):
            if path_parts[i].lower() in work_dir_patterns:
                work_dir_index = i
                break
    
    # 如果找到了工作目录，项目名应该是工作目录后的第一个目录
    if work_dir_index >= 0 and work_dir_index + 1 < len(path_parts):
        project_name = path_parts[work_dir_index + 1]
    # 如果没有找到工作目录但找到了用户目录，则尝试使用用户目录后的目录作为项目名
    elif username_index >= 0 and username_index + 1 < len(path_parts):
        # 跳过直接在用户目录下的常见目录
        skip_dirs = ['Desktop', 'Documents', 'Downloads', 'dev', 'src']
        # 从用户目录后开始寻找项目名
        for i in range(username_index + 1, len(path_parts)):
            if path_parts[i] not in skip_dirs:
                project_name = path_parts[i]
                break
    # 如果无法确定，使用路径中的最后一个目录作为项目名
    else:
        project_name = path_parts[-1] if path_parts else "Unknown Project"
    
    # 如果路径中包含git等关键字，可能表示这是一个代码仓库，使用子目录名
    if project_name and project_name.lower() in ['.git', 'git', 'github', 'gitlab']:
        project_name = path_parts[-1] if len(path_parts) > 1 else "Unknown Project"
    
    return project_name if project_name else "Unknown Project"

def extract_project_from_git_repos(workspace_id: str, cursor_root: str = None, debug=False) -> str:
    """从Git仓库提取项目名称

    Args:
        workspace_id: 工作区ID
        cursor_root: Cursor根目录
        debug: 是否打印调试信息
        
    Returns:
        str: 提取的项目名称
    """
    try:
        # 如果未提供cursor_root，尝试从CursorStorageLocator中获取
        import pathlib
        if not cursor_root:
            h = pathlib.Path.home()
            import platform
            s = platform.system()
            if s == "Darwin":   # macOS
                cursor_root = h / "Library" / "Application Support" / "Cursor"
            elif s == "Windows":  # Windows
                cursor_root = h / "AppData" / "Roaming" / "Cursor"
            elif s == "Linux":    # Linux
                cursor_root = h / ".config" / "Cursor"
            else:
                return None
        else:
            cursor_root = pathlib.Path(cursor_root)
        
        # 查找repos文件
        repos_file = cursor_root / "User" / "globalStorage" / "github.remotehub" / "repos.json"
        
        if not repos_file.exists():
            if debug:
                logger.debug(f"Git repos file does not exist: {repos_file}")
            return None
        
        # 读取repos文件
        import json
        with open(repos_file, 'r') as f:
            repos_data = json.load(f)
        
        # 搜索与workspace_id相关的仓库
        for repo in repos_data.get("repos", []):
            repo_workspace_id = repo.get("workspaceId")
            if repo_workspace_id == workspace_id:
                # 提取仓库名称
                repo_name = repo.get("repository", {}).get("name")
                if repo_name:
                    if debug:
                        logger.debug(f"Found Git repository name: {repo_name} for workspace: {workspace_id}")
                    return repo_name
        
        if debug:
            logger.debug(f"No matching Git repository found for workspace: {workspace_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error extracting project from Git repos: {e}")
        return None 