"""
适配器接口模块。

这个模块定义了所有 AI 助手适配器必须实现的接口。
"""

from __future__ import annotations

import pathlib
from abc import ABC, abstractmethod
from typing import List, Optional

from ..models.chat_session import ChatSession


class AssistantAdapter(ABC):
    """所有 AI 助手适配器必须实现的接口。"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """返回助手的唯一标识符。
        
        Returns:
            str: 助手的唯一标识符
        """
        pass
    
    @property
    @abstractmethod
    def display_name(self) -> str:
        """返回助手的显示名称。
        
        Returns:
            str: 助手的显示名称
        """
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """返回助手的描述。
        
        Returns:
            str: 助手的描述
        """
        pass
    
    @property
    @abstractmethod
    def icon_url(self) -> str:
        """返回助手的图标 URL。
        
        Returns:
            str: 助手的图标 URL
        """
        pass
    
    @abstractmethod
    def get_storage_locations(self) -> List[pathlib.Path]:
        """返回此助手存储聊天历史的所有可能位置。
        
        Returns:
            List[pathlib.Path]: 存储位置列表
        """
        pass
    
    @abstractmethod
    def extract_chat_sessions(self) -> List[ChatSession]:
        """提取并返回此助手的所有聊天会话。
        
        Returns:
            List[ChatSession]: 聊天会话列表
        """
        pass
    
    @abstractmethod
    def supports_file(self, file_path: pathlib.Path) -> bool:
        """检查给定文件是否可能包含此助手的聊天数据。
        
        Args:
            file_path: 要检查的文件路径
            
        Returns:
            bool: 如果文件可能包含此助手的聊天数据，则为 True
        """
        pass
    
    @abstractmethod
    def extract_from_file(self, file_path: pathlib.Path) -> List[ChatSession]:
        """从特定文件提取聊天会话。
        
        Args:
            file_path: 要提取的文件路径
            
        Returns:
            List[ChatSession]: 提取的聊天会话列表
        """
        pass
