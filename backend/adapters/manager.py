"""
适配器管理器模块。

这个模块定义了管理和协调所有已注册的助手适配器的类。
"""

import logging
from typing import Dict, List, Optional

from .base import AssistantAdapter
from ..models.chat_session import ChatSession

# 配置日志
logger = logging.getLogger(__name__)


class AdapterManager:
    """管理和协调所有已注册的助手适配器。"""
    
    def __init__(self):
        """初始化适配器管理器。"""
        self.adapters: Dict[str, AssistantAdapter] = {}
    
    def register_adapter(self, adapter: AssistantAdapter) -> None:
        """注册一个新的助手适配器。
        
        Args:
            adapter: 要注册的适配器
        """
        logger.info(f"Registering adapter: {adapter.name}")
        self.adapters[adapter.name] = adapter
    
    def get_adapter(self, name: str) -> Optional[AssistantAdapter]:
        """获取指定名称的适配器。
        
        Args:
            name: 适配器名称
            
        Returns:
            Optional[AssistantAdapter]: 找到的适配器，如果未找到则为 None
        """
        return self.adapters.get(name)
    
    def get_all_adapters(self) -> List[AssistantAdapter]:
        """获取所有已注册的适配器。
        
        Returns:
            List[AssistantAdapter]: 适配器列表
        """
        return list(self.adapters.values())
    
    def extract_all_chats(self) -> Dict[str, List[ChatSession]]:
        """从所有已注册的适配器提取聊天会话。
        
        Returns:
            Dict[str, List[ChatSession]]: 按适配器名称分组的聊天会话
        """
        result = {}
        for name, adapter in self.adapters.items():
            try:
                logger.info(f"Extracting chats from adapter: {name}")
                result[name] = adapter.extract_chat_sessions()
                logger.info(f"Extracted {len(result[name])} chats from adapter: {name}")
            except Exception as e:
                logger.error(f"Error extracting chats from {name}: {e}")
                result[name] = []
        return result
