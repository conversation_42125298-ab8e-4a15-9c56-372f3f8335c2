# 多 AI 助手聊天历史提取系统

这个系统是对原有 Cursor 聊天历史提取系统的重构和扩展，支持多个 AI 编码助手，包括 Augment Code、Cline、Roocode 和 Windsurf。

## 功能特点

- 支持多个 AI 编码助手的聊天历史提取
- 模块化设计，易于添加新的助手支持
- 统一的 API 接口，便于前端集成
- 与原有 Cursor 功能保持向后兼容

## 目录结构

```
backend/
├── adapters/         # 适配器实现
├── models/           # 数据模型
├── storage/          # 存储实现
├── api/              # API 路由
└── utils/            # 工具函数
```

## 安装

```bash
# 安装依赖
pip install -r backend/requirements.txt
```

## 使用方法

```bash
# 启动服务器
python server_multi.py --port 5001
```

## API 端点

- `GET /api/assistants` - 获取所有支持的助手类型
- `GET /api/chats` - 获取所有聊天会话
- `GET /api/chat/<session_id>` - 获取特定的聊天会话
- `GET /api/chat/<session_id>/export` - 导出特定的聊天会话为 HTML

## 开发指南

### 添加新的助手支持

1. 在 `adapters/` 目录下创建新的适配器类
2. 在 `storage/` 目录下创建新的存储实现
3. 在 `server_multi.py` 中注册新的适配器

### 运行测试

```bash
# 运行单元测试
python -m unittest discover -s backend/tests
```
