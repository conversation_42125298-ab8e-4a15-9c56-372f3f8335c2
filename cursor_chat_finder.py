#!/usr/bin/env python3
"""
Find and extract all Cursor chat histories from a user's system.
This script locates all workspace and session databases and extracts chat data.
"""

import os
import glob
import json
import pathlib
import platform
import datetime
import logging
from typing import List, Dict, Any, Optional

from extract_cursor_chat import load_chat_session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Enable detailed logging for debugging Cursor storage locations
DETAILED_LOGGING = True

def get_cursor_storage_path() -> pathlib.Path:
    """Get the path where Cursor stores its data based on the OS."""
    system = platform.system()
    home = pathlib.Path.home()

    if system == "Darwin":  # macOS
        return home / "Library" / "Application Support" / "Cursor"
    elif system == "Windows":
        return home / "AppData" / "Roaming" / "Cursor"
    elif system == "Linux":
        return home / ".config" / "Cursor"
    else:
        raise RuntimeError(f"Unsupported platform: {system}")

def find_workspace_dbs() -> List[Dict[str, pathlib.Path]]:
    """Find all workspace databases (state.vscdb) and their associated session DBs."""
    cursor_path = get_cursor_storage_path()
    if DETAILED_LOGGING:
        logger.info(f"Cursor storage path: {cursor_path}")

    workspace_storage = cursor_path / "User" / "workspaceStorage"
    if DETAILED_LOGGING:
        logger.info(f"Workspace storage path: {workspace_storage}")

    if not workspace_storage.exists():
        if DETAILED_LOGGING:
            logger.warning(f"Workspace storage directory does not exist: {workspace_storage}")
        return []

    results = []

    # Possible locations for session databases
    extension_dirs = [
        cursor_path / "User" / "globalStorage" / "cursor.cursor",
        cursor_path / "User" / "globalStorage" / "cursor",
        # Try directly in globalStorage as well
        cursor_path / "User" / "globalStorage"
    ]

    if DETAILED_LOGGING:
        logger.info("Checking for session databases in the following locations:")
        for dir_path in extension_dirs:
            logger.info(f"  - {dir_path} (exists: {dir_path.exists()})")

    # Find all session databases across all possible locations
    all_session_dbs = []
    for extension_dir in extension_dirs:
        if extension_dir.exists():
            sqlite_files = list(extension_dir.glob("*.sqlite"))
            if sqlite_files:
                if DETAILED_LOGGING:
                    logger.info(f"Found {len(sqlite_files)} .sqlite files in {extension_dir}:")
                    for db_file in sqlite_files:
                        logger.info(f"  - {db_file.name} (size: {db_file.stat().st_size} bytes)")
                all_session_dbs.extend(sqlite_files)

    # If no session DBs found, try more generic patterns
    if not all_session_dbs:
        if DETAILED_LOGGING:
            logger.info("No .sqlite files found, trying other extensions...")

        for pattern in ["*.db", "*.sqlite3", "*.vscdb"]:
            for extension_dir in extension_dirs:
                if extension_dir.exists():
                    other_files = list(extension_dir.glob(pattern))
                    if other_files:
                        if DETAILED_LOGGING:
                            logger.info(f"Found {len(other_files)} {pattern} files in {extension_dir}:")
                            for db_file in other_files:
                                logger.info(f"  - {db_file.name} (size: {db_file.stat().st_size} bytes)")
                        all_session_dbs.extend(other_files)

    if DETAILED_LOGGING:
        if all_session_dbs:
            logger.info(f"Total session databases found: {len(all_session_dbs)}")
        else:
            logger.warning("No session databases found in any location")

    # Create a dummy workspace entry if no workspaces but we have session DBs
    if all_session_dbs and not any(workspace_storage.glob("*")):
        if DETAILED_LOGGING:
            logger.info("No workspace directories found, but session databases exist. Creating dummy workspace entry.")

        results.append({
            "workspace_db": None,  # We'll handle None values in extract_all_chats
            "session_dbs": all_session_dbs,
            "workspace_id": "unknown"
        })
        return results

    # Process workspaces
    workspace_count = 0
    for workspace_dir in workspace_storage.glob("*"):
        workspace_db = workspace_dir / "state.vscdb"

        if DETAILED_LOGGING:
            logger.info(f"Found workspace directory: {workspace_dir.name}")
            logger.info(f"  - state.vscdb exists: {workspace_db.exists()}")

        if not workspace_db.exists():
            if DETAILED_LOGGING:
                logger.warning(f"No state.vscdb found in workspace {workspace_dir.name}")
            continue

        workspace_count += 1
        # For now, just associate all session DBs with all workspaces
        # This is a simplification but ensures we don't miss any chats
        results.append({
            "workspace_db": workspace_db,
            "session_dbs": all_session_dbs,
            "workspace_id": workspace_dir.name
        })

    if DETAILED_LOGGING:
        logger.info(f"Found {workspace_count} valid workspaces with state.vscdb files")

    return results

def extract_all_chats() -> List[Dict[str, Any]]:
    """Extract all chat sessions from all workspaces."""
    if DETAILED_LOGGING:
        logger.info("Starting extraction of all chat sessions")

    all_workspaces = find_workspace_dbs()
    all_chats = []

    if not all_workspaces:
        if DETAILED_LOGGING:
            logger.warning("No workspaces found, returning sample data")
        # Create sample data for demo purposes
        return create_sample_chats()

    if DETAILED_LOGGING:
        logger.info(f"Found {len(all_workspaces)} workspace configurations to process")

    for workspace_idx, workspace in enumerate(all_workspaces):
        workspace_db = workspace["workspace_db"]
        workspace_id = workspace["workspace_id"]

        if DETAILED_LOGGING:
            logger.info(f"Processing workspace {workspace_idx+1}/{len(all_workspaces)}: {workspace_id}")
            if workspace_db:
                logger.info(f"  - Workspace DB: {workspace_db}")
            else:
                logger.info("  - No workspace DB available (using fallback)")
            logger.info(f"  - Session DBs to process: {len(workspace['session_dbs'])}")

        for session_idx, session_db in enumerate(workspace["session_dbs"]):
            try:
                if DETAILED_LOGGING:
                    logger.info(f"  - Processing session DB {session_idx+1}/{len(workspace['session_dbs'])}: {session_db}")

                # Get file modification time as a proxy for chat date
                mod_time = datetime.datetime.fromtimestamp(session_db.stat().st_mtime)
                chat_date = mod_time.strftime("%Y-%m-%d %H:%M:%S")

                if DETAILED_LOGGING:
                    logger.info(f"    - Last modified: {chat_date}")

                # Extract the chat session
                session = load_chat_session(workspace_db, session_db) if workspace_db else create_fallback_session(session_db)

                # Log message count
                if DETAILED_LOGGING:
                    msg_count = len(session.messages) if session.messages else 0
                    logger.info(f"    - Messages found: {msg_count}")

                # Skip sessions with no messages
                if not session.messages:
                    if DETAILED_LOGGING:
                        logger.info(f"    - Skipping session with no messages")
                    continue

                # Add metadata
                chat_data = session.to_dict()
                chat_data["date"] = chat_date
                chat_data["session_id"] = session_db.stem
                chat_data["workspace_id"] = workspace_id
                chat_data["db_path"] = str(session_db)  # Add database path for reference

                if DETAILED_LOGGING:
                    project_name = chat_data["project"].get("name", "Unknown")
                    logger.info(f"    - Added chat from project: {project_name}")
                    logger.info(f"    - Database path: {session_db}")

                all_chats.append(chat_data)
            except Exception as e:
                logger.error(f"Error extracting chat from {session_db}: {e}")

    # Sort by date (newest first)
    all_chats.sort(key=lambda x: x["date"], reverse=True)

    if DETAILED_LOGGING:
        logger.info(f"Total chats extracted: {len(all_chats)}")

    # If still no chats, return sample data for demo
    if not all_chats:
        if DETAILED_LOGGING:
            logger.warning("No chats found in any database, returning sample data")
        return create_sample_chats()

    return all_chats

def create_fallback_session(session_db: pathlib.Path):
    """Create a fallback session when workspace_db is not available"""
    from extract_cursor_chat import ChatSession, extract_messages
    project = {"name": "Unknown Project", "rootPath": "/"}
    messages = extract_messages(session_db)
    return ChatSession(project, messages)

def create_sample_chats() -> List[Dict[str, Any]]:
    """Create sample chat data for demo purposes"""
    return [
        {
            "project": {"name": "Sample Project", "rootPath": "/path/to/sample"},
            "messages": [
                {"role": "user", "content": "Can you help me with this React component?"},
                {"role": "assistant", "content": "Of course! What specific issues are you having with the component?"}
            ],
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "session_id": "sample1",
            "workspace_id": "sample"
        },
        {
            "project": {"name": "Demo API", "rootPath": "/path/to/demo-api"},
            "messages": [
                {"role": "user", "content": "How do I properly structure my Flask API?"},
                {"role": "assistant", "content": "For Flask APIs, I recommend organizing your code with a blueprint structure. Here's an example..."}
            ],
            "date": (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S"),
            "session_id": "sample2",
            "workspace_id": "sample"
        }
    ]

def save_all_chats(output_path: pathlib.Path):
    """Save all extracted chats to a JSON file."""
    chats = extract_all_chats()
    output_path.write_text(json.dumps(chats, ensure_ascii=False, indent=2))
    return chats

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Extract all Cursor chat histories")
    parser.add_argument("--out", type=pathlib.Path, default=pathlib.Path("cursor_chats.json"),
                        help="Output JSON file (default: cursor_chats.json)")
    args = parser.parse_args()

    chats = save_all_chats(args.out)
    print(f"Extracted {len(chats)} chat sessions to {args.out}")