#!/usr/bin/env python3
"""
extract_augment_chat.py - 提取 Augment Code 扩展的历史记录数据

这个脚本用于从 VS Code 的工作区存储中提取 Augment Code 扩展的聊天历史记录数据，
包括项目名称、项目路径、工作区 ID 和会话数据。
支持导出为 JSON 或 Markdown 格式。
"""

# 标准库导入
import argparse
import json
import logging
import os
import pathlib
import platform
import re
import shutil
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import quote_plus

# 第三方库导入
try:
    # SQLAlchemy Core 导入
    from sqlalchemy import (
        create_engine, MetaData, Table, Column, Integer, String, Text,
        TIMESTAMP, JSON, Index, UniqueConstraint, text, exc
    )
    from sqlalchemy.dialects.mysql import insert
    from sqlalchemy.engine import Engine
    from sqlalchemy.pool import QueuePool
    import pymysql
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

# 保持向后兼容性的导入
try:
    import mysql.connector
    from mysql.connector import Error
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

# 常量定义
DEFAULT_DB_CONFIG = {
    'host': 'rm-2zeci3z6ogyl025l59o.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'chip',
    'password': 'chip@2024',
    'database': 'chat_history'
}

DEFAULT_HISTORY_DIR = 'history'
DEFAULT_OUTPUT_FORMATS = ['json', 'md', 'db']
DATETIME_FORMAT = '%Y年%m月%d日 %H:%M'
PLATFORM_NAME = 'augment_code'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器，负责创建表和导入数据 - 使用 SQLAlchemy Core"""

    def __init__(self, host='localhost', port=3306, user='root', password='', database='augment_chat',
                 pool_size=10, max_overflow=20, pool_timeout=30, pool_recycle=3600):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.pool_timeout = pool_timeout
        self.pool_recycle = pool_recycle

        # SQLAlchemy 相关属性
        self.engine: Optional[Engine] = None
        self.metadata: Optional[MetaData] = None
        self.tables: Dict[str, Table] = {}

        # 向后兼容属性
        self.connection = None

    def connect(self):
        """连接到MySQL数据库"""
        if not SQLALCHEMY_AVAILABLE:
            raise RuntimeError("SQLAlchemy 或 PyMySQL 未安装。请运行: pip install sqlalchemy pymysql")

        try:
            # 构建数据库连接 URL，对用户名和密码进行 URL 编码
            encoded_user = quote_plus(self.user)
            encoded_password = quote_plus(self.password)
            db_url = f"mysql+pymysql://{encoded_user}:{encoded_password}@{self.host}:{self.port}"

            # 首先连接到MySQL服务器（不指定数据库）创建数据库
            temp_engine = create_engine(
                db_url,
                poolclass=QueuePool,
                pool_size=1,
                max_overflow=0,
                echo=False
            )

            # 创建数据库（如果不存在）
            with temp_engine.connect() as conn:
                conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                conn.commit()

            temp_engine.dispose()

            # 创建连接到指定数据库的引擎
            full_db_url = f"{db_url}/{self.database}?charset=utf8mb4"
            self.engine = create_engine(
                full_db_url,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                echo=False,
                connect_args={
                    'charset': 'utf8mb4',
                    'use_unicode': True,
                    'autocommit': False
                }
            )

            # 初始化元数据
            self.metadata = MetaData()
            self._define_tables()

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            # 设置向后兼容的连接属性
            self.connection = self.engine

            logger.info(f"成功连接到数据库: {self.database}")

        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def _define_tables(self):
        """定义数据库表结构"""
        # 项目表
        self.tables['projects'] = Table(
            'projects', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('workspace_id', String(255), nullable=False),
            Column('platform', String(50), nullable=False),
            Column('name', String(255), nullable=False),
            Column('path', Text, nullable=False),
            Column('created_at', TIMESTAMP, server_default=text('CURRENT_TIMESTAMP')),
            Column('updated_at', TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),

            # 约束和索引
            UniqueConstraint('workspace_id', 'platform', name='unique_workspace_platform'),
            Index('idx_workspace_id', 'workspace_id'),
            Index('idx_platform', 'platform'),
            Index('idx_name', 'name'),

            # MySQL 特定选项
            mysql_engine='InnoDB',
            mysql_charset='utf8mb4',
            mysql_collate='utf8mb4_unicode_ci'
        )

        # 对话表
        self.tables['conversations'] = Table(
            'conversations', self.metadata,
            Column('id', String(255), primary_key=True),
            Column('workspace_id', String(255), nullable=False),
            Column('project_name', String(255), nullable=False),
            Column('name', String(255), default=''),
            Column('created_at', TIMESTAMP, nullable=True),
            Column('last_interacted_at', TIMESTAMP, nullable=True),
            Column('message_count', Integer, default=0),
            Column('created_timestamp', TIMESTAMP, server_default=text('CURRENT_TIMESTAMP')),
            Column('updated_timestamp', TIMESTAMP, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),

            # 索引
            Index('idx_workspace_id', 'workspace_id'),
            Index('idx_project_name', 'project_name'),
            Index('idx_created_at', 'created_at'),
            Index('idx_last_interacted_at', 'last_interacted_at'),

            # MySQL 特定选项
            mysql_engine='InnoDB',
            mysql_charset='utf8mb4',
            mysql_collate='utf8mb4_unicode_ci'
        )

        # 消息表
        self.tables['messages'] = Table(
            'messages', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('conversation_id', String(255), nullable=False),
            Column('request_id', String(255), nullable=False),
            Column('role', String(20), nullable=False),
            Column('content', Text, nullable=False),
            Column('timestamp', TIMESTAMP, nullable=True),
            Column('message_order', Integer, nullable=False),
            Column('workspace_files', JSON, nullable=True),
            Column('created_at', TIMESTAMP, server_default=text('CURRENT_TIMESTAMP')),

            # 索引和约束
            Index('idx_conversation_id', 'conversation_id'),
            Index('idx_request_id', 'request_id'),
            Index('idx_role', 'role'),
            Index('idx_timestamp', 'timestamp'),
            Index('idx_message_order', 'message_order'),
            UniqueConstraint('conversation_id', 'request_id', 'role', 'message_order', name='unique_message'),

            # MySQL 特定选项
            mysql_engine='InnoDB',
            mysql_charset='utf8mb4',
            mysql_collate='utf8mb4_unicode_ci'
        )

    def create_tables(self):
        """创建所需的表"""
        if not self.engine:
            raise RuntimeError("数据库未连接")

        try:
            # 使用 SQLAlchemy 创建所有表
            self.metadata.create_all(self.engine, checkfirst=True)

            logger.info("创建 projects 表成功")
            logger.info("创建 conversations 表成功")
            logger.info("创建 messages 表成功")

        except Exception as e:
            logger.error(f"创建表失败: {e}")
            raise

    def import_projects(self, projects: List['AugmentProject']):
        """导入项目数据到数据库"""
        if not self.engine:
            raise RuntimeError("数据库未连接")

        stats = {'conversations': 0, 'messages': 0, 'failed': 0}

        try:
            with self.engine.begin() as conn:  # 自动事务管理
                for project in projects:
                    logger.info(f"正在导入项目: {project.name}")
                    self._insert_project(conn, project)
                    self._import_project_conversations(conn, project, stats)

            self._log_import_results(len(projects), stats)

        except Exception as e:
            logger.error(f"导入数据失败: {e}")
            raise

    def _insert_project(self, conn, project: 'AugmentProject'):
        """插入项目数据"""
        projects_table = self.tables['projects']

        # 使用 MySQL 特定的 INSERT ... ON DUPLICATE KEY UPDATE
        stmt = insert(projects_table).values(
            workspace_id=project.workspace_id,
            platform=PLATFORM_NAME,
            name=project.name,
            path=project.path
        )

        # 添加 ON DUPLICATE KEY UPDATE 子句
        stmt = stmt.on_duplicate_key_update(
            name=stmt.inserted.name,
            path=stmt.inserted.path,
            updated_at=text('CURRENT_TIMESTAMP')
        )

        conn.execute(stmt)

    def _import_project_conversations(self, conn, project: 'AugmentProject', stats: Dict[str, int]):
        """导入项目的所有对话"""
        for conversation in project.conversations:
            try:
                conv_messages = self._import_conversation(conn, project, conversation)
                stats['conversations'] += 1
                stats['messages'] += conv_messages
            except Exception as e:
                logger.error(f"导入对话失败 {conversation.get('id', 'unknown')}: {e}")
                stats['failed'] += 1
                # 继续处理下一个对话，不中断整个导入过程
                continue

    def _log_import_results(self, project_count: int, stats: Dict[str, int]):
        """记录导入结果"""
        logger.info(f"导入完成: {project_count} 个项目, {stats['conversations']} 个会话, {stats['messages']} 条消息")
        if stats['failed'] > 0:
            logger.warning(f"跳过了 {stats['failed']} 个有问题的对话")

    def _import_conversation(self, conn, project: 'AugmentProject', conversation: Dict[str, Any]) -> int:
        """导入单个对话数据，返回导入的消息数量"""
        # 解析时间戳
        created_at = self._parse_timestamp(conversation.get('created_at', ''))
        last_interacted_at = self._parse_timestamp(conversation.get('last_interacted_at', ''))

        # 插入对话数据
        conversations_table = self.tables['conversations']
        stmt = insert(conversations_table).values(
            id=conversation['id'],
            workspace_id=project.workspace_id,
            project_name=project.name,
            name=conversation.get('name', ''),
            created_at=created_at,
            last_interacted_at=last_interacted_at,
            message_count=len(conversation.get('messages', []))
        )

        # 添加 ON DUPLICATE KEY UPDATE 子句
        stmt = stmt.on_duplicate_key_update(
            name=stmt.inserted.name,
            last_interacted_at=stmt.inserted.last_interacted_at,
            message_count=stmt.inserted.message_count,
            updated_timestamp=text('CURRENT_TIMESTAMP')
        )

        conn.execute(stmt)

        # 导入消息数据
        messages = conversation.get('messages', [])
        message_count = 0

        for order, message in enumerate(messages, 1):
            try:
                imported = self._import_message(conn, conversation['id'], message, order)
                message_count += imported
            except Exception as e:
                logger.error(f"导入消息失败 (对话: {conversation['id']}, 序号: {order}): {e}")
                # 继续处理下一条消息
                continue

        return message_count

    def _import_message(self, conn, conversation_id: str, message: Dict[str, Any], order: int) -> int:
        """导入单条消息数据，返回导入的消息数量"""
        request_message = message.get('request_message', '')
        response_text = message.get('response_text', '')
        imported_count = 0

        # 导入用户消息
        if request_message:
            imported_count += self._insert_single_message(
                conn, conversation_id, message, 'user', request_message, order * 2 - 1, True
            )

        # 导入AI响应消息
        if response_text:
            imported_count += self._insert_single_message(
                conn, conversation_id, message, 'assistant', response_text, order * 2, False
            )

        return imported_count

    def _insert_single_message(self, conn, conversation_id: str, message: Dict[str, Any],
                              role: str, content: str, message_order: int, include_files: bool) -> int:
        """插入单条消息到数据库"""
        timestamp = self._parse_timestamp(message.get('timestamp', ''))
        workspace_files_data = None

        if include_files and message.get('workspace_files'):
            workspace_files_data = message.get('workspace_files', [])

        messages_table = self.tables['messages']
        stmt = insert(messages_table).values(
            conversation_id=conversation_id,
            request_id=message.get('request_id', ''),
            role=role,
            content=content,
            timestamp=timestamp,
            message_order=message_order,
            workspace_files=workspace_files_data
        )

        # 添加 ON DUPLICATE KEY UPDATE 子句
        stmt = stmt.on_duplicate_key_update(
            content=stmt.inserted.content,
            timestamp=stmt.inserted.timestamp,
            workspace_files=stmt.inserted.workspace_files
        )

        try:
            conn.execute(stmt)
            return 1
        except Exception as e:
            logger.error(f"插入{role}消息失败: {e}")
            logger.error(f"消息内容长度: {len(content)}")
            logger.error(f"消息内容预览: {content[:100]}...")
            return 0

    def _parse_timestamp(self, timestamp_str: str) -> Optional[str]:
        """解析时间戳字符串为MySQL可接受的格式"""
        if not timestamp_str:
            return None

        try:
            # 处理ISO格式的时间戳
            if 'T' in timestamp_str:
                # 移除Z后缀并解析
                clean_timestamp = timestamp_str.replace('Z', '').split('.')[0]
                dt = datetime.fromisoformat(clean_timestamp)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return timestamp_str
        except Exception as e:
            logger.warning(f"解析时间戳失败: {timestamp_str}, 错误: {e}")
            return None

    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            self.engine = None
            self.connection = None
            logger.info("数据库连接已关闭")

class AugmentStorageLocator:
    """定位 VS Code 中的 Augment Code 存储位置"""

    def __init__(self):
        self.home = pathlib.Path.home()
        self.system = platform.system()

    def get_vscode_root(self) -> pathlib.Path:
        """获取 VS Code 根目录"""
        if self.system == "Darwin":   # macOS
            return self.home / "Library" / "Application Support" / "Code"
        elif self.system == "Windows":  # Windows
            return self.home / "AppData" / "Roaming" / "Code"
        elif self.system == "Linux":    # Linux
            return self.home / ".config" / "Code"
        else:
            raise RuntimeError(f"Unsupported OS: {self.system}")

    def get_workspace_storage_paths(self) -> List[tuple]:
        """获取所有工作区存储路径"""
        workspace_storage = self.get_vscode_root() / "User" / "workspaceStorage"
        if not workspace_storage.exists():
            logger.warning(f"工作区存储目录不存在: {workspace_storage}")
            return []

        workspaces = []
        for folder in workspace_storage.iterdir():
            if folder.is_dir() and folder.name != 'ext-dev':
                db_path = folder / "state.vscdb"
                if db_path.exists():
                    workspaces.append((folder.name, db_path))

        logger.info(f"找到 {len(workspaces)} 个工作区")
        return workspaces

class AugmentDataExtractor:
    """从数据库中提取 Augment Code 数据"""

    def __init__(self, db_path: pathlib.Path, workspace_id: str):
        self.db_path = db_path
        self.workspace_id = workspace_id
        self.augment_dir = self.db_path.parent / "Augment.vscode-augment"

    def extract_project_info(self) -> Dict[str, str]:
        """提取项目信息"""
        project_info = {"name": "Unknown", "path": "Unknown"}

        try:
            # 尝试从 workspace.json 获取项目路径
            workspace_json = self.db_path.parent / "workspace.json"
            if workspace_json.exists():
                with open(workspace_json, 'r') as f:
                    data = json.load(f)

                folder = data.get('folder', '')
                if folder and folder.startswith('file://'):
                    folder = folder[7:]
                    project_info["path"] = folder
                    project_info["name"] = os.path.basename(folder)
                    logger.info(f"从 workspace.json 提取项目信息: {project_info}")
        except Exception as e:
            logger.error(f"提取项目信息时出错: {e}")

        return project_info

    def has_memories_file(self) -> Tuple[bool, str]:
        """检查是否存在 Augment-Memories 文件，并返回其内容

        Returns:
            Tuple[bool, str]: (是否存在且不为空, 文件内容)
        """
        memories_path = self.augment_dir / "Augment-Memories"
        if memories_path.exists() and memories_path.is_file():
            try:
                content = memories_path.read_text(encoding='utf-8')
                if content.strip():
                    logger.info(f"找到非空的 Augment-Memories 文件: {memories_path}")
                    return True, content
                else:
                    logger.info(f"找到空的 Augment-Memories 文件: {memories_path}")
                    return False, ""
            except Exception as e:
                logger.error(f"读取 Augment-Memories 文件时出错: {e}")
                return False, ""
        else:
            logger.info(f"未找到 Augment-Memories 文件: {memories_path}")
            return False, ""

    def extract_augment_data(self) -> Optional[List[Dict[str, Any]]]:
        """提取 Augment Code 数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询 memento/webviewView.augment-chat 键
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()

            if not result:
                logger.info(f"工作区 {self.workspace_id} 中没有找到 Augment Code 数据")
                return None

            # 解析 JSON 数据
            data = json.loads(result[0])
            webview_state = json.loads(data.get("webviewState", "{}"))

            # 提取会话数据
            conversations = webview_state.get("conversations", {})
            logger.info(f"工作区 {self.workspace_id} 中找到 {len(conversations)} 个会话")

            # 结构化会话数据
            structured_conversations = []
            for conv_id, conv_data in conversations.items():
                structured_conv = {
                    "id": conv_id,
                    "name": conv_data.get("name", ""),
                    "created_at": conv_data.get("createdAtIso", ""),
                    "last_interacted_at": conv_data.get("lastInteractedAtIso", ""),
                    "messages": []
                }

                # 提取消息
                for msg in conv_data.get("chatHistory", []):
                    structured_msg = {
                        "request_id": msg.get("request_id", ""),
                        "request_message": msg.get("request_message", ""),
                        "response_text": msg.get("response_text", ""),
                        "timestamp": msg.get("timestamp", ""),
                        "workspace_files": []
                    }

                    # 提取相关文件
                    for file_chunk in msg.get("workspace_file_chunks", []):
                        file_info = file_chunk.get("file", {})
                        structured_msg["workspace_files"].append({
                            "repo_root": file_info.get("repoRoot", ""),
                            "path_name": file_info.get("pathName", ""),
                            "full_range": file_info.get("fullRange", {})
                        })

                    structured_conv["messages"].append(structured_msg)

                structured_conversations.append(structured_conv)

            conn.close()
            return structured_conversations

        except Exception as e:
            logger.error(f"提取 Augment 数据时出错: {e}")
            return None

class AugmentProject:
    """表示一个包含 Augment Code 数据的项目"""

    def __init__(self, name: str, path: str, workspace_id: str, conversations: List[Dict[str, Any]], memories_content: str = ""):
        self.name = name
        self.path = path
        self.workspace_id = workspace_id
        self.conversations = conversations
        self.memories_content = memories_content

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "path": self.path,
            "workspace_id": self.workspace_id,
            "conversations": self.conversations
        }

    def export_to_markdown(self, output_dir: pathlib.Path) -> List[str]:
        """将项目的会话导出为 Markdown 文件

        Args:
            output_dir: 输出目录路径

        Returns:
            List[str]: 导出的文件路径列表
        """
        if not self.conversations and not self.memories_content:
            logger.warning(f"项目 '{self.name}' 没有会话数据和记忆数据")
            return []

        project_name = self._get_display_name()
        project_dir = output_dir / sanitize_filename(project_name)
        project_dir.mkdir(exist_ok=True)

        exported_files = []

        # 导出记忆文件
        if self.memories_content:
            exported_files.extend(self._export_memories(project_dir, project_name))

        # 导出会话文件
        if self.conversations:
            exported_files.extend(self._export_conversations(project_dir, project_name))

        return exported_files

    def _get_display_name(self) -> str:
        """获取项目显示名称"""
        return self.name if self.name and self.name != "Unknown" else self.workspace_id

    def _export_memories(self, project_dir: pathlib.Path, project_name: str) -> List[str]:
        """导出记忆文件"""
        memories_path = project_dir / "Memories.md"
        with open(memories_path, 'w', encoding='utf-8') as f:
            f.write(f"# {project_name} 的记忆\n\n")
            f.write(self.memories_content)

        logger.info(f"已导出记忆文件: {memories_path}")
        return [str(memories_path)]

    def _export_conversations(self, project_dir: pathlib.Path, project_name: str) -> List[str]:
        """导出所有会话文件"""
        exported_files = []

        for conversation in self.conversations:
            if not conversation.get("messages"):
                continue

            conv_name = get_conversation_name(conversation)
            md_path = project_dir / f"{sanitize_filename(conv_name)}.md"

            markdown_content = self._generate_conversation_markdown(conversation, conv_name, project_name)

            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            exported_files.append(str(md_path))
            logger.info(f"已导出: {md_path}")

        return exported_files

    def _generate_conversation_markdown(self, conversation: Dict[str, Any], conv_name: str, project_name: str) -> str:
        """生成会话的 Markdown 内容"""
        # 生成头部信息
        markdown_content = f"# {conv_name}\n\n"
        markdown_content += f"**项目**: {project_name}\n\n"
        markdown_content += f"**创建时间**: {format_datetime(conversation.get('created_at', ''))}\n\n"
        markdown_content += f"**最后交互**: {format_datetime(conversation.get('last_interacted_at', ''))}\n\n"
        markdown_content += f"**对话ID**: {conversation.get('id', 'Unknown')}\n\n"
        markdown_content += "---\n\n"

        # 生成消息内容
        message_groups = self._group_messages(conversation.get("messages", []))
        for i, group in enumerate(message_groups, 1):
            if group:
                markdown_content += self._format_message_group(group, i)

        return markdown_content

    def _group_messages(self, messages: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """将消息按请求分组"""
        message_groups = []
        current_group = []

        for message in messages:
            # 如果是新的用户请求（request_message 不为空），开始一个新组
            if message.get("request_message"):
                if current_group:
                    message_groups.append(current_group)
                current_group = [message]
            # 否则，添加到当前组（AI 响应或续写）
            else:
                current_group.append(message)

        # 添加最后一组
        if current_group:
            message_groups.append(current_group)

        return message_groups

    def _format_message_group(self, group: List[Dict[str, Any]], group_index: int) -> str:
        """格式化消息组为 Markdown"""
        content = f"### 对话 {group_index}\n\n"

        # 处理用户请求
        user_messages = [msg for msg in group if msg.get("request_message")]
        for msg in user_messages:
            timestamp = msg.get("timestamp", "")
            formatted_time = format_datetime(timestamp)
            time_str = f" ({formatted_time})" if formatted_time != "Unknown" else ""
            content += f"> **👤 用户**{time_str}\n\n{msg.get('request_message')}\n\n"

        # 处理 AI 响应
        ai_messages = [msg for msg in group if msg.get("response_text")]
        if ai_messages:
            # 只获取第一条 AI 响应的时间戳
            first_timestamp = ai_messages[0].get("timestamp", "")
            formatted_time = format_datetime(first_timestamp)
            time_str = f" ({formatted_time})" if formatted_time != "Unknown" else ""

            # 合并所有 AI 响应
            combined_response = ""
            for msg in ai_messages:
                combined_response += msg.get("response_text", "") + "\n\n"

            # 只显示一次 Augment AI 标题
            content += f"> **🤖 Augment**{time_str}\n\n{combined_response}"

        content += "---\n\n"
        return content

def find_augment_projects() -> List[AugmentProject]:
    """查找所有包含 Augment Code 数据的项目"""
    locator = AugmentStorageLocator()
    workspaces = locator.get_workspace_storage_paths()

    projects = []
    for workspace_id, db_path in workspaces:
        extractor = AugmentDataExtractor(db_path, workspace_id)
        project_info = extractor.extract_project_info()
        conversations = extractor.extract_augment_data()

        # 检查是否有记忆文件
        has_memories, memories_content = extractor.has_memories_file()

        # 如果有会话数据或记忆内容，创建项目
        if conversations or has_memories:
            project = AugmentProject(
                name=project_info["name"],
                path=project_info["path"],
                workspace_id=workspace_id,
                conversations=conversations or [],
                memories_content=memories_content
            )
            projects.append(project)

    return projects

# 工具函数
def sanitize_filename(name: str) -> str:
    """将项目名称转换为有效的文件名"""
    if not name:
        return "unknown_project"

    # 移除非法字符，只保留字母、数字、下划线、连字符和点
    sanitized = re.sub(r'[^\w\-\.]', '_', name)
    return sanitized if sanitized else "unknown_project"

def format_datetime(timestamp: str, format_str: str = DATETIME_FORMAT) -> str:
    """格式化 ISO 格式的时间戳

    Args:
        timestamp: ISO 格式的时间戳
        format_str: 输出格式

    Returns:
        str: 格式化后的时间字符串
    """
    if not timestamp:
        return "Unknown"
    try:
        dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
        return dt.strftime(format_str)
    except Exception:
        return timestamp

def get_conversation_name(conversation: Dict[str, Any]) -> str:
    """获取会话名称，如果没有则根据创建时间生成"""
    conv_name = conversation.get("name", "")
    if conv_name:
        return conv_name

    # 使用创建时间作为名称
    created_at = conversation.get("created_at", "")
    if created_at:
        try:
            dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
            return dt.strftime("%Y-%m-%d_%H-%M-%S")
        except Exception:
            pass

    return "unnamed_conversation"

def calculate_project_stats(projects: List['AugmentProject']) -> Dict[str, int]:
    """计算项目统计信息"""
    total_conversations = sum(len(project.conversations) for project in projects)
    total_messages = sum(
        sum(len(conv["messages"]) for conv in project.conversations)
        for project in projects
    )
    return {
        'projects': len(projects),
        'conversations': total_conversations,
        'messages': total_messages
    }

def create_markdown_index(output_dir: pathlib.Path, projects: List[AugmentProject]) -> None:
    """创建 Markdown 索引文件

    Args:
        output_dir: 输出目录路径
        projects: 项目列表
    """
    index_path = output_dir / "index.md"
    stats = calculate_project_stats(projects)

    with open(index_path, 'w', encoding='utf-8') as f:
        _write_index_header(f, stats)
        _write_project_summary_table(f, projects)
        _write_detailed_conversation_list(f, projects)

def _write_index_header(f, stats: Dict[str, int]):
    """写入索引文件头部"""
    f.write("# Augment Code 聊天历史索引\n\n")
    f.write(f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
    f.write("## 统计信息\n\n")
    f.write(f"- 项目数量: {stats['projects']}\n")
    f.write(f"- 会话数量: {stats['conversations']}\n")
    f.write(f"- 消息数量: {stats['messages']}\n\n")

def _write_project_summary_table(f, projects: List[AugmentProject]):
    """写入项目汇总表格"""
    f.write("## 项目列表\n\n")
    f.write("| 项目名称 | 会话数量 | 最后交互时间 | 记忆文件 |\n")
    f.write("|---------|----------|--------------|----------|\n")

    for project in projects:
        project_name = project._get_display_name()
        safe_name = sanitize_filename(project_name)

        # 获取最后交互时间
        last_interacted_at = _get_project_last_interaction(project)

        # 检查是否有记忆文件
        has_memories = "✅" if project.memories_content else "❌"

        # 写入项目行
        f.write(f"| [{project_name}]({safe_name}/) | {len(project.conversations)} | {last_interacted_at} | {has_memories} |\n")

    f.write("\n")

def _get_project_last_interaction(project: AugmentProject) -> str:
    """获取项目最后交互时间"""
    if not project.conversations:
        return "Unknown"

    all_times = [conv.get("last_interacted_at", "") for conv in project.conversations if conv.get("last_interacted_at")]
    return format_datetime(max(all_times)) if all_times else "Unknown"

def _write_detailed_conversation_list(f, projects: List[AugmentProject]):
    """写入详细会话列表"""
    f.write("## 详细会话列表\n\n")

    for project in projects:
        project_name = project._get_display_name()
        safe_name = sanitize_filename(project_name)

        f.write(f"### {project_name}\n\n")

        # 如果有记忆文件，添加链接
        if project.memories_content:
            f.write(f"[📝 记忆文件]({safe_name}/Memories.md)\n\n")

        # 如果没有会话，显示提示信息
        if not project.conversations:
            f.write("*该项目没有会话数据*\n\n")
            continue

        _write_conversation_table(f, project, safe_name)

def _write_conversation_table(f, project: AugmentProject, safe_name: str):
    """写入会话表格"""
    f.write("| 会话名称 | 创建时间 | 最后交互时间 | 消息数量 |\n")
    f.write("|---------|----------|--------------|----------|\n")

    for conv in project.conversations:
        conv_name = get_conversation_name(conv)
        safe_conv_name = sanitize_filename(conv_name)

        # 格式化时间
        created_at = format_datetime(conv.get("created_at", ""))
        last_interacted_at = format_datetime(conv.get("last_interacted_at", ""))

        # 计算消息数量
        msg_count = len(conv.get("messages", []))

        # 写入会话行
        f.write(f"| [{conv_name}]({safe_name}/{safe_conv_name}.md) | {created_at} | {last_interacted_at} | {msg_count} |\n")

    f.write("\n")

def save_project_to_json(project: AugmentProject, projects_dir: pathlib.Path) -> str:
    """将单个项目数据保存为 JSON 文件"""
    # 创建安全的文件名
    safe_name = sanitize_filename(project.name)
    file_path = projects_dir / f"{safe_name}.json"

    # 保存数据
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(project.to_dict(), f, ensure_ascii=False, indent=2)

    return str(file_path)

def save_to_json(projects: List[AugmentProject], output_file: str, save_individual: bool = True, json_dir: pathlib.Path = None) -> Tuple[str, List[str]]:
    """将项目数据保存为 JSON 文件

    Args:
        projects: 项目列表
        output_file: 汇总文件路径
        save_individual: 是否按项目分别保存
        json_dir: JSON 文件保存目录

    Returns:
        Tuple[str, List[str]]: 汇总文件路径和各项目文件路径列表
    """
    individual_files = []

    # 按项目分别保存
    if save_individual:
        # 确保保存目录存在
        projects_dir = json_dir if json_dir else pathlib.Path("projects")
        projects_dir.mkdir(exist_ok=True, parents=True)

        # 保存各项目数据
        for project in projects:
            file_path = save_project_to_json(project, projects_dir)
            individual_files.append(file_path)
            logger.info(f"项目 '{project.name}' 的数据已保存到 {file_path}")

    # 保存汇总数据
    if output_file:
        # 确保输出目录存在
        output_path = pathlib.Path(output_file)
        output_path.parent.mkdir(exist_ok=True, parents=True)

        data = [project.to_dict() for project in projects]
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"汇总数据已保存到 {output_file}")

    return output_file, individual_files

def save_to_database(projects: List[AugmentProject], db_config: Dict[str, Any]) -> None:
    """将项目数据保存到数据库

    Args:
        projects: 项目列表
        db_config: 数据库配置字典
    """
    if not SQLALCHEMY_AVAILABLE:
        logger.error("SQLAlchemy 或 PyMySQL 未安装。请运行: pip install sqlalchemy pymysql")
        return

    db_manager = DatabaseManager(
        host=db_config.get('host', 'localhost'),
        port=db_config.get('port', 3306),
        user=db_config.get('user', 'root'),
        password=db_config.get('password', ''),
        database=db_config.get('database', 'augment_chat')
    )

    try:
        # 连接数据库
        db_manager.connect()

        # 创建表
        db_manager.create_tables()

        # 导入数据
        db_manager.import_projects(projects)

        # 输出统计信息
        total_conversations = sum(len(project.conversations) for project in projects)
        total_messages = sum(
            sum(len(conv["messages"]) for conv in project.conversations)
            for project in projects
        )
        logger.info(f"数据库导入完成: {len(projects)} 个项目, {total_conversations} 个会话, {total_messages} 条消息")

    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        raise
    finally:
        db_manager.close()

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="提取 Augment Code 扩展的历史记录数据")

    # 基本参数
    parser.add_argument("--output", "-o", default=None, help="汇总数据输出文件路径")
    parser.add_argument("--no-summary", action="store_true", help="不生成汇总数据文件")
    parser.add_argument("--no-individual", action="store_true", help="不按项目分别保存数据")
    parser.add_argument("--clean", action="store_true", help="清理输出目录后再保存")
    parser.add_argument("--format", "-f", choices=DEFAULT_OUTPUT_FORMATS, default="json", help="输出格式 (json, md 或 db)")
    parser.add_argument("--history-dir", default=DEFAULT_HISTORY_DIR, help="历史记录根目录 (默认: ./history)")

    # 数据库相关参数
    parser.add_argument("--db-host", default=DEFAULT_DB_CONFIG['host'], help="数据库主机地址")
    parser.add_argument("--db-port", type=int, default=DEFAULT_DB_CONFIG['port'], help="数据库端口")
    parser.add_argument("--db-user", default=DEFAULT_DB_CONFIG['user'], help="数据库用户名")
    parser.add_argument("--db-password", default=DEFAULT_DB_CONFIG['password'], help="数据库密码")
    parser.add_argument("--db-database", default=DEFAULT_DB_CONFIG['database'], help="数据库名称")

    return parser.parse_args()

def handle_database_mode(projects: List[AugmentProject], args: argparse.Namespace):
    """处理数据库模式"""
    db_config = {
        'host': args.db_host,
        'port': args.db_port,
        'user': args.db_user,
        'password': args.db_password,
        'database': args.db_database
    }

    logger.info(f"导入数据到数据库: {args.db_host}:{args.db_port}/{args.db_database}")
    save_to_database(projects, db_config)

def handle_file_mode(projects: List[AugmentProject], args: argparse.Namespace):
    """处理文件模式（JSON 或 Markdown）"""
    # 创建目录结构
    history_dir = pathlib.Path(args.history_dir)
    history_dir.mkdir(exist_ok=True, parents=True)

    output_subdir = "markdown" if args.format == "md" else "json"
    format_dir = history_dir / output_subdir
    format_dir.mkdir(exist_ok=True, parents=True)

    # 清理输出目录
    if args.clean and not args.no_individual:
        _clean_output_directory(format_dir)

    # 根据格式导出数据
    if args.format == "md":
        _handle_markdown_export(projects, format_dir)
    else:  # json
        _handle_json_export(projects, args, format_dir)

def _clean_output_directory(format_dir: pathlib.Path):
    """清理输出目录"""
    if format_dir.exists():
        logger.info(f"清理 {format_dir} 目录")
        for item in format_dir.iterdir():
            if item.is_file():
                item.unlink()
            elif item.is_dir():
                shutil.rmtree(item)

def _handle_markdown_export(projects: List[AugmentProject], format_dir: pathlib.Path):
    """处理 Markdown 导出"""
    logger.info(f"导出 Markdown 到 {format_dir}...")

    md_files = []
    for project in projects:
        project_files = project.export_to_markdown(format_dir)
        md_files.extend(project_files)

    # 创建索引文件
    if md_files:
        create_markdown_index(format_dir, projects)
        logger.info(f"已创建 Markdown 索引文件: {format_dir / 'index.md'}")

    logger.info(f"已导出 {len(md_files)} 个 Markdown 文件到 {format_dir}")

def _handle_json_export(projects: List[AugmentProject], args: argparse.Namespace, format_dir: pathlib.Path):
    """处理 JSON 导出"""
    # 确定输出文件路径
    output_file = None
    if not args.no_summary:
        default_path = format_dir / "augment_data.json"
        output_file = str(default_path) if args.output is None else args.output

    save_individual = not args.no_individual

    # 保存数据
    _, individual_files = save_to_json(
        projects,
        output_file=output_file,
        save_individual=save_individual,
        json_dir=format_dir
    )

    # 输出统计信息
    if save_individual:
        logger.info(f"已将 {len(individual_files)} 个项目的数据分别保存到 {format_dir}")

def main():
    """主函数"""
    args = parse_arguments()

    # 提取项目数据
    projects = find_augment_projects()
    logger.info(f"找到 {len(projects)} 个包含 Augment Code 数据的项目")

    # 根据格式处理数据
    if args.format == "db":
        handle_database_mode(projects, args)
    else:
        handle_file_mode(projects, args)

    # 输出最终统计信息
    stats = calculate_project_stats(projects)
    logger.info(f"统计信息: {stats['projects']} 个项目, {stats['conversations']} 个会话, {stats['messages']} 条消息")

if __name__ == "__main__":
    main()
