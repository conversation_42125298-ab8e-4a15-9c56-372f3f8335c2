# Augment Code 数据库导入功能

## 概述

`extract_augment_chat.py` 脚本现在支持将 Augment Code 的聊天历史记录直接导入到 MySQL 数据库中。这个功能通过新增的 `db` 格式选项实现。

## 功能特性

- 自动创建数据库和表结构
- 支持数据的增量更新（使用 `ON DUPLICATE KEY UPDATE`）
- 将用户消息和AI响应分别存储为不同的记录
- 工作区文件信息以JSON格式存储在消息表中
- 完整的错误处理和事务支持

## 数据库表结构

### 1. projects 表
存储项目基本信息：
- `id`: 自增主键
- `workspace_id`: 工作区ID
- `platform`: 平台名称（固定为 'augment_code'）
- `name`: 项目名称
- `path`: 项目路径
- `created_at`, `updated_at`: 创建和更新时间

### 2. conversations 表
存储对话信息：
- `id`: 对话ID（主键）
- `workspace_id`: 工作区ID
- `project_name`: 项目名称
- `name`: 对话名称
- `created_at`: 对话创建时间
- `last_interacted_at`: 最后交互时间
- `message_count`: 消息数量

### 3. messages 表
存储消息内容：
- `id`: 自增主键
- `conversation_id`: 对话ID
- `request_id`: 请求ID
- `role`: 角色（'user' 或 'assistant'）
- `content`: 消息内容
- `timestamp`: 消息时间戳
- `message_order`: 消息顺序
- `workspace_files`: 工作区文件信息（JSON格式）

## 安装依赖

在使用数据库功能之前，需要安装 MySQL 连接器：

```bash
pip install mysql-connector-python
```

## 使用方法

### 基本用法

```bash
# 导入到本地MySQL数据库（使用默认配置）
python extract_augment_chat.py --format db

# 指定数据库连接参数
python extract_augment_chat.py --format db \
    --db-host localhost \
    --db-port 3306 \
    --db-user root \
    --db-password your_password \
    --db-database augment_chat
```

### 命令行参数

#### 数据库相关参数
- `--db-host`: 数据库主机地址（默认: localhost）
- `--db-port`: 数据库端口（默认: 3306）
- `--db-user`: 数据库用户名（默认: root）
- `--db-password`: 数据库密码（默认: 空）
- `--db-database`: 数据库名称（默认: augment_chat）

### 使用示例

#### 示例1：使用默认配置
```bash
python extract_augment_chat.py --format db
```

#### 示例2：连接远程数据库
```bash
python extract_augment_chat.py --format db \
    --db-host ************* \
    --db-user augment_user \
    --db-password secret123 \
    --db-database my_augment_db
```

#### 示例3：连接云数据库
```bash
python extract_augment_chat.py --format db \
    --db-host your-cloud-db.amazonaws.com \
    --db-port 3306 \
    --db-user admin \
    --db-password your_secure_password \
    --db-database augment_production
```

## 数据导入逻辑

### 消息处理
脚本会将原始的 Augment Code 消息数据转换为标准化的用户-助手对话格式：

1. **用户消息**: 当 `request_message` 不为空时，创建一个 `role='user'` 的记录
2. **助手消息**: 当 `response_text` 不为空时，创建一个 `role='assistant'` 的记录
3. **消息顺序**: 用户消息使用奇数序号，助手消息使用偶数序号

### 工作区文件
工作区文件信息以JSON格式存储在 `workspace_files` 字段中：

```json
[
    {
        "repo_root": "/path/to/project",
        "path_name": "src/main.py",
        "full_range": {
            "startLineNumber": 1,
            "startColumn": 0,
            "endLineNumber": 50,
            "endColumn": 0
        }
    }
]
```

### 时间戳处理
脚本会自动将 ISO 格式的时间戳转换为 MySQL 兼容的格式：
- 输入: `2025-01-15T14:30:00.123Z`
- 输出: `2025-01-15 14:30:00`

## 查询示例

### 获取所有项目
```sql
SELECT * FROM projects WHERE platform = 'augment_code';
```

### 获取项目的对话列表
```sql
SELECT c.* FROM conversations c
JOIN projects p ON c.workspace_id = p.workspace_id
WHERE p.name = 'your_project_name'
ORDER BY c.last_interacted_at DESC;
```

### 获取对话的消息
```sql
SELECT role, content, timestamp, message_order
FROM messages
WHERE conversation_id = 'your_conversation_id'
ORDER BY message_order;
```

### 搜索包含特定关键词的消息
```sql
SELECT p.name as project_name, m.role, m.content, m.timestamp
FROM messages m
JOIN conversations c ON m.conversation_id = c.id
JOIN projects p ON c.workspace_id = p.workspace_id
WHERE m.content LIKE '%关键词%'
ORDER BY m.timestamp DESC;
```

## 注意事项

1. **数据库权限**: 确保数据库用户有创建数据库和表的权限
2. **字符编码**: 表使用 `utf8mb4` 编码，支持完整的 Unicode 字符集
3. **重复导入**: 脚本支持重复运行，会自动更新已存在的数据
4. **事务安全**: 所有数据库操作都在事务中进行，确保数据一致性
5. **连接管理**: 脚本会自动管理数据库连接的打开和关闭

## 故障排除

### 常见错误

1. **MySQL connector 未安装**
   ```
   错误: MySQL connector 未安装。请运行: pip install mysql-connector-python
   ```
   解决: 安装 MySQL 连接器

2. **数据库连接失败**
   ```
   错误: 数据库连接失败: Access denied for user 'root'@'localhost'
   ```
   解决: 检查数据库用户名和密码

3. **权限不足**
   ```
   错误: Access denied for user 'user'@'host' to database 'augment_chat'
   ```
   解决: 确保用户有创建数据库和表的权限

### 调试建议

1. 使用 `--db-password` 参数时，注意特殊字符的转义
2. 检查防火墙设置，确保数据库端口可访问
3. 验证数据库服务是否正在运行
4. 查看脚本输出的详细日志信息
