# Augment Code 历史记录提取工具

这个工具用于从 VS Code 的工作区存储中提取 Augment Code 扩展的聊天历史记录数据，包括项目名称、项目路径、工作区 ID 和会话数据。

## 功能特点

- 自动查找所有包含 Augment Code 数据的 VS Code 工作区
- 提取项目信息（名称、路径、工作区 ID）
- 提取会话数据（会话 ID、名称、创建时间、最后交互时间）
- 提取消息数据（请求 ID、请求消息、响应文本、时间戳、相关文件）
- 将提取的数据保存为结构化的 JSON 文件或 Markdown 文件
- 支持按项目分别保存数据到独立的文件
- 支持生成包含所有项目数据的汇总文件
- 支持自定义输出目录结构

## 使用方法

1. 确保已安装 Python 3.6 或更高版本
2. 运行脚本：

```bash
python extract_augment_chat.py
```

默认情况下，脚本会：
1. 创建 `history/json` 目录结构
2. 将所有项目的汇总数据保存到 `history/json/augment_data.json` 文件中
3. 将各项目的数据分别保存到 `history/json` 目录下，文件名基于项目名称

### 命令行参数

脚本支持以下命令行参数：

```bash
# 指定汇总文件的路径
python extract_augment_chat.py --output /path/to/output.json

# 不生成汇总文件，只按项目分别保存
python extract_augment_chat.py --no-summary

# 只生成汇总文件，不按项目分别保存
python extract_augment_chat.py --no-individual

# 清理输出目录后再保存
python extract_augment_chat.py --clean

# 选择输出格式（json 或 md）
python extract_augment_chat.py --format md

# 指定历史记录根目录
python extract_augment_chat.py --history-dir custom/path
```

这些参数可以组合使用，例如：

```bash
# 清理输出目录，并导出为 Markdown 格式
python extract_augment_chat.py --clean --format md

# 指定历史记录根目录，并导出为 JSON 格式
python extract_augment_chat.py --history-dir data/history --format json
```

## 输出格式

### JSON 格式

提取的数据将保存为 JSON 格式，结构如下：

```json
[
  {
    "name": "项目名称",
    "path": "项目路径",
    "workspace_id": "工作区ID",
    "conversations": [
      {
        "id": "会话ID",
        "name": "会话名称",
        "created_at": "创建时间",
        "last_interacted_at": "最后交互时间",
        "messages": [
          {
            "request_id": "请求ID",
            "request_message": "用户消息",
            "response_text": "AI响应",
            "timestamp": "时间戳",
            "workspace_files": [
              {
                "repo_root": "仓库根目录",
                "path_name": "文件路径",
                "full_range": {
                  "startLineNumber": 起始行号,
                  "startColumn": 起始列号,
                  "endLineNumber": 结束行号,
                  "endColumn": 结束列号
                }
              }
            ]
          }
        ]
      }
    ]
  }
]
```

### Markdown 格式

导出的 Markdown 文件将按项目和会话组织，每个会话生成一个独立的 Markdown 文件，格式如下：

```markdown
# 会话名称

**项目**: 项目名称

**创建时间**: YYYY年MM月DD日 HH:MM

**最后交互**: YYYY年MM月DD日 HH:MM

**对话ID**: 会话ID

---

### 对话 1

> **👤 用户** (YYYY年MM月DD日 HH:MM)

用户消息内容

> **🤖 Augment** (YYYY年MM月DD日 HH:MM)

AI响应内容

---

### 对话 2

...
```

Markdown 格式的特点：
- 按项目和会话组织文件结构
- 每个会话生成一个独立的 Markdown 文件
- 将连续的 AI 响应合并为一个响应
- 显示用户和 AI 的图标
- 显示消息的时间戳

## 目录结构

脚本将创建以下目录结构：

```
项目根目录/
└── history/                # 历史记录根目录
    ├── markdown/           # Markdown 格式输出目录
    │   ├── 项目1/          # 按项目名称组织
    │   │   ├── 会话1.md    # 每个会话一个文件
    │   │   └── 会话2.md
    │   └── 项目2/
    │       └── 会话1.md
    └── json/               # JSON 格式输出目录
        ├── augment_data.json  # 汇总文件
        ├── 项目1.json      # 每个项目一个文件
        └── 项目2.json
```

## 系统要求

- Python 3.6+
- 操作系统：Windows、macOS 或 Linux
- VS Code 安装了 Augment Code 扩展

## 注意事项

- 脚本需要访问 VS Code 的工作区存储目录，该目录通常位于：
  - Windows: `%APPDATA%\Code\User\workspaceStorage`
  - macOS: `~/Library/Application Support/Code/User/workspaceStorage`
  - Linux: `~/.config/Code/User/workspaceStorage`
- 脚本只会提取已安装 Augment Code 扩展的工作区数据
- 默认情况下，输出文件将保存在项目根目录下的 `history` 目录中
- 可以使用 `--format` 参数选择输出格式（`json` 或 `md`）
- 可以使用 `--history-dir` 参数自定义历史记录根目录
