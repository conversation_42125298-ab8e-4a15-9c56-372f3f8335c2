#!/usr/bin/env python3
"""
test_db_import.py - 测试数据库导入功能

这个脚本用于测试 extract_augment_chat.py 的数据库导入功能。
"""

import sys
import os
import subprocess
import tempfile
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_db_import():
    """测试数据库导入功能"""
    print("=== Augment Code 数据库导入功能测试 ===\n")
    
    # 检查MySQL连接器是否安装
    try:
        import mysql.connector
        print("✅ MySQL connector 已安装")
    except ImportError:
        print("❌ MySQL connector 未安装")
        print("请运行: pip install mysql-connector-python")
        return False
    
    # 测试脚本是否存在
    script_path = Path(__file__).parent / "extract_augment_chat.py"
    if not script_path.exists():
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    print(f"✅ 脚本文件存在: {script_path}")
    
    # 测试帮助信息
    print("\n--- 测试帮助信息 ---")
    try:
        result = subprocess.run([
            sys.executable, str(script_path), "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 帮助信息显示正常")
            # 检查是否包含数据库相关参数
            if "--db-host" in result.stdout and "--format" in result.stdout:
                print("✅ 数据库参数已添加")
            else:
                print("❌ 数据库参数未找到")
                return False
        else:
            print(f"❌ 帮助信息显示失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 帮助信息显示超时")
        return False
    except Exception as e:
        print(f"❌ 运行帮助命令失败: {e}")
        return False
    
    # 测试数据库连接（使用无效配置，应该失败）
    print("\n--- 测试数据库连接错误处理 ---")
    try:
        result = subprocess.run([
            sys.executable, str(script_path),
            "--format", "db",
            "--db-host", "invalid_host",
            "--db-user", "invalid_user",
            "--db-password", "invalid_password"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print("✅ 数据库连接错误处理正常")
            if "数据库连接失败" in result.stderr or "MySQL connector" in result.stderr:
                print("✅ 错误信息正确")
            else:
                print(f"⚠️  错误信息: {result.stderr}")
        else:
            print("⚠️  预期连接失败，但成功了")
    except subprocess.TimeoutExpired:
        print("❌ 数据库连接测试超时")
        return False
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False
    
    # 测试JSON格式（确保原有功能正常）
    print("\n--- 测试JSON格式导出 ---")
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            result = subprocess.run([
                sys.executable, str(script_path),
                "--format", "json",
                "--history-dir", temp_dir,
                "--no-summary"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ JSON格式导出正常")
                # 检查是否生成了文件
                json_dir = Path(temp_dir) / "json"
                if json_dir.exists():
                    print("✅ JSON目录已创建")
                    files = list(json_dir.glob("*.json"))
                    print(f"📁 生成了 {len(files)} 个JSON文件")
                else:
                    print("⚠️  JSON目录未创建（可能没有数据）")
            else:
                print(f"❌ JSON格式导出失败: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("❌ JSON格式导出超时")
            return False
        except Exception as e:
            print(f"❌ JSON格式导出测试失败: {e}")
            return False
    
    print("\n=== 测试完成 ===")
    print("\n📋 测试结果总结:")
    print("✅ MySQL connector 检查通过")
    print("✅ 脚本文件存在")
    print("✅ 帮助信息正常")
    print("✅ 数据库参数已添加")
    print("✅ 数据库错误处理正常")
    print("✅ JSON格式导出正常")
    
    print("\n🎯 下一步操作:")
    print("1. 确保MySQL服务正在运行")
    print("2. 创建数据库用户和权限")
    print("3. 运行实际的数据库导入测试:")
    print(f"   python {script_path} --format db --db-user your_user --db-password your_password")
    
    return True

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("\n1. 导入到本地MySQL（默认配置）:")
    print("   python extract_augment_chat.py --format db")
    
    print("\n2. 指定数据库连接参数:")
    print("   python extract_augment_chat.py --format db \\")
    print("       --db-host localhost \\")
    print("       --db-user root \\")
    print("       --db-password your_password \\")
    print("       --db-database augment_chat")
    
    print("\n3. 连接远程数据库:")
    print("   python extract_augment_chat.py --format db \\")
    print("       --db-host ************* \\")
    print("       --db-user augment_user \\")
    print("       --db-password secret123")
    
    print("\n4. 查看所有可用参数:")
    print("   python extract_augment_chat.py --help")

def main():
    """主函数"""
    print("Augment Code 数据库导入功能测试工具")
    print("=" * 50)
    
    # 运行测试
    success = test_db_import()
    
    if success:
        show_usage_examples()
        print("\n🎉 所有测试通过！数据库导入功能已就绪。")
        return 0
    else:
        print("\n❌ 测试失败，请检查上述错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
