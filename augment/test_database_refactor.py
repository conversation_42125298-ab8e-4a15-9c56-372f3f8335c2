#!/usr/bin/env python3
"""
test_database_refactor.py - 测试重构后的 DatabaseManager 类

这个脚本用于验证重构后的 DatabaseManager 类是否正常工作。
"""

import sys
import logging
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    try:
        from extract_augment_chat import DatabaseManager, SQLALCHEMY_AVAILABLE
        
        if not SQLALCHEMY_AVAILABLE:
            logger.error("SQLAlchemy 或 PyMySQL 未安装。请运行: pip install sqlalchemy pymysql")
            return False
        
        # 创建数据库管理器（使用测试数据库）
        db_manager = DatabaseManager(
            host='localhost',
            port=3306,
            user='root',
            password='',  # 根据实际情况修改
            database='test_augment_chat',
            pool_size=5,
            max_overflow=10
        )
        
        logger.info("正在测试数据库连接...")
        
        # 连接数据库
        db_manager.connect()
        logger.info("✓ 数据库连接成功")
        
        # 创建表
        db_manager.create_tables()
        logger.info("✓ 表创建成功")
        
        # 关闭连接
        db_manager.close()
        logger.info("✓ 数据库连接关闭成功")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据库测试失败: {e}")
        return False

def test_data_import():
    """测试数据导入功能"""
    try:
        from extract_augment_chat import DatabaseManager, AugmentProject
        
        # 创建测试数据
        test_conversations = [
            {
                "id": "test_conv_1",
                "name": "测试对话1",
                "created_at": "2024-01-01T10:00:00Z",
                "last_interacted_at": "2024-01-01T11:00:00Z",
                "messages": [
                    {
                        "request_id": "req_1",
                        "request_message": "测试用户消息",
                        "response_text": "测试AI响应",
                        "timestamp": "2024-01-01T10:30:00Z",
                        "workspace_files": [
                            {
                                "repo_root": "/test/repo",
                                "path_name": "test.py",
                                "full_range": {"start": 1, "end": 10}
                            }
                        ]
                    }
                ]
            }
        ]
        
        test_project = AugmentProject(
            name="测试项目",
            path="/test/project/path",
            workspace_id="test_workspace_123",
            conversations=test_conversations
        )
        
        # 创建数据库管理器
        db_manager = DatabaseManager(
            host='localhost',
            port=3306,
            user='root',
            password='',  # 根据实际情况修改
            database='test_augment_chat'
        )
        
        logger.info("正在测试数据导入...")
        
        # 连接数据库
        db_manager.connect()
        
        # 创建表
        db_manager.create_tables()
        
        # 导入测试数据
        db_manager.import_projects([test_project])
        logger.info("✓ 数据导入成功")
        
        # 关闭连接
        db_manager.close()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据导入测试失败: {e}")
        return False

def test_performance():
    """测试性能相关功能"""
    try:
        from extract_augment_chat import DatabaseManager
        
        logger.info("正在测试连接池性能...")
        
        # 创建多个连接测试连接池
        db_manager = DatabaseManager(
            host='localhost',
            port=3306,
            user='root',
            password='',  # 根据实际情况修改
            database='test_augment_chat',
            pool_size=3,
            max_overflow=5
        )
        
        # 连接数据库
        db_manager.connect()
        
        # 模拟多个并发操作
        for i in range(10):
            with db_manager.engine.connect() as conn:
                result = conn.execute(db_manager.metadata.bind.dialect.statement_compiler(
                    db_manager.metadata.bind.dialect, None
                ).process("SELECT 1 as test_col"))
                logger.info(f"连接 {i+1}: 查询成功")
        
        logger.info("✓ 连接池测试成功")
        
        # 关闭连接
        db_manager.close()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试重构后的 DatabaseManager 类")
    logger.info("=" * 50)
    
    tests = [
        ("数据库连接测试", test_database_connection),
        ("数据导入测试", test_data_import),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n运行测试: {test_name}")
        logger.info("-" * 30)
        
        if test_func():
            passed += 1
            logger.info(f"✓ {test_name} 通过")
        else:
            logger.error(f"✗ {test_name} 失败")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！重构成功！")
        return 0
    else:
        logger.error("❌ 部分测试失败，请检查配置和依赖")
        return 1

if __name__ == "__main__":
    sys.exit(main())
