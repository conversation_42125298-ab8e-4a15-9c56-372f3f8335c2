# Extract Augment Chat - 类结构图

## 📋 概览

重构后的 `extract_augment_chat.py` 包含 4 个主要类和多个工具函数，采用了清晰的分层架构设计。

## 🏗️ 整体架构

```
extract_augment_chat.py
├── 常量定义
├── 核心类 (4个)
├── 工具函数 (8个)
├── 主函数模块 (6个)
└── 入口点
```

## 📦 类结构详图

### 1. DatabaseManager 类
**职责**: MySQL 数据库操作管理

```
DatabaseManager
├── 属性
│   ├── host: str
│   ├── port: int
│   ├── user: str
│   ├── password: str
│   ├── database: str
│   └── connection: mysql.connector.connection
│
├── 公共方法
│   ├── __init__(host, port, user, password, database)
│   ├── connect() -> None
│   ├── create_tables() -> None
│   ├── import_projects(projects: List[AugmentProject]) -> None
│   └── close() -> None
│
└── 私有方法
    ├── _insert_project(cursor, project) -> None
    ├── _import_project_conversations(cursor, project, stats) -> None
    ├── _log_import_results(project_count, stats) -> None
    ├── _import_conversation(cursor, project, conversation) -> int
    ├── _import_message(cursor, conversation_id, message, order) -> int
    ├── _insert_single_message(cursor, conversation_id, message, role, content, message_order, include_files) -> int
    └── _parse_timestamp(timestamp_str) -> Optional[str]
```

### 2. AugmentStorageLocator 类
**职责**: 定位 VS Code 存储位置

```
AugmentStorageLocator
├── 属性
│   ├── home: pathlib.Path
│   └── system: str
│
└── 公共方法
    ├── __init__()
    ├── get_vscode_root() -> pathlib.Path
    └── get_workspace_storage_paths() -> List[tuple]
```

### 3. AugmentDataExtractor 类
**职责**: 从 SQLite 数据库提取 Augment 数据

```
AugmentDataExtractor
├── 属性
│   ├── db_path: pathlib.Path
│   ├── workspace_id: str
│   └── augment_dir: pathlib.Path
│
└── 公共方法
    ├── __init__(db_path, workspace_id)
    ├── extract_project_info() -> Dict[str, str]
    ├── has_memories_file() -> Tuple[bool, str]
    └── extract_augment_data() -> Optional[List[Dict[str, Any]]]
```

### 4. AugmentProject 类
**职责**: 项目数据模型和 Markdown 导出

```
AugmentProject
├── 属性
│   ├── name: str
│   ├── path: str
│   ├── workspace_id: str
│   ├── conversations: List[Dict[str, Any]]
│   └── memories_content: str
│
├── 公共方法
│   ├── __init__(name, path, workspace_id, conversations, memories_content)
│   ├── to_dict() -> Dict[str, Any]
│   └── export_to_markdown(output_dir) -> List[str]
│
└── 私有方法
    ├── _get_display_name() -> str
    ├── _export_memories(project_dir, project_name) -> List[str]
    ├── _export_conversations(project_dir, project_name) -> List[str]
    ├── _generate_conversation_markdown(conversation, conv_name, project_name) -> str
    ├── _group_messages(messages) -> List[List[Dict[str, Any]]]
    └── _format_message_group(group, group_index) -> str
```

## 🔧 工具函数模块

### 核心工具函数
```
工具函数
├── sanitize_filename(name: str) -> str
├── format_datetime(timestamp: str, format_str: str) -> str
├── get_conversation_name(conversation: Dict[str, Any]) -> str
├── calculate_project_stats(projects: List[AugmentProject]) -> Dict[str, int]
├── find_augment_projects() -> List[AugmentProject]
├── save_project_to_json(project: AugmentProject, projects_dir: pathlib.Path) -> str
├── save_to_json(projects, output_file, save_individual, json_dir) -> Tuple[str, List[str]]
└── save_to_database(projects: List[AugmentProject], db_config: Dict[str, Any]) -> None
```

### Markdown 索引生成函数
```
Markdown 索引函数
├── create_markdown_index(output_dir: pathlib.Path, projects: List[AugmentProject]) -> None
├── _write_index_header(f, stats: Dict[str, int]) -> None
├── _write_project_summary_table(f, projects: List[AugmentProject]) -> None
├── _get_project_last_interaction(project: AugmentProject) -> str
├── _write_detailed_conversation_list(f, projects: List[AugmentProject]) -> None
└── _write_conversation_table(f, project: AugmentProject, safe_name: str) -> None
```

## 🎯 主函数模块

### 主控制流程
```
主函数模块
├── main() -> None                                    # 主入口函数
├── parse_arguments() -> argparse.Namespace          # 参数解析
├── handle_database_mode(projects, args) -> None     # 数据库模式处理
├── handle_file_mode(projects, args) -> None         # 文件模式处理
├── _handle_markdown_export(projects, format_dir) -> None    # Markdown 导出
├── _handle_json_export(projects, args, format_dir) -> None  # JSON 导出
└── _clean_output_directory(format_dir) -> None      # 目录清理
```

## 🔗 类之间的关系

### 依赖关系图
```
main()
├── find_augment_projects()
│   ├── AugmentStorageLocator
│   ├── AugmentDataExtractor
│   └── AugmentProject
│
├── handle_database_mode()
│   └── DatabaseManager
│
└── handle_file_mode()
    ├── AugmentProject.export_to_markdown()
    ├── create_markdown_index()
    └── save_to_json()
```

### 数据流向
```
VS Code Storage → AugmentStorageLocator → AugmentDataExtractor → AugmentProject → Output
                                                                                    ├── JSON Files
                                                                                    ├── Markdown Files
                                                                                    └── MySQL Database
```

## 📊 重构改进点

### 1. 职责分离
- **DatabaseManager**: 专注数据库操作
- **AugmentStorageLocator**: 专注存储位置定位
- **AugmentDataExtractor**: 专注数据提取
- **AugmentProject**: 专注数据模型和导出

### 2. 方法分解
- 将大型方法分解为多个小方法
- 每个方法职责单一，易于测试和维护

### 3. 代码复用
- 提取通用工具函数
- 消除重复代码

### 4. 可扩展性
- 清晰的接口设计
- 易于添加新的导出格式或数据源

## 🎨 设计模式

### 使用的设计模式
1. **单一职责原则**: 每个类只负责一个功能领域
2. **开闭原则**: 易于扩展新功能，无需修改现有代码
3. **依赖注入**: 通过参数传递依赖，提高可测试性
4. **模板方法**: 在 AugmentProject 中使用模板方法模式处理导出流程

### 代码质量指标
- ✅ 函数平均长度: < 20 行
- ✅ 类平均方法数: < 15 个
- ✅ 圈复杂度: 大部分函数 < 5
- ✅ 代码重复率: < 5%
