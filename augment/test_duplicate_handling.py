#!/usr/bin/env python3
"""
test_duplicate_handling.py - 测试数据库重复数据处理

这个脚本用于测试数据库导入时的重复数据处理机制。
"""

import sys
import os
import subprocess
import mysql.connector
from mysql.connector import Error

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_duplicate_handling():
    """测试重复数据处理机制"""
    print("=== 测试数据库重复数据处理机制 ===\n")
    
    # 数据库配置（请根据实际情况修改）
    db_config = {
        'host': 'rm-2zeci3z6ogyl025l59o.mysql.rds.aliyuncs.com',
        'port': 3306,
        'user': 'root',
        'password': 'Gj123456',
        'database': 'chat_history'
    }
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        print("✅ 成功连接到数据库")
        
        # 1. 检查当前数据量
        print("\n--- 检查当前数据量 ---")
        
        cursor.execute("SELECT COUNT(*) FROM projects")
        projects_count_before = cursor.fetchone()[0]
        print(f"Projects 表记录数: {projects_count_before}")
        
        cursor.execute("SELECT COUNT(*) FROM conversations")
        conversations_count_before = cursor.fetchone()[0]
        print(f"Conversations 表记录数: {conversations_count_before}")
        
        cursor.execute("SELECT COUNT(*) FROM messages")
        messages_count_before = cursor.fetchone()[0]
        print(f"Messages 表记录数: {messages_count_before}")
        
        # 2. 运行第一次导入
        print("\n--- 运行第一次数据导入 ---")
        result1 = subprocess.run([
            sys.executable, "extract_augment_chat.py", "--format", "db",
            "--db-host", db_config['host'],
            "--db-user", db_config['user'],
            "--db-password", db_config['password'],
            "--db-database", db_config['database']
        ], capture_output=True, text=True, timeout=300)
        
        if result1.returncode == 0:
            print("✅ 第一次导入成功")
        else:
            print(f"❌ 第一次导入失败: {result1.stderr}")
            return False
        
        # 3. 检查第一次导入后的数据量
        print("\n--- 检查第一次导入后的数据量 ---")
        
        cursor.execute("SELECT COUNT(*) FROM projects")
        projects_count_after1 = cursor.fetchone()[0]
        print(f"Projects 表记录数: {projects_count_after1}")
        
        cursor.execute("SELECT COUNT(*) FROM conversations")
        conversations_count_after1 = cursor.fetchone()[0]
        print(f"Conversations 表记录数: {conversations_count_after1}")
        
        cursor.execute("SELECT COUNT(*) FROM messages")
        messages_count_after1 = cursor.fetchone()[0]
        print(f"Messages 表记录数: {messages_count_after1}")
        
        # 4. 运行第二次导入（测试重复数据处理）
        print("\n--- 运行第二次数据导入（测试重复处理）---")
        result2 = subprocess.run([
            sys.executable, "extract_augment_chat.py", "--format", "db",
            "--db-host", db_config['host'],
            "--db-user", db_config['user'],
            "--db-password", db_config['password'],
            "--db-database", db_config['database']
        ], capture_output=True, text=True, timeout=300)
        
        if result2.returncode == 0:
            print("✅ 第二次导入成功")
        else:
            print(f"❌ 第二次导入失败: {result2.stderr}")
            return False
        
        # 5. 检查第二次导入后的数据量
        print("\n--- 检查第二次导入后的数据量 ---")
        
        cursor.execute("SELECT COUNT(*) FROM projects")
        projects_count_after2 = cursor.fetchone()[0]
        print(f"Projects 表记录数: {projects_count_after2}")
        
        cursor.execute("SELECT COUNT(*) FROM conversations")
        conversations_count_after2 = cursor.fetchone()[0]
        print(f"Conversations 表记录数: {conversations_count_after2}")
        
        cursor.execute("SELECT COUNT(*) FROM messages")
        messages_count_after2 = cursor.fetchone()[0]
        print(f"Messages 表记录数: {messages_count_after2}")
        
        # 6. 分析结果
        print("\n--- 重复数据处理结果分析 ---")
        
        projects_diff = projects_count_after2 - projects_count_after1
        conversations_diff = conversations_count_after2 - conversations_count_after1
        messages_diff = messages_count_after2 - messages_count_after1
        
        print(f"Projects 表增量: {projects_diff}")
        print(f"Conversations 表增量: {conversations_diff}")
        print(f"Messages 表增量: {messages_diff}")
        
        # 7. 验证结果
        success = True
        if projects_diff == 0:
            print("✅ Projects 表重复数据处理正确")
        else:
            print("❌ Projects 表出现重复数据")
            success = False
        
        if conversations_diff == 0:
            print("✅ Conversations 表重复数据处理正确")
        else:
            print("❌ Conversations 表出现重复数据")
            success = False
        
        if messages_diff == 0:
            print("✅ Messages 表重复数据处理正确")
        else:
            print("❌ Messages 表出现重复数据")
            success = False
        
        # 8. 检查唯一约束
        print("\n--- 检查唯一约束 ---")
        
        # 检查 messages 表的重复记录
        cursor.execute("""
            SELECT conversation_id, request_id, role, message_order, COUNT(*) as count
            FROM messages 
            GROUP BY conversation_id, request_id, role, message_order
            HAVING COUNT(*) > 1
            LIMIT 5
        """)
        
        duplicates = cursor.fetchall()
        if duplicates:
            print(f"❌ 发现 {len(duplicates)} 组重复的消息记录:")
            for dup in duplicates:
                print(f"  - conversation_id: {dup[0]}, request_id: {dup[1]}, role: {dup[2]}, count: {dup[4]}")
            success = False
        else:
            print("✅ 没有发现重复的消息记录")
        
        return success
        
    except Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except subprocess.TimeoutExpired:
        print("❌ 导入操作超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("\n🔌 数据库连接已关闭")

def show_recommendations():
    """显示建议"""
    print("\n📋 重复数据处理机制说明:")
    print("\n🔧 当前实现:")
    print("1. Projects 表: 使用 (workspace_id, platform) 唯一键，重复时更新项目信息")
    print("2. Conversations 表: 使用 id 主键，重复时更新对话信息")
    print("3. Messages 表: 使用 (conversation_id, request_id, role, message_order) 唯一键，重复时更新消息内容")
    
    print("\n✅ 优势:")
    print("- 支持增量导入，不会产生重复数据")
    print("- 可以更新已有数据（如最新的对话状态）")
    print("- 保持数据一致性")
    
    print("\n⚠️  注意事项:")
    print("- 首次运行可能需要较长时间建立唯一索引")
    print("- 如果消息内容发生变化，会更新为最新版本")
    print("- message_order 用于区分同一 request_id 下的用户消息和AI响应")

def main():
    """主函数"""
    print("数据库重复数据处理测试工具")
    print("=" * 50)
    
    # 运行测试
    success = test_duplicate_handling()
    
    if success:
        print("\n🎉 重复数据处理测试通过！")
        show_recommendations()
        return 0
    else:
        print("\n❌ 重复数据处理测试失败，请检查上述错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
