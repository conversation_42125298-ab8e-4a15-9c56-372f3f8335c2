# Extract Augment Chat - 可视化类关系图

## 🎨 UML 类图

```mermaid
classDiagram
    class DatabaseManager {
        -host: str
        -port: int
        -user: str
        -password: str
        -database: str
        -connection: mysql.connector.connection
        +__init__(host, port, user, password, database)
        +connect() void
        +create_tables() void
        +import_projects(projects) void
        +close() void
        -_insert_project(cursor, project) void
        -_import_project_conversations(cursor, project, stats) void
        -_log_import_results(project_count, stats) void
        -_import_conversation(cursor, project, conversation) int
        -_import_message(cursor, conversation_id, message, order) int
        -_insert_single_message(...) int
        -_parse_timestamp(timestamp_str) Optional[str]
    }

    class AugmentStorageLocator {
        -home: pathlib.Path
        -system: str
        +__init__()
        +get_vscode_root() pathlib.Path
        +get_workspace_storage_paths() List[tuple]
    }

    class AugmentDataExtractor {
        -db_path: pathlib.Path
        -workspace_id: str
        -augment_dir: pathlib.Path
        +__init__(db_path, workspace_id)
        +extract_project_info() Dict[str, str]
        +has_memories_file() Tuple[bool, str]
        +extract_augment_data() Optional[List[Dict]]
    }

    class AugmentProject {
        +name: str
        +path: str
        +workspace_id: str
        +conversations: List[Dict]
        +memories_content: str
        +__init__(name, path, workspace_id, conversations, memories_content)
        +to_dict() Dict[str, Any]
        +export_to_markdown(output_dir) List[str]
        -_get_display_name() str
        -_export_memories(project_dir, project_name) List[str]
        -_export_conversations(project_dir, project_name) List[str]
        -_generate_conversation_markdown(...) str
        -_group_messages(messages) List[List[Dict]]
        -_format_message_group(group, group_index) str
    }

    %% 关系定义
    DatabaseManager --> AugmentProject : imports
    AugmentStorageLocator --> AugmentDataExtractor : provides paths
    AugmentDataExtractor --> AugmentProject : creates
```

## 🔄 数据流程图

```mermaid
flowchart TD
    A[VS Code Storage] --> B[AugmentStorageLocator]
    B --> C[获取工作区路径]
    C --> D[AugmentDataExtractor]
    D --> E[提取项目信息]
    D --> F[提取会话数据]
    D --> G[检查记忆文件]
    E --> H[AugmentProject]
    F --> H
    G --> H
    H --> I{选择输出格式}
    I -->|JSON| J[save_to_json]
    I -->|Markdown| K[export_to_markdown]
    I -->|Database| L[DatabaseManager]
    J --> M[JSON 文件]
    K --> N[Markdown 文件]
    L --> O[MySQL 数据库]
```

## 🏗️ 模块依赖图

```mermaid
graph TB
    subgraph "主控制模块"
        Main[main函数]
        Parse[parse_arguments]
        HandleDB[handle_database_mode]
        HandleFile[handle_file_mode]
    end

    subgraph "核心业务类"
        DBMgr[DatabaseManager]
        Locator[AugmentStorageLocator]
        Extractor[AugmentDataExtractor]
        Project[AugmentProject]
    end

    subgraph "工具函数"
        Utils[工具函数集合]
        MDIndex[Markdown索引生成]
        JSONSave[JSON保存函数]
        DBSave[数据库保存函数]
    end

    subgraph "外部依赖"
        MySQL[(MySQL数据库)]
        VSCode[(VS Code存储)]
        Files[(文件系统)]
    end

    %% 依赖关系
    Main --> Parse
    Main --> HandleDB
    Main --> HandleFile
    
    HandleDB --> DBMgr
    HandleFile --> Project
    HandleFile --> MDIndex
    HandleFile --> JSONSave
    
    DBMgr --> MySQL
    Locator --> VSCode
    Extractor --> VSCode
    Project --> Files
    
    Main --> Locator
    Main --> Extractor
    Main --> Project
    
    Project --> Utils
    MDIndex --> Utils
    JSONSave --> Utils
    DBSave --> DBMgr
```

## 📊 方法调用关系图

```mermaid
sequenceDiagram
    participant Main as main()
    participant Locator as AugmentStorageLocator
    participant Extractor as AugmentDataExtractor
    participant Project as AugmentProject
    participant DBMgr as DatabaseManager

    Main->>Locator: get_workspace_storage_paths()
    Locator-->>Main: workspace_paths[]

    loop 每个工作区
        Main->>Extractor: extract_project_info()
        Main->>Extractor: extract_augment_data()
        Main->>Extractor: has_memories_file()
        Extractor-->>Main: project_data
        
        Main->>Project: new AugmentProject()
        Project-->>Main: project_instance
    end

    alt 数据库模式
        Main->>DBMgr: connect()
        Main->>DBMgr: create_tables()
        Main->>DBMgr: import_projects()
        Main->>DBMgr: close()
    else 文件模式
        Main->>Project: export_to_markdown()
        Project-->>Main: exported_files[]
    end
```

## 🎯 重构前后对比

### 重构前的问题
```mermaid
graph TD
    A[巨大的main函数] --> B[复杂的逻辑混合]
    B --> C[难以测试]
    B --> D[难以维护]
    B --> E[代码重复]
    
    F[长方法] --> G[职责不清]
    G --> H[耦合度高]
```

### 重构后的优势
```mermaid
graph TD
    A[模块化设计] --> B[职责分离]
    B --> C[易于测试]
    B --> D[易于维护]
    B --> E[代码复用]
    
    F[短方法] --> G[职责单一]
    G --> H[低耦合度]
    
    I[清晰的接口] --> J[可扩展性]
    J --> K[新功能易添加]
```

## 📈 代码质量指标

### 类复杂度分析
```mermaid
pie title 方法数量分布
    "DatabaseManager" : 12
    "AugmentProject" : 9
    "AugmentDataExtractor" : 3
    "AugmentStorageLocator" : 2
```

### 代码行数分布
```mermaid
pie title 代码行数分布
    "DatabaseManager" : 308
    "AugmentProject" : 133
    "工具函数" : 168
    "主函数模块" : 119
    "其他类" : 333
```

## 🔍 设计模式应用

### 1. 单一职责原则 (SRP)
- ✅ 每个类只负责一个核心功能
- ✅ 方法职责明确，易于理解

### 2. 开闭原则 (OCP)
- ✅ 易于扩展新的导出格式
- ✅ 易于添加新的数据源

### 3. 依赖倒置原则 (DIP)
- ✅ 高层模块不依赖低层模块
- ✅ 通过接口进行交互

### 4. 接口隔离原则 (ISP)
- ✅ 接口设计精简，职责单一
- ✅ 客户端只依赖需要的接口

## 🚀 扩展性设计

### 支持的扩展点
1. **新的导出格式**: 只需添加新的处理函数
2. **新的数据源**: 只需实现新的 Extractor 类
3. **新的存储后端**: 只需实现新的 Manager 类
4. **自定义处理逻辑**: 通过工具函数扩展

### 未来可能的扩展
- 支持其他 AI 编程助手
- 支持增量数据同步
- 支持数据分析和统计
- 支持 Web 界面展示
