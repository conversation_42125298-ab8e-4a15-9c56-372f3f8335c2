#!/usr/bin/env python3
"""
test_password_encoding.py - 测试密码 URL 编码

验证包含特殊字符的密码是否正确编码
"""

from urllib.parse import quote_plus

def test_password_encoding():
    """测试密码编码"""
    
    # 测试密码
    password = "chip@2024"
    user = "chip"
    host = "rm-2zeci3z6ogyl025l59o.mysql.rds.aliyuncs.com"
    port = 3306
    database = "chat_history"
    
    print("原始密码:", password)
    print("原始用户名:", user)
    
    # URL 编码
    encoded_user = quote_plus(user)
    encoded_password = quote_plus(password)
    
    print("编码后用户名:", encoded_user)
    print("编码后密码:", encoded_password)
    
    # 构建 URL
    db_url = f"mysql+pymysql://{encoded_user}:{encoded_password}@{host}:{port}"
    full_db_url = f"{db_url}/{database}?charset=utf8mb4"
    
    print("\n数据库连接 URL:")
    print("基础 URL:", db_url)
    print("完整 URL:", full_db_url)
    
    # 验证编码结果
    expected_encoded_password = "chip%402024"  # @ 应该被编码为 %40
    
    if encoded_password == expected_encoded_password:
        print("\n✅ 密码编码正确!")
        print(f"@ 符号已正确编码为 %40")
    else:
        print(f"\n❌ 密码编码可能有问题")
        print(f"期望: {expected_encoded_password}")
        print(f"实际: {encoded_password}")
    
    return encoded_password == expected_encoded_password

def test_database_connection():
    """测试实际数据库连接"""
    try:
        from extract_augment_chat import DatabaseManager, SQLALCHEMY_AVAILABLE
        
        if not SQLALCHEMY_AVAILABLE:
            print("❌ SQLAlchemy 或 PyMySQL 未安装")
            return False
        
        print("\n正在测试数据库连接...")
        
        # 使用默认配置
        db_manager = DatabaseManager(
            host='rm-2zeci3z6ogyl025l59o.mysql.rds.aliyuncs.com',
            port=3306,
            user='chip',
            password='chip@2024',
            database='chat_history'
        )
        
        # 尝试连接
        db_manager.connect()
        print("✅ 数据库连接成功!")
        
        # 关闭连接
        db_manager.close()
        print("✅ 数据库连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

if __name__ == "__main__":
    print("测试密码 URL 编码")
    print("=" * 50)
    
    # 测试编码
    encoding_ok = test_password_encoding()
    
    # 测试连接
    if encoding_ok:
        connection_ok = test_database_connection()
        
        if connection_ok:
            print("\n🎉 所有测试通过!")
        else:
            print("\n⚠️ 编码正确但连接失败，请检查网络和数据库配置")
    else:
        print("\n❌ 编码测试失败")
